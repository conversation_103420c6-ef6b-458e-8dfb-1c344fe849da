<?php
declare(strict_types=1);

namespace app\common\core\traits;

use think\response\Json;

trait ResponseTrait
{
	/**
	 * 成功响应
	 *
	 * @param string     $message 响应消息
	 * @param mixed|null $data    响应数据
	 * @param int        $code    响应状态码
	 */
	protected function success(string $message = 'success', mixed $data = null, int $code = 1): Json
	{
		return $this->result($data, $code, $message);
	}
	
	/**
	 * 错误响应
	 *
	 * @param string $message 错误消息
	 * @param int    $code    错误状态码
	 * @param mixed  $data    错误数据
	 */
	protected function error(string $message = 'error', int $code = 0, $data = null): <PERSON><PERSON>
	{
		return $this->result($data, $code, $message);
	}
	
	/**
	 * 响应结果
	 *
	 * @param mixed  $data    响应数据
	 * @param int    $code    响应状态码
	 * @param string $message 响应消息
	 */
	protected function result(mixed $data, int $code, string $message): J<PERSON>
	{
		$result = [
			'code'    => $code,
			'message' => $message,
			'data'    => $data,
			'time'    => time(),
		];
		
		return json($result);
	}
	
} 
-- 权限统一SQL脚本
-- 生成时间: 2025-08-01 17:34:24
-- 更新数量: 59 个权限
-- 目标格式: module:controller:action

-- 开始事务
START TRANSACTION;

-- 权限名称统一更新
UPDATE system_menu SET name = 'system:article:index' WHERE id = 2582; -- article -> system:article:index
UPDATE system_menu SET name = 'crm:index:index' WHERE id = 189; -- crm:index -> crm:index:index
UPDATE system_menu SET name = 'crm:product:index' WHERE id = 210; -- crm:product -> crm:product:index
UPDATE system_menu SET name = 'crm:work_report/index:index' WHERE id = 2581; -- crm:work_report/index -> crm:work_report/index:index
UPDATE system_menu SET name = 'system:log:index' WHERE id = 4; -- log -> system:log:index
UPDATE system_menu SET name = 'system:message:index' WHERE id = 140; -- message -> system:message:index
UPDATE system_menu SET name = 'system:notice:index' WHERE id = 127; -- notice -> system:notice:index
UPDATE system_menu SET name = 'system:office:index' WHERE id = 77; -- office -> system:office:index
UPDATE system_menu SET name = 'office:attendance:index' WHERE id = 101; -- office:attendance -> office:attendance:index
UPDATE system_menu SET name = 'office:console:index' WHERE id = 78; -- office:console -> office:console:index
UPDATE system_menu SET name = 'office:workflow:index' WHERE id = 79; -- office:workflow -> office:workflow:index
UPDATE system_menu SET name = 'system:project:index' WHERE id = 2440; -- project -> system:project:index
UPDATE system_menu SET name = 'project:task_comment:add' WHERE id = 2585; -- project:task:comment:add -> project:task_comment:add
UPDATE system_menu SET name = 'project:task_comment:delete' WHERE id = 2587; -- project:task:comment:delete -> project:task_comment:delete
UPDATE system_menu SET name = 'project:task_comment:edit' WHERE id = 2586; -- project:task:comment:edit -> project:task_comment:edit
UPDATE system_menu SET name = 'project:task_follow:add' WHERE id = 2588; -- project:task:follow:add -> project:task_follow:add
UPDATE system_menu SET name = 'project:task_follow:delete' WHERE id = 2590; -- project:task:follow:delete -> project:task_follow:delete
UPDATE system_menu SET name = 'project:task_follow:edit' WHERE id = 2589; -- project:task:follow:edit -> project:task_follow:edit
UPDATE system_menu SET name = 'system:system:index' WHERE id = 34; -- system -> system:system:index
UPDATE system_menu SET name = 'system:ims:index' WHERE id = 93; -- system:ims -> system:ims:index
UPDATE system_menu SET name = 'system:ims_supplier:index' WHERE id = 97; -- system:ims:supplier:index -> system:ims_supplier:index
UPDATE system_menu SET name = 'system:log_login:delete' WHERE id = 28; -- system:log:login:delete -> system:log_login:delete
UPDATE system_menu SET name = 'system:log_login:detail' WHERE id = 44; -- system:log:login:detail -> system:log_login:detail
UPDATE system_menu SET name = 'system:log_login:index' WHERE id = 5; -- system:log:login:index -> system:log_login:index
UPDATE system_menu SET name = 'system:log_operation:delete' WHERE id = 30; -- system:log:operation:delete -> system:log_operation:delete
UPDATE system_menu SET name = 'system:log_operation:detail' WHERE id = 46; -- system:log:operation:detail -> system:log_operation:detail
UPDATE system_menu SET name = 'system:log_operation:index' WHERE id = 29; -- system:log:operation:index -> system:log_operation:index
UPDATE system_menu SET name = 'system:permission_admin:add' WHERE id = 16; -- system:permission:admin:add -> system:permission_admin:add
UPDATE system_menu SET name = 'system:permission_admin:delete' WHERE id = 18; -- system:permission:admin:delete -> system:permission_admin:delete
UPDATE system_menu SET name = 'system:permission_admin:detail' WHERE id = 38; -- system:permission:admin:detail -> system:permission_admin:detail
UPDATE system_menu SET name = 'system:permission_admin:edit' WHERE id = 17; -- system:permission:admin:edit -> system:permission_admin:edit
UPDATE system_menu SET name = 'system:permission_admin:index' WHERE id = 3; -- system:permission:admin:index -> system:permission_admin:index
UPDATE system_menu SET name = 'system:permission_admin:reset_password' WHERE id = 31; -- system:permission:admin:reset_password -> system:permission_admin:reset_password
UPDATE system_menu SET name = 'system:permission_department:add' WHERE id = 22; -- system:permission:department:add -> system:permission_department:add
UPDATE system_menu SET name = 'system:permission_department:delete' WHERE id = 24; -- system:permission:department:delete -> system:permission_department:delete
UPDATE system_menu SET name = 'system:permission_department:detail' WHERE id = 40; -- system:permission:department:detail -> system:permission_department:detail
UPDATE system_menu SET name = 'system:permission_department:edit' WHERE id = 23; -- system:permission:department:edit -> system:permission_department:edit
UPDATE system_menu SET name = 'system:permission_department:index' WHERE id = 7; -- system:permission:department:index -> system:permission_department:index
UPDATE system_menu SET name = 'system:permission_menu:add' WHERE id = 13; -- system:permission:menu:add -> system:permission_menu:add
UPDATE system_menu SET name = 'system:permission_menu:delete' WHERE id = 15; -- system:permission:menu:delete -> system:permission_menu:delete
UPDATE system_menu SET name = 'system:permission_menu:detail' WHERE id = 33; -- system:permission:menu:detail -> system:permission_menu:detail
UPDATE system_menu SET name = 'system:permission_menu:edit' WHERE id = 14; -- system:permission:menu:edit -> system:permission_menu:edit
UPDATE system_menu SET name = 'system:permission_menu:index' WHERE id = 2; -- system:permission:menu:index -> system:permission_menu:index
UPDATE system_menu SET name = 'system:permission_post:add' WHERE id = 25; -- system:permission:post:add -> system:permission_post:add
UPDATE system_menu SET name = 'system:permission_post:delete' WHERE id = 27; -- system:permission:post:delete -> system:permission_post:delete
UPDATE system_menu SET name = 'system:permission_post:detail' WHERE id = 42; -- system:permission:post:detail -> system:permission_post:detail
UPDATE system_menu SET name = 'system:permission_post:edit' WHERE id = 26; -- system:permission:post:edit -> system:permission_post:edit
UPDATE system_menu SET name = 'system:permission_post:index' WHERE id = 8; -- system:permission:post:index -> system:permission_post:index
UPDATE system_menu SET name = 'system:permission_role:add' WHERE id = 19; -- system:permission:role:add -> system:permission_role:add
UPDATE system_menu SET name = 'system:permission_role:delete' WHERE id = 21; -- system:permission:role:delete -> system:permission_role:delete
UPDATE system_menu SET name = 'system:permission_role:detail' WHERE id = 36; -- system:permission:role:detail -> system:permission_role:detail
UPDATE system_menu SET name = 'system:permission_role:edit' WHERE id = 20; -- system:permission:role:edit -> system:permission_role:edit
UPDATE system_menu SET name = 'system:permission_role:index' WHERE id = 6; -- system:permission:role:index -> system:permission_role:index
UPDATE system_menu SET name = 'system:tenant:index' WHERE id = 59; -- system:tenant -> system:tenant:index
UPDATE system_menu SET name = 'system:tenant_config:detail' WHERE id = 233; -- system:tenant:config:detail -> system:tenant_config:detail
UPDATE system_menu SET name = 'system:tenant_config:save' WHERE id = 2332; -- system:tenant:config:save -> system:tenant_config:save
UPDATE system_menu SET name = 'system:user_attendance_data:daily' WHERE id = 104; -- system:user:attendance_data:daily -> system:user_attendance_data:daily
UPDATE system_menu SET name = 'system:UserCenter:index' WHERE id = 49; -- UserCenter -> system:UserCenter:index
UPDATE system_menu SET name = 'system:workflow:index' WHERE id = 122; -- workflow -> system:workflow:index

-- 提交事务
COMMIT;

-- 如有问题，可以回滚：ROLLBACK;

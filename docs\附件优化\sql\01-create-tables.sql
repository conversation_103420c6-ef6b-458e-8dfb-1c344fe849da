-- =====================================================
-- 附件系统优化 - 数据库表结构创建脚本
-- 版本: v1.0
-- 创建日期: 2025-01-31
-- 说明: 创建新的附件系统表结构，实现文件去重和权限隔离
-- =====================================================

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 备份现有表结构（如果需要）
-- =====================================================
-- 如果需要保留现有数据，请先备份
-- CREATE TABLE system_attachment_backup AS SELECT * FROM system_attachment;

-- =====================================================
-- 2. 删除现有表（谨慎操作）
-- =====================================================
-- 注意：这将删除现有的附件表，请确保已备份数据
DROP TABLE IF EXISTS `system_attachment_user`;
DROP TABLE IF EXISTS `system_attachment`;

-- =====================================================
-- 3. 创建新的物理文件表
-- =====================================================
CREATE TABLE `system_attachment` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `name` varchar(100) NOT NULL COMMENT '系统生成的文件名',
    `path` varchar(255) NOT NULL COMMENT '文件路径',
    `extension` varchar(10) DEFAULT NULL COMMENT '文件扩展名',
    `size` bigint(20) NOT NULL DEFAULT 0 COMMENT '文件大小(字节)',
    `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
    `storage` varchar(20) NOT NULL DEFAULT 'local' COMMENT '存储方式：local,qnoss,alioss,txoss',
    `storage_id` varchar(100) DEFAULT NULL COMMENT '存储平台ID(etag/hash等)',
    `file_md5` varchar(32) NOT NULL COMMENT '文件MD5值',
    `file_hash` varchar(64) DEFAULT NULL COMMENT '文件SHA256值(备用)',
    `ref_count` int(11) NOT NULL DEFAULT 0 COMMENT '引用计数',
    `storage_meta` json DEFAULT NULL COMMENT '存储平台元数据',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='附件文件表';

-- =====================================================
-- 4. 创建用户文件关联表
-- =====================================================
CREATE TABLE `system_attachment_user` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `attachment_id` bigint(20) UNSIGNED NOT NULL COMMENT '附件ID',
    `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户分类ID',
    `display_name` varchar(255) NOT NULL COMMENT '用户自定义显示名称',
    `original_name` varchar(255) NOT NULL COMMENT '原始上传文件名',
    `upload_ip` varchar(45) DEFAULT NULL COMMENT '上传IP',
    `upload_source` varchar(50) DEFAULT 'web' COMMENT '上传来源：web,api,mobile',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户文件关联表';

-- =====================================================
-- 5. 创建附件使用统计表（可选）
-- =====================================================
CREATE TABLE `system_attachment_stats` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
    `date` date NOT NULL COMMENT '统计日期',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `total_files` int(11) NOT NULL DEFAULT 0 COMMENT '总文件数',
    `total_size` bigint(20) NOT NULL DEFAULT 0 COMMENT '总大小(字节)',
    `unique_files` int(11) NOT NULL DEFAULT 0 COMMENT '去重后文件数',
    `unique_size` bigint(20) NOT NULL DEFAULT 0 COMMENT '去重后大小(字节)',
    `dedup_ratio` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '去重率(%)',
    `upload_count` int(11) NOT NULL DEFAULT 0 COMMENT '当日上传数',
    `delete_count` int(11) NOT NULL DEFAULT 0 COMMENT '当日删除数',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='附件使用统计表';

-- =====================================================
-- 6. 添加表注释和字段注释
-- =====================================================
ALTER TABLE `system_attachment_new` COMMENT = '附件文件表 - 存储物理文件信息，支持去重';
ALTER TABLE `system_attachment_user` COMMENT = '用户文件关联表 - 管理用户文件权限和显示信息';
ALTER TABLE `system_attachment_stats` COMMENT = '附件使用统计表 - 记录文件使用情况和去重效果';

-- =====================================================
-- 7. 验证表结构
-- =====================================================
-- 查看表结构
SHOW CREATE TABLE `system_attachment`;
SHOW CREATE TABLE `system_attachment_user`;
SHOW CREATE TABLE `system_attachment_stats`;

-- 查看表信息
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME IN ('system_attachment', 'system_attachment_user', 'system_attachment_stats');

-- =====================================================
-- 8. 创建示例数据（可选，用于测试）
-- =====================================================
-- 插入示例物理文件
INSERT INTO `system_attachment` (
    `name`, `path`, `extension`, `size`, `mime_type`, 
    `storage`, `storage_id`, `file_md5`, `ref_count`
) VALUES 
(
    'example_file_001.jpg',
    '/uploads/2025/01/31/example_file_001.jpg',
    'jpg',
    1024000,
    'image/jpeg',
    'local',
    '',
    'abc123def456ghi789jkl012mno345pq',
    0
),
(
    'example_doc_001.pdf',
    '/uploads/2025/01/31/example_doc_001.pdf',
    'pdf',
    2048000,
    'application/pdf',
    'local',
    '',
    'xyz789abc123def456ghi012jkl345mn',
    0
);

-- 插入示例用户关联（假设用户ID为1，租户ID为0）
INSERT INTO `system_attachment_user` (
    `attachment_id`, `user_id`, `tenant_id`, `cate_id`,
    `display_name`, `original_name`, `upload_source`, `creator_id`
) VALUES 
(
    1, 1, 0, 0,
    '示例图片.jpg',
    'example_image.jpg',
    'web',
    1
),
(
    2, 1, 0, 0,
    '示例文档.pdf',
    'example_document.pdf',
    'web',
    1
);

-- 更新引用计数
UPDATE `system_attachment` SET `ref_count` = 1 WHERE `id` IN (1, 2);

-- =====================================================
-- 9. 设置外键检查
-- =====================================================
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 10. 执行完成提示
-- =====================================================
SELECT '附件系统表结构创建完成！' as message;
SELECT '请继续执行 02-create-indexes.sql 创建索引' as next_step;

-- =====================================================
-- 表结构创建完成
-- 
-- 创建的表：
-- 1. system_attachment - 物理文件表
-- 2. system_attachment_user - 用户文件关联表  
-- 3. system_attachment_stats - 使用统计表
--
-- 下一步：
-- 1. 执行 02-create-indexes.sql 创建索引
-- 2. 如需迁移数据，执行 03-migration-scripts.sql
-- 3. 更新应用代码以使用新表结构
-- =====================================================

<?php
/**
 * 重新整理权限菜单ID顺序 v3
 * 使用临时大数值避免unsigned字段的负数问题
 */

require_once 'vendor/autoload.php';

echo "=== 重新整理权限菜单ID顺序 v3 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取当前权限菜单:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type, status
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $allMenus = $stmt->fetchAll();
    
    echo "  当前权限总数: " . count($allMenus) . " 个\n\n";
    
    echo "2. 按模块分组并分配新的ID:\n";
    
    // 按模块分组
    $moduleGroups = [];
    foreach ($allMenus as $menu) {
        $parts = explode(':', $menu['name']);
        $module = $parts[0] ?? 'unknown';
        
        if (!isset($moduleGroups[$module])) {
            $moduleGroups[$module] = [];
        }
        $moduleGroups[$module][] = $menu;
    }
    
    // 定义新的ID分配 (从小数字开始，按模块顺序)
    $newIdCounter = 1;
    $reorderMappings = [];
    
    // 模块处理顺序
    $moduleOrder = ['system', 'crm', 'project', 'workflow', 'notice', 'office', 'daily', 'ims'];
    
    foreach ($moduleOrder as $module) {
        if (!isset($moduleGroups[$module])) {
            continue;
        }
        
        $menus = $moduleGroups[$module];
        
        // 按权限名称排序，确保逻辑顺序
        usort($menus, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        $startId = $newIdCounter;
        
        foreach ($menus as $menu) {
            $reorderMappings[] = [
                'old_id' => $menu['id'],
                'new_id' => $newIdCounter,
                'name' => $menu['name'],
                'title' => $menu['title'],
                'module' => $module
            ];
            $newIdCounter++;
        }
        
        $endId = $newIdCounter - 1;
        echo "  {$module}模块: " . count($menus) . "个权限，新ID范围 {$startId}-{$endId}\n";
    }
    
    echo "\n  总共需要重新分配: " . count($reorderMappings) . " 个权限\n\n";
    
    echo "3. 开始重新分配ID:\n";
    
    if (empty($reorderMappings)) {
        echo "  ✅ 没有需要重新分配的权限\n";
        return;
    }
    
    $pdo->beginTransaction();
    
    try {
        // 使用临时大数值范围 (100000+) 避免冲突
        $tempIdBase = 100000;
        
        // 第一步：将所有ID临时设置为大数值
        echo "  第一步：设置临时ID...\n";
        foreach ($reorderMappings as $index => $mapping) {
            $tempId = $tempIdBase + $index;
            $stmt = $pdo->prepare("UPDATE system_menu SET id = ? WHERE id = ?");
            $stmt->execute([$tempId, $mapping['old_id']]);
            
            // 同时更新关联表
            $stmt = $pdo->prepare("UPDATE system_role_menu SET menu_id = ? WHERE menu_id = ?");
            $stmt->execute([$tempId, $mapping['old_id']]);
        }
        
        // 第二步：设置为最终的新ID
        echo "  第二步：设置最终ID...\n";
        $successCount = 0;
        foreach ($reorderMappings as $index => $mapping) {
            $tempId = $tempIdBase + $index;
            
            // 更新菜单表
            $stmt = $pdo->prepare("UPDATE system_menu SET id = ? WHERE id = ?");
            $stmt->execute([$mapping['new_id'], $tempId]);
            
            // 更新关联表
            $stmt = $pdo->prepare("UPDATE system_role_menu SET menu_id = ? WHERE menu_id = ?");
            $stmt->execute([$mapping['new_id'], $tempId]);
            
            if ($successCount < 10) { // 只显示前10个
                echo "    ✅ ID:{$mapping['old_id']} -> {$mapping['new_id']} {$mapping['name']}\n";
            } elseif ($successCount == 10) {
                echo "    ... 继续处理剩余权限\n";
            }
            $successCount++;
        }
        
        // 重置自增ID
        $maxNewId = max(array_column($reorderMappings, 'new_id'));
        $nextAutoIncrement = $maxNewId + 1;
        $stmt = $pdo->prepare("ALTER TABLE system_menu AUTO_INCREMENT = ?");
        $stmt->execute([$nextAutoIncrement]);
        echo "    ✅ 重置自增ID为: {$nextAutoIncrement}\n";
        
        $pdo->commit();
        
        echo "\n  ✅ 成功重新分配 {$successCount} 个权限ID\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
    echo "\n4. 验证重新分配结果:\n";
    
    // 显示每个模块的ID范围
    $currentId = 1;
    foreach ($moduleOrder as $module) {
        if (!isset($moduleGroups[$module])) continue;
        
        $count = count($moduleGroups[$module]);
        $endId = $currentId + $count - 1;
        
        echo "  {$module}模块: {$count}个权限，ID范围 {$currentId}-{$endId}\n";
        $currentId += $count;
    }
    
    echo "\n5. 显示重新排序后的权限 (每个模块前3个):\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $reorderedMenus = $stmt->fetchAll();
    
    $currentModule = '';
    $moduleCount = 0;
    
    foreach ($reorderedMenus as $menu) {
        $parts = explode(':', $menu['name']);
        $module = $parts[0] ?? 'unknown';
        
        if ($module !== $currentModule) {
            $currentModule = $module;
            $moduleCount = 0;
            echo "\n  {$module}模块:\n";
        }
        
        if ($moduleCount < 3) {
            echo "    ID:{$menu['id']} {$menu['name']} ({$menu['title']})\n";
        } elseif ($moduleCount == 3) {
            $totalInModule = count(array_filter($reorderedMenus, function($m) use ($module) {
                return strpos($m['name'], $module . ':') === 0;
            }));
            echo "    ... 还有 " . ($totalInModule - 3) . " 个权限\n";
        }
        
        $moduleCount++;
    }
    
    echo "\n🎉 权限菜单ID重新排序完成！\n";
    echo "  现在所有权限都按照模块逻辑顺序排列：\n";
    echo "  1. system (系统管理)\n";
    echo "  2. crm (客户管理)\n";
    echo "  3. project (项目管理)\n";
    echo "  4. workflow (工作流)\n";
    echo "  5. notice (通知)\n";
    echo "  6. office (办公)\n";
    echo "  7. daily (每日报价)\n";
    echo "  8. ims (库存管理)\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 菜单重新排序完成 ===\n";

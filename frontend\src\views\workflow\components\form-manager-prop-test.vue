<template>
  <div class="prop-test-container">
    <h2>FormManager Props 测试</h2>
    
    <el-card>
      <h3>测试场景</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>场景1: 最小化 Props（应该不报错）</h4>
          <el-button @click="testMinimalProps" type="primary">
            测试最小化 Props
          </el-button>
          <div v-if="minimalPropsResult" class="test-result">
            <el-tag :type="minimalPropsResult.success ? 'success' : 'danger'">
              {{ minimalPropsResult.message }}
            </el-tag>
          </div>
        </el-col>
        
        <el-col :span="12">
          <h4>场景2: 完整 Props</h4>
          <el-button @click="testFullProps" type="success">
            测试完整 Props
          </el-button>
          <div v-if="fullPropsResult" class="test-result">
            <el-tag :type="fullPropsResult.success ? 'success' : 'danger'">
              {{ fullPropsResult.message }}
            </el-tag>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-card v-if="showMinimalTest">
      <h3>最小化 Props 测试</h3>
      <FormManager
        v-model="minimalTest.visible"
        ref="minimalFormRef"
      />
    </el-card>

    <el-card v-if="showFullTest">
      <h3>完整 Props 测试</h3>
      <FormManager
        v-model="fullTest.visible"
        :type="fullTest.type"
        :formId="fullTest.formId"
        :workflowTypeId="fullTest.workflowTypeId"
        @success="handleSuccess"
        @cancel="handleCancel"
        ref="fullFormRef"
      />
    </el-card>

    <el-card>
      <h3>Props 默认值验证</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="type 默认值">
          {{ propsDefaults.type }}
        </el-descriptions-item>
        <el-descriptions-item label="formId 默认值">
          {{ propsDefaults.formId }}
        </el-descriptions-item>
        <el-descriptions-item label="modelValue 默认值">
          {{ propsDefaults.modelValue }}
        </el-descriptions-item>
        <el-descriptions-item label="workflowTypeId 默认值">
          {{ propsDefaults.workflowTypeId }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import FormManager from './form-manager.vue'

// 测试状态
const showMinimalTest = ref(false)
const showFullTest = ref(false)

// 测试结果
const minimalPropsResult = ref<{success: boolean, message: string} | null>(null)
const fullPropsResult = ref<{success: boolean, message: string} | null>(null)

// 测试数据
const minimalTest = reactive({
  visible: false
})

const fullTest = reactive({
  visible: false,
  type: 'leave',
  formId: 123,
  workflowTypeId: 1
})

// Props 默认值
const propsDefaults = reactive({
  type: '""',
  formId: '0',
  modelValue: 'false',
  workflowTypeId: '0'
})

// 组件引用
const minimalFormRef = ref()
const fullFormRef = ref()

// 测试最小化 Props
const testMinimalProps = () => {
  try {
    showMinimalTest.value = true
    minimalTest.visible = true
    
    setTimeout(() => {
      minimalTest.visible = false
      minimalPropsResult.value = {
        success: true,
        message: '✅ 最小化 Props 测试通过，无警告'
      }
    }, 1000)
    
  } catch (error) {
    minimalPropsResult.value = {
      success: false,
      message: `❌ 最小化 Props 测试失败: ${error}`
    }
  }
}

// 测试完整 Props
const testFullProps = () => {
  try {
    showFullTest.value = true
    fullTest.visible = true
    
    setTimeout(() => {
      fullTest.visible = false
      fullPropsResult.value = {
        success: true,
        message: '✅ 完整 Props 测试通过'
      }
    }, 1000)
    
  } catch (error) {
    fullPropsResult.value = {
      success: false,
      message: `❌ 完整 Props 测试失败: ${error}`
    }
  }
}

// 成功回调
const handleSuccess = (data: any) => {
  console.log('表单操作成功:', data)
  ElMessage.success('表单操作成功')
}

// 取消回调
const handleCancel = () => {
  console.log('表单取消')
  fullTest.visible = false
}

// 监听控制台警告
onMounted(() => {
  // 重写 console.warn 来捕获 Vue 警告
  const originalWarn = console.warn
  console.warn = (...args) => {
    const message = args.join(' ')
    if (message.includes('Missing required prop')) {
      if (minimalPropsResult.value === null) {
        minimalPropsResult.value = {
          success: false,
          message: '❌ 检测到必需 prop 警告'
        }
      }
    }
    originalWarn.apply(console, args)
  }
  
  // 组件卸载时恢复原始 console.warn
  onUnmounted(() => {
    console.warn = originalWarn
  })
})
</script>

<style scoped>
.prop-test-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.prop-test-container .el-card {
  margin-bottom: 20px;
}

.test-result {
  margin-top: 10px;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #409eff;
}

h3, h4 {
  margin-bottom: 15px;
  color: #303133;
}
</style>

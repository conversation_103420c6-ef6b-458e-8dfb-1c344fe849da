<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\system\model\AttachmentModel;
use app\system\model\AttachmentUserModel;
use think\facade\Db;

/**
 * 附件服务类 - 重构版本
 * 支持文件去重和用户权限隔离
 */
class AttachmentService extends BaseService
{
	
	protected function __construct()
	{
		$this->model = new AttachmentUserModel(); // 主要操作用户文件关联表
		parent::__construct();
	}
	
	/**
	 * 获取用户文件列表
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getList(array $params): array
	{
		// 使用模型的静态方法获取用户文件列表
		return AttachmentUserModel::getUserFiles($params);
	}

	/**
	 * 获取所有文件列表（管理员专用）
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getAllList(array $params): array
	{
		// 使用模型的静态方法获取所有文件列表
		return AttachmentUserModel::getAllFiles($params);
	}
	
	/**
	 * 获取用户文件详情
	 *
	 * @param int $id 用户文件关联ID
	 * @return array
	 */
	public function getDetail(int $id): array
	{
		// 获取用户文件信息（包含物理文件信息）
		$userFile = AttachmentUserModel::alias('au')
		                               ->join('system_attachment a', 'au.attachment_id = a.id')
		                               ->where('au.id', $id)
		                               ->field([
			                               'au.*',
			                               'a.name',
			                               'a.path',
			                               'a.size',
			                               'a.extension',
			                               'a.mime_type',
			                               'a.storage',
			                               'a.storage_id',
			                               'a.ref_count',
			                               'a.storage_meta'
		                               ])
		                               ->find();
		
		if (!$userFile) {
			throw new BusinessException('文件不存在');
		}
		
		// 添加文件URL
		$userFile['url'] = getImgUrl($userFile['path']);
		
		return $userFile->toArray();
	}
	
	/**
	 * 删除用户文件
	 *
	 * @param int|array $id 用户文件关联ID
	 * @return bool
	 */
	public function delete(int|array $id): bool
	{
		
		$ids = is_array($id)
			? $id
			: [$id];
		
		$userId = request()->adminId;
		foreach ($ids as $fileId) {
			$this->deleteUserFile($fileId, $userId);
		}
		return true;
	}
	
	/**
	 * 删除单个用户文件
	 *
	 * @param int $fileId 用户文件关联ID
	 * @param int $userId 用户ID
	 * @return void
	 */
	private function deleteUserFile(int $fileId, int $userId): void
	{
		
		// 查找用户文件记录
		$userFile = AttachmentUserModel::where('id', $fileId)
		                               ->where('user_id', $userId)
		                               ->find();
		
		if (!$userFile) {
			throw new BusinessException('文件不存在');
		}
		
		$attachmentId = $userFile->attachment_id;
		
		// 删除用户关联记录
		$userFile->delete();
		
		// 减少物理文件引用计数
		$attachment = AttachmentModel::find($attachmentId);
		if ($attachment) {
			$attachment->decrementRefCount();
			
			// 检查是否需要删除物理文件
			if ($attachment->canDelete()) {
				$this->deletePhysicalFile($attachment);
			}
		}
		
	}
	
	/**
	 * 删除物理文件
	 *
	 * @param AttachmentModel $attachment 物理文件记录
	 * @return void
	 */
	private function deletePhysicalFile(AttachmentModel $attachment): void
	{
		try {
			// 删除物理文件
			if (file_exists(public_path() . $attachment->path)) {
				unlink(public_path() . $attachment->path);
			}
			
			// 删除数据库记录
			$attachment->delete();
		}
		catch (\Exception $e) {
			// 记录日志但不抛出异常，避免影响用户操作
			trace('删除物理文件失败: ' . $e->getMessage(), 'error');
		}
	}
	
	/**
	 * 文件上传（本地存储）
	 *
	 * @param array  $file    文件信息
	 * @param string $storage 存储方式
	 * @param int    $cateId  分类ID
	 * @return array
	 */
	public function uploadFile(array $file, string $storage = 'local', int $cateId = 0): array
	{
		$userId = request()->adminId;
		return Db::transaction(function () use ($file, $storage, $cateId, $userId) {
			// 计算文件MD5
			$fileMd5 = md5_file($file['tmp_name']);
			
			// 查找是否存在相同文件
			$existingAttachment = AttachmentModel::findByMd5($storage, $fileMd5);
			
			if ($existingAttachment) {
				// 文件已存在，创建用户关联
				$userFile = $this->createUserFileAssociation($existingAttachment, $file, $cateId, $userId);
				$existingAttachment->incrementRefCount();
				
				return [
					'id'            => $userFile->id,
					'attachment_id' => $existingAttachment->id,
					'name'          => $existingAttachment->name,
					'display_name'  => $userFile->display_name,
					'path'          => $existingAttachment->path,
					'url'           => getImgUrl($existingAttachment->path),
					'size'          => $existingAttachment->size,
					'extension'     => $existingAttachment->extension,
					'mime_type'     => $existingAttachment->mime_type,
					'is_duplicate'  => true,
					'upload_time'   => $userFile->created_at
				];
			}
			else {
				// 文件不存在，上传新文件
				return $this->uploadNewFile($file, $storage, $cateId, $userId, $fileMd5);
			}
		});
	}
	
	/**
	 * 上传新文件
	 *
	 * @param array  $file    文件信息
	 * @param string $storage 存储方式
	 * @param int    $cateId  分类ID
	 * @param int    $userId  用户ID
	 * @param string $fileMd5 文件MD5
	 * @return array
	 */
	private function uploadNewFile(
		array $file, string $storage, int $cateId, int $userId, string $fileMd5
	): array
	{
		// 这里需要调用具体的存储驱动上传文件
		// 暂时使用简化的本地上传逻辑
		$uploadPath = '/uploads/' . date('Y/m/d/');
		$fileName   = uniqid() . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
		$fullPath   = public_path() . $uploadPath . $fileName;
		
		// 确保目录存在
		$dir = dirname($fullPath);
		if (!is_dir($dir)) {
			mkdir($dir, 0755, true);
		}
		
		// 移动文件（兼容测试环境和实际上传）
		if (is_uploaded_file($file['tmp_name'])) {
			// 真实的HTTP上传文件
			if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
				throw new BusinessException('文件上传失败');
			}
		}
		else {
			// 测试环境或其他方式的文件
			if (!copy($file['tmp_name'], $fullPath)) {
				throw new BusinessException('文件复制失败');
			}
		}
		
		// 创建物理文件记录
		$attachment = AttachmentModel::create([
			'name'      => $fileName,
			'path'      => $uploadPath . $fileName,
			'extension' => pathinfo($file['name'], PATHINFO_EXTENSION),
			'size'      => filesize($fullPath),
			'mime_type' => $file['type'] ?? '',
			'storage'   => $storage,
			'file_md5'  => $fileMd5,
			'ref_count' => 1
		]);
		
		// 创建用户关联记录
		$userFile = $this->createUserFileAssociation($attachment, $file, $cateId, $userId);
		
		return [
			'id'            => $userFile->id,
			'attachment_id' => $attachment->id,
			'name'          => $attachment->name,
			'display_name'  => $userFile->display_name,
			'path'          => $attachment->path,
			'url'           => getImgUrl($attachment->path),
			'size'          => $attachment->size,
			'extension'     => $attachment->extension,
			'mime_type'     => $attachment->mime_type,
			'is_duplicate'  => false,
			'upload_time'   => $userFile->created_at
		];
	}
	
	/**
	 * 创建用户文件关联
	 *
	 * @param AttachmentModel $attachment 物理文件
	 * @param array           $file       原始文件信息
	 * @param int             $cateId     分类ID
	 * @param int             $userId     用户ID
	 * @return AttachmentUserModel
	 */
	private function createUserFileAssociation(
		AttachmentModel $attachment, array $file, int $cateId, int $userId
	): AttachmentUserModel
	{
		return AttachmentUserModel::createUserFileAssociation([
			'attachment_id' => $attachment->id,
			'user_id'       => $userId,
			'cate_id'       => $cateId,
			'display_name'  => $file['name'],
			'original_name' => $file['name'],
			'upload_source' => 'web',
			'creator_id'    => $userId
		]);
	}
	
	/**
	 * 获取用户文件统计
	 *
	 * @return array
	 */
	public function getUserStats(): array
	{
		return AttachmentUserModel::getUserFileStats();
	}

	/**
	 * 获取所有文件统计（管理员专用）
	 *
	 * @return array
	 */
	public function getAllStats(): array
	{
		return AttachmentUserModel::getAllFileStats();
	}
	
	/**
	 * 批量删除用户文件
	 *
	 * @param array $fileIds 文件ID数组
	 * @return array
	 */
	public function batchDelete(array $fileIds): array
	{
		return AttachmentUserModel::batchDeleteUserFiles($fileIds, request()->adminId);
	}
	
	/**
	 * 移动文件到指定分类
	 *
	 * @param int $fileId 用户文件关联ID
	 * @param int $cateId 目标分类ID
	 * @return bool
	 */
	public function moveFile(int $fileId, int $cateId): bool
	{
		// 更新分类
		$userFile = AttachmentUserModel::find($fileId);
		if (!$userFile) {
			throw new BusinessException('文件不存在');
		}
		
		$userFile->cate_id = $cateId;
		return $userFile->save();
	}
	
	/**
	 * 重命名文件
	 *
	 * @param int    $fileId  用户文件关联ID
	 * @param string $newName 新的显示名称
	 * @return bool
	 */
	public function renameFile(int $fileId, string $newName): bool
	{
		
		// 验证文件名
		if (empty(trim($newName))) {
			throw new BusinessException('文件名不能为空');
		}
		// 更新显示名称
		$userFile = AttachmentUserModel::find($fileId);
		if (!$userFile) {
			throw new BusinessException('文件不存在');
		}
		
		$userFile->display_name = trim($newName);
		return $userFile->save();
	}
	
	/**
	 * 批量移动文件
	 *
	 * @param array $fileIds 文件ID数组
	 * @param int   $cateId  目标分类ID
	 * @return array
	 */
	public function batchMoveFiles(array $fileIds, int $cateId): array
	{
		
		$successCount = 0;
		$failedCount  = 0;
		$failedIds    = [];
		
		foreach ($fileIds as $fileId) {
			try {
				if ($this->moveFile($fileId, $cateId)) {
					$successCount++;
				}
				else {
					$failedCount++;
					$failedIds[] = $fileId;
				}
			}
			catch (\Exception $e) {
				$failedCount++;
				$failedIds[] = $fileId;
			}
		}
		
		return [
			'success_count' => $successCount,
			'failed_count'  => $failedCount,
			'failed_ids'    => $failedIds
		];
	}
	
	/**
	 * 复制文件（创建新的用户关联）
	 *
	 * @param int    $fileId       源文件ID
	 * @param int    $targetCateId 目标分类ID
	 * @param string $newName      新文件名（可选）
	 */
	public function copyFile(int $fileId, int $targetCateId, string $newName = ''): int
	{
		$userId = request()->adminId;
		
		return Db::transaction(function () use ($fileId, $targetCateId, $newName, $userId) {
			
			$attachmentUserModel = new AttachmentUserModel();
			// 获取源文件信息
			$sourceFile = $attachmentUserModel->find($fileId);
			if (!$sourceFile) {
				throw new BusinessException('源文件不存在');
			}
			
			// 创建新的用户关联
			$newFile = (new AttachmentUserModel())->saveByCreate([
				'attachment_id' => $sourceFile->attachment_id,
				'user_id'       => $userId,
				'cate_id'       => $targetCateId,
				'display_name'  => $newName
					?: $sourceFile->display_name,
				'original_name' => $sourceFile->original_name,
				'upload_source' => 'copy'
			]);
			
			// 增加物理文件引用计数
			$attachment = AttachmentModel::find($sourceFile->attachment_id);
			if ($attachment) {
				$attachment->incrementRefCount();
			}
			
			return $newFile;
		});
	}
	
}
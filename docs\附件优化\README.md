# 附件系统优化方案

## 📋 目录结构

```
docs/附件优化/
├── README.md                           # 本文件，项目概述
├── 01-需求分析与现状调研.md              # 需求分析和现状调研
├── 02-技术方案设计.md                   # 详细技术方案设计
├── 03-数据库设计.md                     # 数据库表结构设计
├── 04-API接口设计.md                    # API接口设计文档
├── 05-前端改造方案.md                   # 前端改造方案
├── 06-开发任务分解.md                   # 开发任务分解
├── 07-测试方案.md                       # 测试方案
├── 08-部署方案.md                       # 部署和迁移方案
├── sql/                                # SQL脚本目录
│   ├── 01-create-tables.sql            # 创建新表结构
│   ├── 02-create-indexes.sql           # 创建索引
│   └── 03-migration-scripts.sql        # 数据迁移脚本（如需要）
└── examples/                           # 示例代码目录
    ├── backend/                        # 后端示例代码
    └── frontend/                       # 前端示例代码
```

## 🎯 项目概述

### 背景
当前附件上传系统存在以下问题：
1. **重复存储**：不同用户上传相同文件时会重复存储，浪费存储空间
2. **权限控制不当**：附件控制器禁用了数据权限，导致用户可能看到其他用户的文件
3. **去重机制不完善**：虽然有MD5字段，但无法有效实现跨用户的文件去重

### 目标
1. **存储优化**：实现基于MD5的文件去重，相同文件只存储一份
2. **权限隔离**：确保用户只能访问自己的文件，保证数据安全
3. **架构优化**：分离物理文件存储和用户权限管理，提高系统灵活性
4. **云存储兼容**：充分利用云存储平台的去重能力（etag等）

### 核心方案
采用**用户文件关联表设计**：
- `system_attachment`：纯文件存储表，去除用户相关字段
- `system_attachment_user`：用户文件关联表，实现权限隔离

## 🚀 预期收益

### 存储效率
- **空间节省**：相同文件只存储一份，预计节省50-80%存储空间
- **成本降低**：减少云存储费用，特别是对象存储的成本

### 性能提升
- **上传优化**：相同文件秒传，提升用户体验
- **查询优化**：通过合理的索引设计，提升文件列表查询性能

### 安全性
- **权限隔离**：用户级别的文件访问控制
- **数据安全**：防止文件泄露，符合数据安全要求

### 扩展性
- **架构灵活**：支持未来的文件共享、协作等功能扩展
- **多存储支持**：统一的去重策略，支持多种存储平台

## 📅 开发计划

### 第一阶段：基础架构（预计2周）
- 数据库表结构设计和创建
- 核心服务类重构
- 基础API接口开发

### 第二阶段：功能实现（预计2周）
- 文件上传逻辑重构
- 文件列表和管理功能
- 权限控制实现

### 第三阶段：前端适配（预计1周）
- 前端组件适配
- 用户界面优化
- 功能测试

### 第四阶段：测试部署（预计1周）
- 系统测试
- 性能测试
- 生产环境部署

## 📖 文档说明

每个文档都包含详细的技术规范、实现细节和示例代码，请按顺序阅读：

1. **需求分析**：了解项目背景和需求
2. **技术方案**：掌握整体架构设计
3. **数据库设计**：理解数据模型
4. **API设计**：了解接口规范
5. **前端方案**：掌握前端改造要点
6. **开发任务**：按任务分解进行开发
7. **测试方案**：确保质量
8. **部署方案**：安全上线

## ⚠️ 注意事项

1. **向后兼容**：确保现有功能不受影响
2. **数据安全**：开发过程中注意数据权限控制
3. **性能监控**：关注系统性能变化
4. **逐步迁移**：建议分阶段实施，降低风险

## 🔗 相关资源

- [系统架构文档](../工作流表单后续整合规范/)
- [权限系统文档](../test_role/)
- [数据库规范](.cursor/rules/database_schema.mdc)

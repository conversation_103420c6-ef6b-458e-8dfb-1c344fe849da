<template>
  <div class="company-news-widget art-custom-card">
    <div class="widget-header">
      <h4 class="box-title">新闻资讯</h4>
      <el-button text type="primary" size="small" @click="viewAllNews"> 查看更多 </el-button>
    </div>

    <div class="news-content" v-loading="loading">
      <div v-if="newsList.length > 0" class="news-list">
        <div
          v-for="news in newsList"
          :key="news.id"
          class="news-item"
          @click="viewNewsDetail(news)"
        >
          <div class="news-header">
            <div class="news-title">{{ news.title }}</div>
            <div class="news-badges">
              <el-tag v-if="news.is_important" type="danger" size="small" effect="plain">
                重要
              </el-tag>
            </div>
          </div>
          <div class="news-meta">
            <span class="news-time">{{ news.created_at_text }}</span>
            <div class="news-summary" v-if="news.summary">
              {{ news.summary }}
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty :image-size="60" description="暂无数据">
          <el-button @click="loadCompanyNews" type="primary" size="small">
            <!--            刷新数据-->
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 企业资讯详情组件 -->
    <ArticleDetail ref="articleDetailRef" :show-actions="false" @success="loadCompanyNews" />
  </div>
</template>

<script setup lang="ts">
  import { WorkbenchApi, type CompanyNews } from '@/api/dashboard/workbenchApi'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import ArticleDetail from '@/components/system/ArticleDetail.vue'

  const router = useRouter()

  const newsList = ref<CompanyNews[]>([])
  const loading = ref(false)
  const articleDetailRef = ref()

  /**
   * 加载企业新闻数据
   */
  const loadCompanyNews = async () => {
    try {
      loading.value = true
      const res = await WorkbenchApi.getCompanyNews({ limit: 5 })

      if (res.code === 1) {
        newsList.value = res.data as CompanyNews[]
      }
    } catch (error) {
      console.error('加载企业新闻失败:', error)
      ElMessage.error('加载企业新闻失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 查看所有新闻
   */
  const viewAllNews = () => {
    router.push('/office/article/article_list')
  }

  /**
   * 查看新闻详情
   */
  const viewNewsDetail = (news: CompanyNews) => {
    // 打开详情弹窗
    articleDetailRef.value?.showDetail(news.id)
  }

  // 组件挂载时加载数据
  onMounted(() => {
    loadCompanyNews()
  })

  // 暴露刷新方法
  defineExpose({
    refresh: loadCompanyNews
  })
</script>

<style lang="scss" scoped>
  .company-news-widget {
    height: 400px;
    padding: 20px;
    background: var(--art-main-bg-color);
    border-radius: calc(var(--custom-radius) + 4px) !important;

    .widget-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--art-text-color-1);
      }
    }

    .news-content {
      height: calc(100% - 60px);
      overflow: hidden;
    }

    .news-list {
      height: 100%;
      overflow-y: auto;

      .news-item {
        padding: 16px;
        margin-bottom: 12px;
        border-radius: 8px;
        border: 1px solid var(--el-border-color-light);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
        }

        .news-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: 12px;
          gap: 12px;

          .news-title {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
            color: var(--art-text-color-1);
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }

          .news-badges {
            flex-shrink: 0;
          }
        }

        .news-meta {
          .news-time {
            font-size: 12px;
            color: var(--art-text-color-3);
            margin-bottom: 8px;
            display: block;
          }

          .news-summary {
            font-size: 13px;
            color: var(--art-text-color-2);
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }

    .empty-state {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .company-news-widget {
      height: auto;
      min-height: 300px;

      .news-content {
        height: auto;
        min-height: 240px;
      }

      .news-list {
        .news-item {
          padding: 12px;
          margin-bottom: 8px;

          .news-header {
            margin-bottom: 8px;
            gap: 8px;

            .news-title {
              font-size: 13px;
            }
          }

          .news-meta {
            .news-time {
              font-size: 11px;
              margin-bottom: 6px;
            }

            .news-summary {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
</style>

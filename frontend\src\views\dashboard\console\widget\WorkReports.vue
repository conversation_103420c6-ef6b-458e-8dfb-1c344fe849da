<template>
  <div class="work-reports-widget art-custom-card">
    <div class="widget-header">
      <h4 class="box-title">工作汇报</h4>
      <el-button text type="primary" size="small" @click="viewAllReports"> 查看更多</el-button>
    </div>

    <div class="reports-content" v-loading="loading">
      <div v-if="reports.length > 0" class="reports-list">
        <div
          v-for="report in reports"
          :key="report.id"
          class="report-item"
          @click="viewReportDetail(report)"
        >
          <div class="report-header">
            <div class="reporter-info">
              <span class="reporter-name">{{ report.reporter_name }}</span>
              <el-tag
                :type="getReportTypeColor(report.report_type)"
                size="small"
                class="report-type-tag"
              >
                {{ report.type_text }}
              </el-tag>
            </div>
            <span class="report-time">{{ report.created_at_text }}</span>
          </div>
          <div class="report-content">
            <div class="report-title">{{ report.title }}</div>
            <div class="report-summary">{{ report.summary }}</div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty :image-size="60" description="暂无数据">
          <!--          <el-button @click="loadWorkReports" type="primary" size="small"> 刷新数据 </el-button>-->
        </el-empty>
      </div>
    </div>

    <!-- 工作汇报详情组件 -->
    <WorkReportDetail ref="workReportDetailRef" :show-actions="false" @success="loadWorkReports" />
  </div>
</template>

<script setup lang="ts">
  import { WorkbenchApi, type WorkReport } from '@/api/dashboard/workbenchApi'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import WorkReportDetail from '@/components/crm/WorkReportDetail.vue'

  const router = useRouter()

  const reports = ref<WorkReport[]>([])
  const loading = ref(false)
  const workReportDetailRef = ref()

  /**
   * 加载工作汇报数据
   */
  const loadWorkReports = async () => {
    try {
      loading.value = true
      const res = await WorkbenchApi.getWorkReports({ limit: 5 })

      if (res.code === 1) {
        reports.value = res.data as WorkReport[]
      }
    } catch (error) {
      console.error('加载工作汇报失败:', error)
      ElMessage.error('加载工作汇报失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 查看所有汇报
   */
  const viewAllReports = () => {
    router.push('/office/crm_work_report')
  }

  /**
   * 查看汇报详情
   */
  const viewReportDetail = (report: WorkReport) => {
    // 打开详情弹窗
    workReportDetailRef.value?.showDetail(report.id)
  }

  /**
   * 获取汇报类型颜色
   */
  const getReportTypeColor = (type: number): string => {
    const colorMap: Record<number, string> = {
      1: 'primary', // 日报
      2: 'success', // 周报
      3: 'warning', // 月报
      4: 'info', // 季报
      5: 'danger' // 年报
    }
    return colorMap[type] || 'info'
  }

  // 组件挂载时加载数据
  onMounted(() => {
    loadWorkReports()
  })

  // 暴露刷新方法
  defineExpose({
    refresh: loadWorkReports
  })
</script>

<style lang="scss" scoped>
  .work-reports-widget {
    height: 400px;
    padding: 20px;
    background: var(--art-main-bg-color);
    border-radius: calc(var(--custom-radius) + 4px) !important;

    .widget-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--art-text-color-1);
      }
    }

    .reports-content {
      height: calc(100% - 60px);
      overflow: hidden;
    }

    .reports-list {
      height: 100%;
      overflow-y: auto;

      .report-item {
        padding: 16px;
        margin-bottom: 12px;
        border-radius: 8px;
        border: 1px solid var(--el-border-color-light);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
        }

        .report-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .reporter-info {
            display: flex;
            align-items: center;
            gap: 8px;

            .reporter-name {
              font-size: 14px;
              font-weight: 500;
              color: var(--art-text-color-1);
            }

            .report-type-tag {
              font-size: 11px;
            }
          }

          .report-time {
            font-size: 12px;
            color: var(--art-text-color-3);
          }
        }

        .report-content {
          .report-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--art-text-color-1);
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .report-summary {
            font-size: 13px;
            color: var(--art-text-color-2);
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }

    .empty-state {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .work-reports-widget {
      height: auto;
      min-height: 300px;

      .reports-content {
        height: auto;
        min-height: 240px;
      }

      .reports-list {
        .report-item {
          padding: 12px;
          margin-bottom: 8px;

          .report-header {
            margin-bottom: 8px;

            .reporter-info {
              .reporter-name {
                font-size: 13px;
              }
            }

            .report-time {
              font-size: 11px;
            }
          }

          .report-content {
            .report-title {
              font-size: 13px;
              margin-bottom: 6px;
            }

            .report-summary {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
</style>

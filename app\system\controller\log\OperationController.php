<?php

namespace app\system\controller\log;

use app\common\core\base\BaseAdminController;
use app\system\service\OperationLogService;
use think\response\Json;

/**
 * 操作日志控制器
 */
class OperationController extends BaseAdminController
{
	/**
	 * @var OperationLogService
	 */
	private OperationLogService $service;
	
	public function initialize(): void
	{
		parent::initialize();
		
		$this->service = OperationLogService::getInstance();
	}
	
    /**
     * 获取列表
     */
    public function index(): Json
    {
        return $this->success('获取成功', $this->service->getList(input()));
    }
    
    /**
     * 获取详情
     */
    public function detail($id): J<PERSON>
    {
        return $this->success('获取成功', $this->service->getDetail((int)$id));
    }
    
    /**
     * 删除
     */
    public function delete($id): Json
    {
	    return $this->service->delete((int)$id) ? $this->success('删除成功') : $this->error('删除失败');
    }
	
}

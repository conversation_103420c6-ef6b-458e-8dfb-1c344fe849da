<?php
/**
 * 执行权限更新SQL脚本
 */

require_once 'vendor/autoload.php';

echo "=== 执行权限更新 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 读取SQL脚本:\n";
    
    $sqlScript = file_get_contents('permission_normalization_fixed.sql');
    if (!$sqlScript) {
        throw new Exception("无法读取SQL脚本文件");
    }
    
    echo "  ✅ SQL脚本读取成功\n\n";
    
    echo "2. 执行前验证:\n";
    
    // 检查当前权限数量
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE status = 1 AND deleted_at IS NULL");
    $stmt->execute();
    $beforeCount = $stmt->fetch()['count'];
    echo "  执行前权限数量: {$beforeCount} 个\n";
    
    // 检查一些关键权限
    $testPermissions = [
        'crm:follow:index',
        'notice:message:delete',
        'project:project:index',
        'system:article:add'
    ];
    
    echo "  检查关键权限:\n";
    foreach ($testPermissions as $permission) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE name = ? AND status = 1 AND deleted_at IS NULL");
        $stmt->execute([$permission]);
        $exists = $stmt->fetch()['count'] > 0;
        echo "    {$permission}: " . ($exists ? '✅ 存在' : '❌ 不存在') . "\n";
    }
    
    echo "\n3. 执行SQL脚本:\n";
    
    // 分割SQL语句
    $statements = explode(';', $sqlScript);
    $executedCount = 0;
    $errorCount = 0;
    
    $pdo->beginTransaction();
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // 跳过注释和空语句
        if (empty($statement) || strpos($statement, '--') === 0 || 
            strpos($statement, 'START TRANSACTION') !== false ||
            strpos($statement, 'COMMIT') !== false ||
            strpos($statement, 'ROLLBACK') !== false) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executedCount++;
            
            // 显示执行进度
            if ($executedCount % 20 == 0) {
                echo "    已执行 {$executedCount} 条语句...\n";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "    ❌ 执行失败: " . substr($statement, 0, 50) . "...\n";
            echo "       错误: " . $e->getMessage() . "\n";
        }
    }
    
    if ($errorCount > 0) {
        echo "\n  ⚠️ 发现 {$errorCount} 个错误，回滚事务\n";
        $pdo->rollBack();
        throw new Exception("SQL执行过程中发现错误，已回滚");
    } else {
        $pdo->commit();
        echo "\n  ✅ 所有SQL语句执行成功，已提交事务\n";
        echo "  总执行语句数: {$executedCount} 条\n";
    }
    
    echo "\n4. 执行后验证:\n";
    
    // 检查执行后权限数量
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE status = 1 AND deleted_at IS NULL");
    $stmt->execute();
    $afterCount = $stmt->fetch()['count'];
    echo "  执行后权限数量: {$afterCount} 个\n";
    echo "  权限数量变化: " . ($afterCount - $beforeCount) . " 个\n";
    
    // 检查更新后的权限
    $updatedPermissions = [
        'crm:crm_follow:index',
        'notice:notice_message:delete',
        'project:project:index',
        'system:system_article:add'
    ];
    
    echo "  检查更新后权限:\n";
    foreach ($updatedPermissions as $permission) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE name = ? AND status = 1 AND deleted_at IS NULL");
        $stmt->execute([$permission]);
        $exists = $stmt->fetch()['count'] > 0;
        echo "    {$permission}: " . ($exists ? '✅ 存在' : '❌ 不存在') . "\n";
    }
    
    // 检查权限命名规范性
    echo "\n  检查权限命名规范性:\n";
    
    $stmt = $pdo->prepare("
        SELECT name, COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END
    ");
    $stmt->execute();
    $patterns = $stmt->fetchAll();
    
    foreach ($patterns as $pattern) {
        echo "    {$pattern['name']}: {$pattern['count']} 个\n";
    }
    
    echo "\n5. 权限更新完成!\n";
    echo "  ✅ 权限命名已规范化\n";
    echo "  ✅ 数据库更新成功\n";
    echo "  ✅ 可以继续下一步：更新权限解析逻辑\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "\n如果需要回滚，请执行:\n";
    echo "ROLLBACK;\n";
}

echo "\n=== 权限更新完成 ===\n";

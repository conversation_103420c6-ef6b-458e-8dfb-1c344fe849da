-- =====================================================
-- 附件系统优化 - 索引创建脚本
-- 版本: v1.0
-- 创建日期: 2025-01-31
-- 说明: 为新的附件系统表创建优化的索引结构
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;

-- =====================================================
-- 1. 为 system_attachment 表创建索引
-- =====================================================

-- 唯一索引：防止重复文件（核心去重索引）
-- 组合 storage + file_md5 + storage_id 确保同一存储方式下文件唯一
ALTER TABLE `system_attachment`
ADD UNIQUE KEY `uk_storage_identifier` (`storage`, `file_md5`, `storage_id`);

-- 单字段索引：支持各种查询场景
ALTER TABLE `system_attachment`
ADD KEY `idx_file_md5` (`file_md5`);

ALTER TABLE `system_attachment`
ADD KEY `idx_storage_id` (`storage_id`);

ALTER TABLE `system_attachment`
ADD KEY `idx_storage` (`storage`);

ALTER TABLE `system_attachment`
ADD KEY `idx_ref_count` (`ref_count`);

ALTER TABLE `system_attachment`
ADD KEY `idx_created_at` (`created_at`);

ALTER TABLE `system_attachment`
ADD KEY `idx_deleted_at` (`deleted_at`);

-- 复合索引：支持常用查询组合
ALTER TABLE `system_attachment`
ADD KEY `idx_storage_deleted` (`storage`, `deleted_at`);

ALTER TABLE `system_attachment`
ADD KEY `idx_ref_count_deleted` (`ref_count`, `deleted_at`);

-- =====================================================
-- 2. 为 system_attachment_user 表创建索引
-- =====================================================

-- 唯一索引：防止用户重复关联同一文件
-- 注意：deleted_at 包含在唯一索引中，支持软删除后重新关联
ALTER TABLE `system_attachment_user` 
ADD UNIQUE KEY `uk_attachment_user_tenant` (`attachment_id`, `user_id`, `tenant_id`, `deleted_at`);

-- 核心查询索引：用户文件列表查询（最重要的索引）
ALTER TABLE `system_attachment_user` 
ADD KEY `idx_user_tenant` (`user_id`, `tenant_id`);

-- 关联查询索引
ALTER TABLE `system_attachment_user` 
ADD KEY `idx_attachment_id` (`attachment_id`);

-- 分类查询索引
ALTER TABLE `system_attachment_user` 
ADD KEY `idx_cate_user` (`cate_id`, `user_id`, `tenant_id`);

-- 租户查询索引
ALTER TABLE `system_attachment_user` 
ADD KEY `idx_tenant_id` (`tenant_id`);

-- 创建者查询索引
ALTER TABLE `system_attachment_user` 
ADD KEY `idx_creator_id` (`creator_id`);

-- 时间查询索引
ALTER TABLE `system_attachment_user` 
ADD KEY `idx_created_at` (`created_at`);

ALTER TABLE `system_attachment_user` 
ADD KEY `idx_deleted_at` (`deleted_at`);

-- 复合索引：支持常用查询组合
ALTER TABLE `system_attachment_user` 
ADD KEY `idx_user_tenant_cate` (`user_id`, `tenant_id`, `cate_id`);

ALTER TABLE `system_attachment_user` 
ADD KEY `idx_user_tenant_deleted` (`user_id`, `tenant_id`, `deleted_at`);

ALTER TABLE `system_attachment_user` 
ADD KEY `idx_attachment_deleted` (`attachment_id`, `deleted_at`);

ALTER TABLE `system_attachment_user` 
ADD KEY `idx_tenant_created` (`tenant_id`, `created_at`);

-- =====================================================
-- 3. 为 system_attachment_stats 表创建索引
-- =====================================================

-- 唯一索引：每个租户每天只有一条统计记录
ALTER TABLE `system_attachment_stats` 
ADD UNIQUE KEY `uk_date_tenant` (`date`, `tenant_id`);

-- 查询索引
ALTER TABLE `system_attachment_stats` 
ADD KEY `idx_date` (`date`);

ALTER TABLE `system_attachment_stats` 
ADD KEY `idx_tenant_id` (`tenant_id`);

ALTER TABLE `system_attachment_stats` 
ADD KEY `idx_created_at` (`created_at`);

-- 复合索引：支持时间范围查询
ALTER TABLE `system_attachment_stats` 
ADD KEY `idx_tenant_date` (`tenant_id`, `date`);

-- =====================================================
-- 4. 添加外键约束（可选，根据需要启用）
-- =====================================================

-- 注意：外键约束会影响性能，请根据实际需求决定是否启用
-- 如果启用，请确保相关表存在且字段类型匹配

-- 用户文件关联表的外键约束
-- ALTER TABLE `system_attachment_user` 
-- ADD CONSTRAINT `fk_attachment_user_attachment`
-- FOREIGN KEY (`attachment_id`) REFERENCES `system_attachment` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `system_attachment_user` 
-- ADD CONSTRAINT `fk_attachment_user_user` 
-- FOREIGN KEY (`user_id`) REFERENCES `system_admin` (`id`) ON DELETE CASCADE;

-- =====================================================
-- 5. 创建触发器维护引用计数（可选）
-- =====================================================

-- 注意：触发器会影响性能，建议在应用层维护引用计数
-- 如果需要数据库层面的强一致性，可以启用以下触发器

DELIMITER $$

-- 插入用户关联时增加引用计数
CREATE TRIGGER `tr_attachment_user_insert`
AFTER INSERT ON `system_attachment_user`
FOR EACH ROW
BEGIN
    UPDATE `system_attachment`
    SET `ref_count` = `ref_count` + 1
    WHERE `id` = NEW.`attachment_id`;
END$$

-- 软删除用户关联时减少引用计数
CREATE TRIGGER `tr_attachment_user_soft_delete`
AFTER UPDATE ON `system_attachment_user`
FOR EACH ROW
BEGIN
    IF NEW.`deleted_at` IS NOT NULL AND OLD.`deleted_at` IS NULL THEN
        UPDATE `system_attachment`
        SET `ref_count` = `ref_count` - 1
        WHERE `id` = NEW.`attachment_id`;
    END IF;

    -- 如果从软删除恢复，增加引用计数
    IF NEW.`deleted_at` IS NULL AND OLD.`deleted_at` IS NOT NULL THEN
        UPDATE `system_attachment`
        SET `ref_count` = `ref_count` + 1
        WHERE `id` = NEW.`attachment_id`;
    END IF;
END$$

-- 物理删除用户关联时减少引用计数
CREATE TRIGGER `tr_attachment_user_delete`
AFTER DELETE ON `system_attachment_user`
FOR EACH ROW
BEGIN
    UPDATE `system_attachment`
    SET `ref_count` = `ref_count` - 1
    WHERE `id` = OLD.`attachment_id`;
END$$

DELIMITER ;

-- =====================================================
-- 6. 验证索引创建结果
-- =====================================================

-- 查看 system_attachment 表的索引
SHOW INDEX FROM `system_attachment`;

-- 查看 system_attachment_user 表的索引  
SHOW INDEX FROM `system_attachment_user`;

-- 查看 system_attachment_stats 表的索引
SHOW INDEX FROM `system_attachment_stats`;

-- =====================================================
-- 7. 索引使用情况分析查询
-- =====================================================

-- 查看索引统计信息
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    SUB_PART,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('system_attachment', 'system_attachment_user', 'system_attachment_stats')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- =====================================================
-- 8. 性能测试查询示例
-- =====================================================

-- 测试用户文件列表查询性能
EXPLAIN SELECT 
    au.id,
    au.display_name,
    au.cate_id,
    au.created_at,
    a.size,
    a.extension,
    a.mime_type
FROM system_attachment_user au
JOIN system_attachment a ON au.attachment_id = a.id
WHERE au.user_id = 1 
  AND au.tenant_id = 0
  AND au.deleted_at IS NULL
  AND a.deleted_at IS NULL
ORDER BY au.created_at DESC
LIMIT 20;

-- 测试文件去重查询性能
EXPLAIN SELECT id, ref_count
FROM system_attachment
WHERE storage = 'local' 
  AND file_md5 = 'abc123def456ghi789jkl012mno345pq' 
  AND deleted_at IS NULL;

-- 测试引用计数查询性能
EXPLAIN SELECT COUNT(*) 
FROM system_attachment_user 
WHERE attachment_id = 1 
  AND deleted_at IS NULL;

-- =====================================================
-- 9. 执行完成提示
-- =====================================================
SELECT '附件系统索引创建完成！' as message;
SELECT '已创建的索引数量：' as info, COUNT(*) as index_count
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('system_attachment', 'system_attachment_user', 'system_attachment_stats');

-- =====================================================
-- 索引创建完成
-- 
-- 创建的索引类型：
-- 1. 唯一索引 - 防止数据重复
-- 2. 单字段索引 - 支持基础查询
-- 3. 复合索引 - 支持复杂查询
-- 4. 外键约束 - 保证数据完整性（可选）
-- 5. 触发器 - 维护引用计数（可选）
--
-- 下一步：
-- 1. 如需迁移现有数据，执行 03-migration-scripts.sql
-- 2. 更新应用代码以使用新表结构
-- 3. 进行性能测试和优化
-- =====================================================

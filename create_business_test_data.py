#!/usr/bin/env python3
"""
创建业务测试数据
"""
import os
import mysql.connector
from datetime import datetime, timedelta
import random

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

def get_db_connection():
    return mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )

def create_crm_customers(cursor):
    """创建CRM客户测试数据"""
    print("1. 创建CRM客户测试数据:")
    
    customers = [
        {
            'customer_name': '北京科技有限公司',
            'phone': '13800001001',
            'industry': '科技',
            'source': '网络推广',
            'creator_id': 203,  # CRM管理员
            'owner_user_id': 203
        },
        {
            'customer_name': '上海贸易集团',
            'phone': '13800001002',
            'industry': '贸易',
            'source': '客户推荐',
            'creator_id': 205,  # 业务经理
            'owner_user_id': 205
        },
        {
            'customer_name': '深圳制造企业',
            'phone': '13800001003',
            'industry': '制造业',
            'source': '展会',
            'creator_id': 206,  # 普通员工
            'owner_user_id': 206
        },
        {
            'customer_name': '广州服务公司',
            'phone': '13800001004',
            'industry': '服务业',
            'source': '电话营销',
            'creator_id': 203,  # CRM管理员
            'owner_user_id': 205  # 分配给业务经理
        },
        {
            'customer_name': '杭州互联网公司',
            'phone': '13800001005',
            'industry': '互联网',
            'source': '网络推广',
            'creator_id': 205,  # 业务经理
            'owner_user_id': 203  # 分配给CRM管理员
        }
    ]
    
    for i, customer in enumerate(customers, 1):
        cursor.execute("""
            INSERT INTO crm_customer (
                customer_name, phone, industry, source,
                status, creator_id, owner_user_id, tenant_id, created_at, updated_at
            ) VALUES (
                %s, %s, %s, %s, 1, %s, %s, 1, NOW(), NOW()
            )
        """, (
            customer['customer_name'], customer['phone'], customer['industry'], customer['source'],
            customer['creator_id'], customer['owner_user_id']
        ))
        print(f"  ✅ 创建客户: {customer['customer_name']} (创建者: {customer['creator_id']}, 负责人: {customer['owner_user_id']})")

def create_crm_leads(cursor):
    """创建CRM线索测试数据"""
    print("\n2. 创建CRM线索测试数据:")
    
    leads = [
        {
            'lead_name': '赵经理',
            'company': '成都新兴企业',
            'mobile': '13800002001',
            'email': '<EMAIL>',
            'source': '网络推广',
            'status': 1,  # 新线索
            'creator_id': 206,  # 普通员工
            'owner_user_id': 206
        },
        {
            'lead_name': '钱总',
            'company': '重庆发展集团',
            'mobile': '13800002002',
            'email': '<EMAIL>',
            'source': '客户推荐',
            'status': 2,  # 跟进中
            'creator_id': 205,  # 业务经理
            'owner_user_id': 205
        },
        {
            'lead_name': '孙主任',
            'company': '西安科技园',
            'mobile': '13800002003',
            'email': '<EMAIL>',
            'source': '展会',
            'status': 3,  # 已联系
            'creator_id': 203,  # CRM管理员
            'owner_user_id': 203
        }
    ]
    
    for i, lead in enumerate(leads, 1):
        cursor.execute("""
            INSERT INTO crm_lead (
                lead_name, company, mobile, email, source, status,
                creator_id, owner_user_id, tenant_id, created_at, updated_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, 1, NOW(), NOW()
            )
        """, (
            lead['lead_name'], lead['company'], lead['mobile'],
            lead['email'], lead['source'], lead['status'],
            lead['creator_id'], lead['owner_user_id']
        ))
        print(f"  ✅ 创建线索: {lead['lead_name']} (创建者: {lead['creator_id']}, 负责人: {lead['owner_user_id']})")

def create_projects(cursor):
    """创建项目测试数据"""
    print("\n3. 创建项目测试数据:")
    
    projects = [
        {
            'name': 'CRM系统升级项目',
            'description': '对现有CRM系统进行功能升级和优化',
            'status': '进行中',
            'start_date': datetime.now().date(),
            'end_date': (datetime.now() + timedelta(days=90)).date(),
            'creator_id': 204,  # 项目管理员
            'manager_id': 204
        },
        {
            'name': '客户数据迁移项目',
            'description': '将旧系统客户数据迁移到新系统',
            'status': '计划中',
            'start_date': (datetime.now() + timedelta(days=30)).date(),
            'end_date': (datetime.now() + timedelta(days=60)).date(),
            'creator_id': 204,  # 项目管理员
            'manager_id': 205   # 分配给业务经理
        },
        {
            'name': '权限系统测试项目',
            'description': '对权限系统进行全面测试',
            'status': '进行中',
            'start_date': datetime.now().date(),
            'end_date': (datetime.now() + timedelta(days=15)).date(),
            'creator_id': 201,  # 租户超级管理员
            'manager_id': 204   # 分配给项目管理员
        }
    ]
    
    for i, project in enumerate(projects, 1):
        cursor.execute("""
            INSERT INTO project_project (
                name, description, status, start_date, end_date,
                creator_id, owner_id, updated_id, tenant_id, created_at, updated_at
            ) VALUES (
                %s, %s, 1, %s, %s, %s, %s, %s, 1, NOW(), NOW()
            )
        """, (
            project['name'], project['description'],
            project['start_date'], project['end_date'],
            project['creator_id'], project['manager_id'], project['creator_id']
        ))
        print(f"  ✅ 创建项目: {project['name']} (创建者: {project['creator_id']}, 负责人: {project['manager_id']})")

def main():
    print("=== 创建业务测试数据 ===\n")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        conn.autocommit = False
        
        # 创建CRM客户数据
        create_crm_customers(cursor)
        
        # 创建CRM线索数据
        create_crm_leads(cursor)
        
        # 创建项目数据
        create_projects(cursor)
        
        # 提交事务
        conn.commit()
        print(f"\n✅ 业务测试数据创建完成！")
        
        # 验证结果
        print(f"\n4. 验证创建结果:")
        
        cursor.execute("SELECT COUNT(*) FROM crm_customer WHERE tenant_id = 1")
        customer_count = cursor.fetchone()[0]
        print(f"  CRM客户数据: {customer_count} 条")
        
        cursor.execute("SELECT COUNT(*) FROM crm_lead WHERE tenant_id = 1")
        lead_count = cursor.fetchone()[0]
        print(f"  CRM线索数据: {lead_count} 条")
        
        cursor.execute("SELECT COUNT(*) FROM project_project WHERE tenant_id = 1")
        project_count = cursor.fetchone()[0]
        print(f"  项目数据: {project_count} 条")
        
    except Exception as e:
        conn.rollback()
        print(f"\n❌ 创建失败: {e}")
        raise
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()

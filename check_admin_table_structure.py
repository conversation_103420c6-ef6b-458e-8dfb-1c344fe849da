#!/usr/bin/env python3
"""
检查system_admin表结构
"""
import os
import mysql.connector

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

def get_db_connection():
    return mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )

def main():
    print("=== 检查system_admin表结构 ===\n")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 检查system_admin表结构
    cursor.execute("DESCRIBE system_admin")
    columns = cursor.fetchall()
    
    print("system_admin表字段:")
    for col in columns:
        field_name, field_type, null, key, default, extra = col
        print(f"  {field_name}: {field_type} (NULL: {null}, KEY: {key})")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    main()

<template>
  <div class="attachment-test-page">
    <el-card class="test-header">
      <template #header>
        <div class="card-header">
          <span>📁 附件管理系统测试页面</span>
          <el-tag type="success">重构版本</el-tag>
        </div>
      </template>
      <el-alert
        title="测试说明"
        type="info"
        description="此页面用于测试重构后的附件管理系统所有功能，包括文件去重、权限控制、多存储支持等。"
        show-icon
        :closable="false"
      />
    </el-card>

    <!-- 权限和统计信息 -->
    <el-row :gutter="20" class="info-cards">
      <el-col :span="12">
        <el-card title="权限信息">
          <template #header>
            <span>🔒 权限信息</span>
            <el-button type="primary" size="small" @click="loadPermissions">刷新</el-button>
          </template>
          <div v-if="permissions">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="上传权限">
                <el-tag :type="permissions.can_upload ? 'success' : 'danger'">
                  {{ permissions.can_upload ? '允许' : '拒绝' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="下载权限">
                <el-tag :type="permissions.can_download ? 'success' : 'danger'">
                  {{ permissions.can_download ? '允许' : '拒绝' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="删除权限">
                <el-tag :type="permissions.can_delete ? 'success' : 'danger'">
                  {{ permissions.can_delete ? '允许' : '拒绝' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="管理员">
                <el-tag :type="permissions.is_admin ? 'warning' : 'info'">
                  {{ permissions.is_admin ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-skeleton v-else :rows="3" animated />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card title="统计信息">
          <template #header>
            <span>📊 统计信息</span>
            <el-button type="primary" size="small" @click="loadStats">刷新</el-button>
          </template>
          <div v-if="stats">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="文件总数">
                <el-tag type="primary">{{ stats.total_files }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="存储总量">
                <el-tag type="success">{{ stats.total_size_formatted }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="分类数量">
                <el-tag type="info">{{ stats.by_category.length }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="文件类型">
                <el-tag type="warning">{{ stats.by_extension.length }}</el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-skeleton v-else :rows="3" animated />
        </el-card>
      </el-col>
    </el-row>

    <!-- 文件上传测试 -->
    <el-card class="upload-test">
      <template #header>
        <span>⬆️ 文件上传测试</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>FormUploader 组件测试</h4>
          <FormUploader
            v-model="uploadedFiles"
            :multiple="true"
            :limit="5"
            file-type="file"
            :category-id="0"
            storage="local"
            @success="handleUploadSuccess"
            @error="handleUploadError"
          />
          <div v-if="uploadedFiles.length > 0" class="uploaded-files">
            <h5>已上传文件：</h5>
            <el-tag
              v-for="file in uploadedFiles"
              :key="file"
              type="success"
              class="file-tag"
            >
              {{ file }}
            </el-tag>
          </div>
        </el-col>
        <el-col :span="12">
          <h4>MediaSelector 组件测试</h4>
          <el-button type="primary" @click="showMediaSelector">选择媒体文件</el-button>
          <div v-if="selectedMedia.length > 0" class="selected-media">
            <h5>已选择媒体：</h5>
            <el-tag
              v-for="media in selectedMedia"
              :key="media.id"
              type="info"
              class="file-tag"
            >
              {{ media.name }}
            </el-tag>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 文件列表和管理 -->
    <el-card class="file-management">
      <template #header>
        <span>📋 文件列表和管理</span>
        <el-button type="primary" size="small" @click="loadFileList">刷新列表</el-button>
      </template>
      
      <!-- 搜索和筛选 -->
      <el-row :gutter="20" class="search-bar">
        <el-col :span="6">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文件名"
            @change="loadFileList"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="selectedCategory" placeholder="选择分类" @change="loadFileList">
            <el-option label="全部" :value="-1" />
            <el-option label="未分类" :value="0" />
            <el-option label="分类1" :value="1" />
            <el-option label="分类2" :value="2" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="selectedStorage" placeholder="存储方式" @change="loadFileList">
            <el-option label="全部" value="" />
            <el-option label="本地存储" value="local" />
            <el-option label="云存储" value="qnoss" />
          </el-select>
        </el-col>
      </el-row>

      <!-- 文件表格 -->
      <el-table
        v-loading="tableLoading"
        :data="fileList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="display_name" label="文件名" min-width="200">
          <template #default="{ row }">
            <div class="file-info">
              <el-icon class="file-icon"><Document /></el-icon>
              <span>{{ row.display_name }}</span>
              <el-tag v-if="row.is_duplicate" type="warning" size="small">重复</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.size) }}
          </template>
        </el-table-column>
        <el-table-column prop="storage" label="存储" width="80">
          <template #default="{ row }">
            <el-tag :type="row.storage === 'local' ? 'success' : 'primary'" size="small">
              {{ row.storage }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="上传时间" width="150" />
        <el-table-column label="操作" width="300">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="viewFile(row)">详情</el-button>
              <el-button size="small" @click="renameFile(row)">重命名</el-button>
              <el-button size="small" @click="moveFile(row)">移动</el-button>
              <el-button size="small" @click="copyFile(row)">复制</el-button>
              <el-button size="small" type="danger" @click="deleteFile(row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadFileList"
        @current-change="loadFileList"
      />

      <!-- 批量操作 -->
      <div v-if="selectedFiles.length > 0" class="batch-operations">
        <el-alert
          :title="`已选择 ${selectedFiles.length} 个文件`"
          type="info"
          show-icon
          :closable="false"
        />
        <el-button-group>
          <el-button type="primary" @click="batchMove">批量移动</el-button>
          <el-button type="danger" @click="batchDelete">批量删除</el-button>
        </el-button-group>
      </div>
    </el-card>

    <!-- MediaSelector 对话框 -->
    <MediaSelector
      v-model="mediaSelectorVisible"
      :multiple="true"
      @confirm="handleMediaSelect"
    />

    <!-- 文件详情对话框 -->
    <el-dialog v-model="fileDetailVisible" title="文件详情" width="600px">
      <div v-if="currentFile">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="文件名">{{ currentFile.display_name }}</el-descriptions-item>
          <el-descriptions-item label="原始名">{{ currentFile.original_name }}</el-descriptions-item>
          <el-descriptions-item label="大小">{{ formatFileSize(currentFile.size) }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ currentFile.mime_type }}</el-descriptions-item>
          <el-descriptions-item label="存储">{{ currentFile.storage }}</el-descriptions-item>
          <el-descriptions-item label="分类ID">{{ currentFile.cate_id }}</el-descriptions-item>
          <el-descriptions-item label="上传时间">{{ currentFile.created_at }}</el-descriptions-item>
          <el-descriptions-item label="引用计数">{{ currentFile.ref_count || 1 }}</el-descriptions-item>
        </el-descriptions>
        <div class="file-preview" style="margin-top: 20px;">
          <el-image
            v-if="isImage(currentFile.mime_type)"
            :src="currentFile.url"
            style="max-width: 100%; max-height: 300px;"
            fit="contain"
          />
          <div v-else class="file-link">
            <el-button type="primary" @click="downloadFile(currentFile)">下载文件</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Document } from '@element-plus/icons-vue'
import { AttachmentApi, type AttachmentData, type AttachmentPermissionResult, type AttachmentStatsResult } from '@/api/attachmentApi'
import FormUploader from '@/components/custom/FormUploader/index.vue'
import MediaSelector from '@/components/custom/MediaSelector/index.vue'
import { ApiStatus } from '@/utils/http/status'

// 响应式数据
const permissions = ref<AttachmentPermissionResult | null>(null)
const stats = ref<AttachmentStatsResult | null>(null)
const uploadedFiles = ref<string[]>([])
const selectedMedia = ref<any[]>([])
const mediaSelectorVisible = ref(false)

// 文件列表相关
const fileList = ref<AttachmentData[]>([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const selectedFiles = ref<AttachmentData[]>([])

// 搜索筛选
const searchKeyword = ref('')
const selectedCategory = ref(-1)
const selectedStorage = ref('')

// 文件详情
const fileDetailVisible = ref(false)
const currentFile = ref<AttachmentData | null>(null)

// 加载权限信息
const loadPermissions = async () => {
  try {
    const res = await AttachmentApi.permissions()
    if (res.code === ApiStatus.success) {
      permissions.value = res.data
    }
  } catch (error) {
    console.error('加载权限信息失败:', error)
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    const res = await AttachmentApi.stats()
    if (res.code === ApiStatus.success) {
      stats.value = res.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 加载文件列表
const loadFileList = async () => {
  tableLoading.value = true
  try {
    const params: any = {
      page: currentPage.value,
      limit: pageSize.value
    }
    
    if (searchKeyword.value) params.keyword = searchKeyword.value
    if (selectedCategory.value !== -1) params.cate_id = selectedCategory.value
    if (selectedStorage.value) params.storage = selectedStorage.value
    
    const res = await AttachmentApi.list(params)
    if (res.code === ApiStatus.success) {
      fileList.value = res.data.data || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 文件上传成功回调
const handleUploadSuccess = (response: any, file: any) => {
  ElMessage.success(`文件 "${response.display_name}" 上传成功${response.is_duplicate ? '（检测到重复文件）' : ''}`)
  loadFileList()
  loadStats()
}

// 文件上传失败回调
const handleUploadError = (error: any) => {
  ElMessage.error('文件上传失败: ' + error.message)
}

// 显示媒体选择器
const showMediaSelector = () => {
  mediaSelectorVisible.value = true
}

// 媒体选择回调
const handleMediaSelect = (selected: any[]) => {
  selectedMedia.value = selected
  ElMessage.success(`选择了 ${selected.length} 个媒体文件`)
}

// 表格选择变化
const handleSelectionChange = (selection: AttachmentData[]) => {
  selectedFiles.value = selection
}

// 查看文件详情
const viewFile = async (file: AttachmentData) => {
  try {
    const res = await AttachmentApi.read(file.id)
    if (res.code === ApiStatus.success) {
      currentFile.value = res.data
      fileDetailVisible.value = true
    }
  } catch (error) {
    ElMessage.error('获取文件详情失败')
  }
}

// 重命名文件
const renameFile = async (file: AttachmentData) => {
  const { value: newName } = await ElMessageBox.prompt('请输入新的文件名', '重命名文件', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: file.display_name
  })
  
  if (newName) {
    try {
      const res = await AttachmentApi.rename(file.id, newName)
      if (res.code === ApiStatus.success) {
        ElMessage.success('重命名成功')
        loadFileList()
      } else {
        ElMessage.error(res.message || '重命名失败')
      }
    } catch (error) {
      ElMessage.error('重命名失败')
    }
  }
}

// 移动文件
const moveFile = async (file: AttachmentData) => {
  const { value: cateId } = await ElMessageBox.prompt('请输入目标分类ID', '移动文件', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: '1'
  })
  
  if (cateId !== null) {
    try {
      const res = await AttachmentApi.move(file.id, parseInt(cateId))
      if (res.code === ApiStatus.success) {
        ElMessage.success('移动成功')
        loadFileList()
      } else {
        ElMessage.error(res.message || '移动失败')
      }
    } catch (error) {
      ElMessage.error('移动失败')
    }
  }
}

// 复制文件
const copyFile = async (file: AttachmentData) => {
  const { value: cateId } = await ElMessageBox.prompt('请输入目标分类ID', '复制文件', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: '2'
  })
  
  if (cateId !== null) {
    try {
      const res = await AttachmentApi.copy(file.id, parseInt(cateId), `复制_${file.display_name}`)
      if (res.code === ApiStatus.success) {
        ElMessage.success('复制成功')
        loadFileList()
        loadStats()
      } else {
        ElMessage.error(res.message || '复制失败')
      }
    } catch (error) {
      ElMessage.error('复制失败')
    }
  }
}

// 删除文件
const deleteFile = async (file: AttachmentData) => {
  await ElMessageBox.confirm(`确定要删除文件 "${file.display_name}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  
  try {
    const res = await AttachmentApi.delete([file.id])
    if (res.code === ApiStatus.success) {
      ElMessage.success('删除成功')
      loadFileList()
      loadStats()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 批量移动
const batchMove = async () => {
  const { value: cateId } = await ElMessageBox.prompt('请输入目标分类ID', '批量移动', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: '1'
  })
  
  if (cateId !== null) {
    try {
      const fileIds = selectedFiles.value.map(f => f.id)
      const res = await AttachmentApi.batchMove(fileIds, parseInt(cateId))
      if (res.code === ApiStatus.success) {
        ElMessage.success('批量移动成功')
        loadFileList()
      } else {
        ElMessage.error(res.message || '批量移动失败')
      }
    } catch (error) {
      ElMessage.error('批量移动失败')
    }
  }
}

// 批量删除
const batchDelete = async () => {
  await ElMessageBox.confirm(`确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`, '批量删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  
  try {
    const fileIds = selectedFiles.value.map(f => f.id)
    const res = await AttachmentApi.delete(fileIds)
    if (res.code === ApiStatus.success) {
      ElMessage.success('批量删除成功')
      loadFileList()
      loadStats()
    } else {
      ElMessage.error(res.message || '批量删除失败')
    }
  } catch (error) {
    ElMessage.error('批量删除失败')
  }
}

// 下载文件
const downloadFile = (file: AttachmentData) => {
  window.open(file.url, '_blank')
}

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const isImage = (mimeType: string): boolean => {
  return mimeType.startsWith('image/')
}

// 初始化
onMounted(() => {
  loadPermissions()
  loadStats()
  loadFileList()
})
</script>

<style scoped>
.attachment-test-page {
  padding: 20px;
}

.test-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-cards {
  margin-bottom: 20px;
}

.upload-test {
  margin-bottom: 20px;
}

.uploaded-files,
.selected-media {
  margin-top: 10px;
}

.file-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.file-management {
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 20px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #409eff;
}

.batch-operations {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.file-preview {
  text-align: center;
}

.file-link {
  padding: 20px;
}
</style>

# 统一开发规范

**版本**: v2.0  
**更新日期**: 2025-01-31  
**适用系统**: CRM管理系统

---

## 🎯 规范目标

### 核心原则
- **一致性**: 所有模块使用统一的开发规范
- **可维护性**: 代码结构清晰，易于维护
- **可扩展性**: 便于功能扩展和模块添加
- **可读性**: 代码命名规范，注释完整

---

## 📁 文件命名规范

### 控制器命名
```php
// 格式: {Module}{Function}Controller
// 示例:
CrmCustomerMyController.php      // CRM我的客户控制器
SystemPermissionAdminController.php  // 系统权限管理员控制器
ProjectTaskController.php       // 项目任务控制器
WorkflowApplicationController.php // 工作流申请控制器
```

### 模型命名
```php
// 格式: {TableName}Model (驼峰命名)
// 示例:
CrmCustomerModel.php            // CRM客户模型
SystemAdminModel.php            // 系统管理员模型
ProjectTaskModel.php            // 项目任务模型
```

### 服务类命名
```php
// 格式: {Function}Service
// 示例:
PermissionService.php           // 权限服务
NotificationService.php         // 通知服务
WorkflowService.php            // 工作流服务
```

---

## 🛣️ 路由规范

### 路由文件结构
```php
<?php
use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

Route::group('api/module', function () {
    $nameSpace = '\app\module\controller';
    
    // 路由定义
    Route::get('resource/index', $nameSpace . '\ResourceController@index');
    Route::post('resource/add', $nameSpace . '\ResourceController@add');
    Route::put('resource/edit/:id', $nameSpace . '\ResourceController@edit');
    Route::delete('resource/delete', $nameSpace . '\ResourceController@delete');
    
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class,
    // OperationLogMiddleware::class
]);
```

### 路由命名规范
```
格式: api/module/controller/action
示例:
- api/crm/customer_my/index
- api/system/permission/admin/add
- api/project/task/edit/:id
- api/workflow/application/delete
```

---

## 🔐 权限规范

### 权限命名格式
```
统一格式: module:controller:action

命名规范:
- module: 模块名，小写
- controller: 控制器名，下划线格式 (camelCase → snake_case)
- action: 方法名，下划线格式 (camelCase → snake_case)

示例:
- crm:customer_my:index          # CrmCustomerMyController@index
- crm:customer_my:add_user       # CrmCustomerMyController@addUser
- system:permission_admin:add    # AdminController@add
- project:task:edit              # ProjectTaskController@edit
- workflow:application:delete    # ApplicationController@delete
- crm:lead:get_user_list        # CrmLeadController@getUserList
```

### 权限解析逻辑
```php
/**
 * 统一的权限解析方法
 */
private function generatePermissionName(string $module, array $parts, string $controllerName, string $method): string
{
    $snakeName = $this->camelToSnake($controllerName);
    
    // 处理子目录控制器
    if (count($parts) > 4) {
        $subPath = strtolower($parts[3]);
        return $subPath . '_' . $snakeName . ':' . $method;
    }
    
    // 基础控制器直接使用控制器名
    return $snakeName . ':' . $method;
}
```

### 权限验证使用
```php
// 在控制器中使用权限验证
class CrmCustomerMyController extends BaseController
{
    // 权限会自动解析为: crm:customer_my:index
    public function index()
    {
        // 业务逻辑
    }
}
```

---

## 🏗️ 代码结构规范

### 控制器结构
```php
<?php
namespace app\crm\controller;

use app\BaseController;
use app\crm\model\CrmCustomerModel;
use app\crm\service\CustomerService;

class CrmCustomerMyController extends BaseController
{
    protected $customerService;
    
    public function __construct()
    {
        parent::__construct();
        $this->customerService = new CustomerService();
    }
    
    /**
     * 获取我的客户列表
     */
    public function index()
    {
        try {
            $params = $this->request->param();
            $result = $this->customerService->getMyCustomers($params);
            
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
    
    /**
     * 添加客户
     */
    public function add()
    {
        try {
            $data = $this->request->post();
            $this->validate($data, 'CustomerValidate');
            
            $result = $this->customerService->addCustomer($data);
            
            return $this->success($result, '添加成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
```

### 服务层结构
```php
<?php
namespace app\crm\service;

use app\crm\model\CrmCustomerModel;
use think\facade\Db;

class CustomerService
{
    protected $customerModel;
    
    public function __construct()
    {
        $this->customerModel = new CrmCustomerModel();
    }
    
    /**
     * 获取我的客户列表
     */
    public function getMyCustomers(array $params): array
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        
        $where = [];
        $where[] = ['admin_id', '=', $this->getCurrentAdminId()];
        $where[] = ['deleted_at', '=', null];
        
        if (!empty($params['keyword'])) {
            $where[] = ['name', 'like', '%' . $params['keyword'] . '%'];
        }
        
        $result = $this->customerModel
            ->where($where)
            ->order('created_at desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);
            
        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 添加客户
     */
    public function addCustomer(array $data): bool
    {
        Db::startTrans();
        try {
            $data['admin_id'] = $this->getCurrentAdminId();
            $data['tenant_id'] = $this->getCurrentTenantId();
            $data['created_at'] = date('Y-m-d H:i:s');
            
            $this->customerModel->save($data);
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
}
```

---

## 📝 注释规范

### 类注释
```php
/**
 * CRM客户管理控制器
 * 
 * @package app\crm\controller
 * <AUTHOR>
 * @version 2.0
 * @since 2025-01-31
 */
class CrmCustomerMyController extends BaseController
{
    // 类实现
}
```

### 方法注释
```php
/**
 * 获取我的客户列表
 * 
 * @param array $params 查询参数
 * @return array 客户列表数据
 * @throws \Exception 当查询失败时抛出异常
 */
public function getMyCustomers(array $params): array
{
    // 方法实现
}
```

### 属性注释
```php
/**
 * 客户服务实例
 * @var CustomerService
 */
protected $customerService;
```

---

## 🔧 编码规范

### PSR标准
- 遵循 **PSR-1** 基本编码标准
- 遵循 **PSR-2** 编码风格指南
- 遵循 **PSR-4** 自动加载标准
- 遵循 **PSR-12** 扩展编码风格

### 命名规范
```php
// 类名: 大驼峰命名
class CrmCustomerMyController

// 方法名: 小驼峰命名
public function getMyCustomers()

// 变量名: 小驼峰命名
$customerList = [];

// 常量名: 全大写下划线分隔
const MAX_RETRY_COUNT = 3;

// 数据库字段: 下划线命名
$data['created_at'] = date('Y-m-d H:i:s');
```

### 代码格式
```php
// 缩进: 4个空格
if ($condition) {
    $result = $this->processData($data);
}

// 数组格式
$config = [
    'host' => 'localhost',
    'port' => 3306,
    'database' => 'crm_system'
];

// 方法链式调用
$result = $this->customerModel
    ->where('status', 1)
    ->where('deleted_at', null)
    ->order('created_at desc')
    ->select();
```

---

## 🗄️ 数据库规范

### 表命名
```sql
-- 格式: module_table_name
-- 示例:
crm_customer          -- CRM客户表
system_admin          -- 系统管理员表
project_task          -- 项目任务表
workflow_instance     -- 工作流实例表
```

### 字段命名
```sql
-- 主键
id INT PRIMARY KEY AUTO_INCREMENT

-- 外键
admin_id INT           -- 管理员ID
tenant_id INT          -- 租户ID
parent_id INT          -- 父级ID

-- 状态字段
status TINYINT DEFAULT 1    -- 状态(1:正常 0:禁用)

-- 时间字段
created_at DATETIME    -- 创建时间
updated_at DATETIME    -- 更新时间
deleted_at DATETIME    -- 删除时间(软删除)
```

### 索引规范
```sql
-- 主键索引
PRIMARY KEY (id)

-- 唯一索引
UNIQUE KEY uk_username (username)

-- 普通索引
KEY idx_tenant_id (tenant_id)
KEY idx_created_at (created_at)

-- 复合索引
KEY idx_tenant_status (tenant_id, status)
```

---

## 🧪 测试规范

### 单元测试
```php
<?php
namespace tests\unit\crm\service;

use PHPUnit\Framework\TestCase;
use app\crm\service\CustomerService;

class CustomerServiceTest extends TestCase
{
    protected $customerService;
    
    protected function setUp(): void
    {
        $this->customerService = new CustomerService();
    }
    
    public function testGetMyCustomers()
    {
        $params = ['page' => 1, 'limit' => 10];
        $result = $this->customerService->getMyCustomers($params);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('total', $result);
    }
}
```

### API测试
```php
public function testCustomerApi()
{
    // 测试获取客户列表
    $response = $this->get('/api/crm/customer_my/index');
    $response->assertStatus(200);
    $response->assertJsonStructure([
        'code',
        'message',
        'data' => [
            'list',
            'total'
        ]
    ]);
}
```

---

## 📋 代码审查清单

### 功能性检查
- [ ] 功能是否按需求实现
- [ ] 边界条件是否处理
- [ ] 错误处理是否完善
- [ ] 权限验证是否正确

### 代码质量检查
- [ ] 命名是否规范
- [ ] 注释是否完整
- [ ] 代码结构是否清晰
- [ ] 是否遵循设计模式

### 性能检查
- [ ] 数据库查询是否优化
- [ ] 是否存在N+1查询
- [ ] 缓存使用是否合理
- [ ] 内存使用是否优化

### 安全检查
- [ ] 输入验证是否完善
- [ ] SQL注入防护是否到位
- [ ] XSS防护是否完善
- [ ] 权限控制是否严格

---

**请所有开发人员严格遵循本规范，确保代码质量和系统稳定性。**

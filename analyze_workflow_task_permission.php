<?php
/**
 * 分析workflow/task路由的权限问题
 */

require_once 'vendor/autoload.php';

echo "=== 分析workflow/task路由权限问题 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 分析路由 api/workflow/task/index:\n";
    
    // 这个路由对应的控制器应该是：app\workflow\controller\TaskController@index
    $expectedController = 'app\\workflow\\controller\\TaskController@index';
    echo "  预期控制器: {$expectedController}\n";
    
    // 根据我们的权限解析逻辑，应该生成的权限名称
    function generateWorkflowPermissionName($controllerName, $method) {
        $snakeName = strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $controllerName));
        if (!str_starts_with($snakeName, 'workflow_')) {
            $snakeName = 'workflow_' . $snakeName;
        }
        return $snakeName . ':' . $method;
    }
    
    $expectedPermission = 'workflow:' . generateWorkflowPermissionName('Task', 'index');
    echo "  预期权限: {$expectedPermission}\n";
    
    echo "\n2. 检查workflow模块的所有权限:\n";
    
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'workflow:%' AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $workflowPermissions = $stmt->fetchAll();
    
    echo "  workflow模块权限列表:\n";
    foreach ($workflowPermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "    - {$perm['name']} ({$perm['title']}) [{$type}]\n";
    }
    
    echo "\n3. 检查是否存在task相关权限:\n";
    
    $taskPermissions = array_filter($workflowPermissions, function($perm) {
        return strpos($perm['name'], 'task') !== false;
    });
    
    if (empty($taskPermissions)) {
        echo "  ❌ 没有找到task相关权限\n";
        echo "  这就是问题所在！\n";
    } else {
        echo "  找到的task相关权限:\n";
        foreach ($taskPermissions as $perm) {
            echo "    - {$perm['name']} ({$perm['title']})\n";
        }
    }
    
    echo "\n4. 检查实际的控制器文件:\n";
    
    $workflowControllerDir = 'app/workflow/controller';
    if (is_dir($workflowControllerDir)) {
        $files = glob($workflowControllerDir . '/*Controller.php');
        echo "  workflow控制器文件:\n";
        foreach ($files as $file) {
            $filename = basename($file);
            echo "    - {$filename}\n";
        }
        
        // 检查TaskController是否存在
        $taskControllerFile = $workflowControllerDir . '/TaskController.php';
        if (file_exists($taskControllerFile)) {
            echo "  ✅ TaskController.php 文件存在\n";
        } else {
            echo "  ❌ TaskController.php 文件不存在\n";
        }
    } else {
        echo "  ❌ workflow控制器目录不存在\n";
    }
    
    echo "\n5. 检查tenant_admin用户是否有workflow权限:\n";
    
    $stmt = $pdo->prepare("
        SELECT m.name, m.title
        FROM system_admin_role ar
        LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
        LEFT JOIN system_menu m ON rm.menu_id = m.id
        WHERE ar.admin_id = 201 AND m.name LIKE 'workflow:%' 
        AND m.status = 1 AND m.deleted_at IS NULL
        ORDER BY m.name
    ");
    $stmt->execute();
    $userWorkflowPermissions = $stmt->fetchAll();
    
    echo "  tenant_admin的workflow权限:\n";
    if (empty($userWorkflowPermissions)) {
        echo "    ❌ 没有workflow权限\n";
    } else {
        foreach ($userWorkflowPermissions as $perm) {
            echo "    - {$perm['name']} ({$perm['title']})\n";
        }
    }
    
    echo "\n6. 问题诊断:\n";
    
    $issues = [];
    
    // 检查权限是否存在
    if (empty($taskPermissions)) {
        $issues[] = "缺少workflow:workflow_task:index权限";
    }
    
    // 检查用户是否有权限
    $hasTaskPermission = false;
    foreach ($userWorkflowPermissions as $perm) {
        if (strpos($perm['name'], 'task') !== false) {
            $hasTaskPermission = true;
            break;
        }
    }
    
    if (!$hasTaskPermission) {
        $issues[] = "tenant_admin用户没有task相关权限";
    }
    
    if (empty($issues)) {
        echo "  ✅ 没有发现明显问题\n";
    } else {
        echo "  发现的问题:\n";
        foreach ($issues as $issue) {
            echo "    ❌ {$issue}\n";
        }
    }
    
    echo "\n7. 解决方案:\n";
    
    if (empty($taskPermissions)) {
        echo "  需要添加workflow task权限:\n";
        echo "  \n";
        echo "  SQL语句:\n";
        echo "  INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES\n";
        echo "  ('workflow:workflow_task:index', '工作流任务管理', 1, 1, 100, NOW(), NOW()),\n";
        echo "  ('workflow:workflow_task:add', '添加工作流任务', 2, 1, 101, NOW(), NOW()),\n";
        echo "  ('workflow:workflow_task:edit', '编辑工作流任务', 2, 1, 102, NOW(), NOW()),\n";
        echo "  ('workflow:workflow_task:delete', '删除工作流任务', 2, 1, 103, NOW(), NOW()),\n";
        echo "  ('workflow:workflow_task:detail', '工作流任务详情', 2, 1, 104, NOW(), NOW());\n";
        echo "  \n";
        echo "  然后需要将这些权限分配给租户超级管理员角色。\n";
    }
    
    echo "\n8. 检查路由配置:\n";
    
    // 检查路由文件
    $routeFiles = [
        'route/Workflow.php',
        'route/Router.php'
    ];
    
    foreach ($routeFiles as $routeFile) {
        if (file_exists($routeFile)) {
            $content = file_get_contents($routeFile);
            if (strpos($content, 'task') !== false) {
                echo "  ✅ {$routeFile} 包含task路由\n";
                
                // 检查是否使用了权限中间件
                if (strpos($content, 'PermissionMiddleware') !== false) {
                    echo "    ✅ 使用了权限中间件\n";
                } else {
                    echo "    ⚠️ 可能没有使用权限中间件\n";
                }
            } else {
                echo "  ❌ {$routeFile} 不包含task路由\n";
            }
        } else {
            echo "  ❌ {$routeFile} 文件不存在\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 分析完成 ===\n";

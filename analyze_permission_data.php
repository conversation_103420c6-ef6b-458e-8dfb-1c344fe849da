<?php
/**
 * 分析当前权限数据格式，为统一规范提供准确数据
 */

require_once 'vendor/autoload.php';

echo "=== 分析当前权限数据格式 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取所有权限数据:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type, status
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $allPermissions = $stmt->fetchAll();
    
    echo "  总权限数: " . count($allPermissions) . " 个\n\n";
    
    echo "2. 按格式分类权限:\n";
    
    $formatGroups = [
        'three_parts' => [],      // module:controller:action
        'four_parts' => [],       // module:sub:controller:action
        'two_parts' => [],        // module:controller
        'single_word' => [],      // word
        'other' => []            // 其他格式
    ];
    
    foreach ($allPermissions as $perm) {
        $name = $perm['name'];
        $parts = explode(':', $name);
        
        if (count($parts) == 4) {
            $formatGroups['four_parts'][] = $perm;
        } elseif (count($parts) == 3) {
            $formatGroups['three_parts'][] = $perm;
        } elseif (count($parts) == 2) {
            $formatGroups['two_parts'][] = $perm;
        } elseif (count($parts) == 1) {
            $formatGroups['single_word'][] = $perm;
        } else {
            $formatGroups['other'][] = $perm;
        }
    }
    
    foreach ($formatGroups as $format => $perms) {
        echo "  {$format}: " . count($perms) . " 个\n";
        if (!empty($perms)) {
            echo "    示例:\n";
            for ($i = 0; $i < min(3, count($perms)); $i++) {
                echo "      - {$perms[$i]['name']} ({$perms[$i]['title']})\n";
            }
        }
    }
    
    echo "\n3. 按模块分析权限一致性:\n";
    
    $moduleAnalysis = [];
    
    foreach ($allPermissions as $perm) {
        $parts = explode(':', $perm['name']);
        if (count($parts) >= 2) {
            $module = $parts[0];
            
            if (!isset($moduleAnalysis[$module])) {
                $moduleAnalysis[$module] = [
                    'total' => 0,
                    'three_parts' => 0,
                    'four_parts' => 0,
                    'two_parts' => 0,
                    'permissions' => []
                ];
            }
            
            $moduleAnalysis[$module]['total']++;
            $moduleAnalysis[$module]['permissions'][] = $perm;
            
            if (count($parts) == 4) {
                $moduleAnalysis[$module]['four_parts']++;
            } elseif (count($parts) == 3) {
                $moduleAnalysis[$module]['three_parts']++;
            } elseif (count($parts) == 2) {
                $moduleAnalysis[$module]['two_parts']++;
            }
        }
    }
    
    foreach ($moduleAnalysis as $module => $stats) {
        $consistency = 'mixed';
        if ($stats['three_parts'] > 0 && $stats['four_parts'] == 0 && $stats['two_parts'] == 0) {
            $consistency = 'three_parts_only';
        } elseif ($stats['four_parts'] > 0 && $stats['three_parts'] == 0 && $stats['two_parts'] == 0) {
            $consistency = 'four_parts_only';
        } elseif ($stats['two_parts'] > 0 && $stats['three_parts'] == 0 && $stats['four_parts'] == 0) {
            $consistency = 'two_parts_only';
        }
        
        $status = ($consistency === 'mixed') ? '❌' : '✅';
        echo "  {$status} {$module}: {$stats['total']}个 ";
        echo "(3段:{$stats['three_parts']}, 4段:{$stats['four_parts']}, 2段:{$stats['two_parts']}) ";
        echo "- {$consistency}\n";
    }
    
    echo "\n4. 需要统一的权限分析:\n";
    
    $needsUnification = [];
    
    foreach ($moduleAnalysis as $module => $stats) {
        if ($stats['three_parts'] > 0 && ($stats['four_parts'] > 0 || $stats['two_parts'] > 0)) {
            $needsUnification[$module] = $stats;
        }
    }
    
    if (!empty($needsUnification)) {
        echo "  需要统一的模块:\n";
        foreach ($needsUnification as $module => $stats) {
            echo "    {$module}: 需要统一 {$stats['total']} 个权限\n";
            
            // 显示不一致的权限示例
            $inconsistentPerms = [];
            foreach ($stats['permissions'] as $perm) {
                $parts = explode(':', $perm['name']);
                if (count($parts) != 3) { // 假设目标格式是3段式
                    $inconsistentPerms[] = $perm['name'];
                }
            }
            
            if (!empty($inconsistentPerms)) {
                echo "      需要调整的权限示例:\n";
                for ($i = 0; $i < min(3, count($inconsistentPerms)); $i++) {
                    echo "        - {$inconsistentPerms[$i]}\n";
                }
            }
        }
    } else {
        echo "  ✅ 所有模块权限格式一致\n";
    }
    
    echo "\n5. 生成统一方案:\n";
    
    $unificationPlan = [
        'target_format' => 'module:controller:action',
        'modules_to_update' => [],
        'estimated_updates' => 0
    ];
    
    foreach ($needsUnification as $module => $stats) {
        $updateCount = $stats['four_parts'] + $stats['two_parts'];
        $unificationPlan['modules_to_update'][$module] = $updateCount;
        $unificationPlan['estimated_updates'] += $updateCount;
    }
    
    echo "  目标格式: {$unificationPlan['target_format']}\n";
    echo "  需要更新的模块: " . count($unificationPlan['modules_to_update']) . " 个\n";
    echo "  预计更新权限数: {$unificationPlan['estimated_updates']} 个\n";
    
    if (!empty($unificationPlan['modules_to_update'])) {
        echo "  详细更新计划:\n";
        foreach ($unificationPlan['modules_to_update'] as $module => $count) {
            echo "    {$module}: {$count} 个权限需要更新\n";
        }
    }
    
    // 保存分析结果
    $analysisData = [
        'total_permissions' => count($allPermissions),
        'format_groups' => array_map('count', $formatGroups),
        'module_analysis' => $moduleAnalysis,
        'unification_plan' => $unificationPlan,
        'analysis_time' => date('Y-m-d H:i:s')
    ];
    
    file_put_contents('permission_data_analysis.json', json_encode($analysisData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "\n✅ 分析结果已保存到: permission_data_analysis.json\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 权限数据分析完成 ===\n";

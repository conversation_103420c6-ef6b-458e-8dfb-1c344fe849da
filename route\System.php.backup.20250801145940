<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/system', function () {
	
	
	$nameSpace = '\app\system\controller';
	
	// 系统-权限管理-管理员相关路由
	Route::post('admin/reset_password/:id', $nameSpace . '\permission\Admin@resetPassword');
	Route::post('admin/avatar', $nameSpace . '\permission\Admin@avatar');
	
	Route::get('admin/index', $nameSpace . '\permission\Admin@index');
	Route::get('admin/detail/:id', $nameSpace . '\permission\Admin@detail');
	Route::post('admin/add', $nameSpace . '\permission\Admin@add');
	Route::post('admin/edit/:id', $nameSpace . '\permission\Admin@edit');
	
	Route::post('admin/delete', $nameSpace . '\permission\Admin@delete');
	
	// 系统-权限管理-菜单相关路由
	Route::get('menu/index', $nameSpace . '\permission\Menu@index');
	Route::get('menu/detail/:id', $nameSpace . '\permission\Menu@detail');
	Route::post('menu/add', $nameSpace . '\permission\Menu@add');
	Route::post('menu/edit/:id', $nameSpace . '\permission\Menu@edit');
	Route::post('menu/delete', $nameSpace . '\permission\Menu@delete');
	
	// 系统-权限管理-角色相关路由
	Route::get('role/index', $nameSpace . '\permission\Role@index');
	Route::get('role/detail/:id', $nameSpace . '\permission\Role@detail');
	Route::post('role/add', $nameSpace . '\permission\Role@add');
	Route::post('role/edit/:id', $nameSpace . '\permission\Role@edit');
	Route::post('role/delete', $nameSpace . '\permission\Role@delete');
	
	// 系统-权限管理-部门相关路由
	Route::get('department/index', $nameSpace . '\permission\Department@index');
	Route::get('department/detail/:id', $nameSpace . '\permission\Department@detail');
	Route::post('department/add', $nameSpace . '\permission\Department@add');
	Route::post('department/edit/:id', $nameSpace . '\permission\Department@edit');
	Route::post('department/delete', $nameSpace . '\permission\Department@delete');
	
	
	// 系统-权限管理-岗位相关路由
	Route::get('post/index', $nameSpace . '\permission\Post@index');
	Route::get('post/detail/:id', $nameSpace . '\permission\Post@detail');
	Route::post('post/add', $nameSpace . '\permission\Post@add');
	Route::post('post/edit/:id', $nameSpace . '\permission\Post@edit');
	Route::post('post/delete', $nameSpace . '\permission\Post@delete');
	
	// 系统配置相关路由
	Route::get('config/detail', $nameSpace . '\Config@detail');
	Route::post('config/save', $nameSpace . '\Config@save');
	
	// 租户配置相关路由
	Route::get('tenant/config/detail', $nameSpace . '\tenant\TenantConfig@detail');
	Route::post('tenant/config/save', $nameSpace . '\tenant\TenantConfig@save');
	
	// 登录日志
	Route::get('log/login/index', $nameSpace . '\log\Login@index');
	Route::get('log/login/detail/:id', $nameSpace . '\log\Login@detail');
	Route::post('log/login/delete', $nameSpace . '\log\Login@delete');
	
	// 操作日志
	Route::get('log/operation/index', $nameSpace . '\log\Operation@index');
	Route::get('log/operation/detail/:id', $nameSpace . '\log\Operation@detail');
	Route::post('log/operation/delete', $nameSpace . '\log\Operation@delete');
	
	Route::get('attachment/index', $nameSpace . '\Attachment@index');
	Route::post('attachment/delete', $nameSpace . '\Attachment@delete');
	
	Route::get('attachment_cat/index', $nameSpace . '\AttachmentCat@index');
	Route::post('attachment_cat/add', $nameSpace . '\AttachmentCat@add');
	Route::post('attachment_cat/edit/:id', $nameSpace . '\AttachmentCat@edit');
	Route::post('attachment_cat/delete', $nameSpace . '\AttachmentCat@delete');
	
	// 租户相关路由
	Route::get('tenant/index', $nameSpace . '\tenant\TenantController@index');
	Route::get('tenant/detail/:id', $nameSpace . '\tenant\TenantController@detail');
	Route::post('tenant/add', $nameSpace . '\tenant\TenantController@add');
	Route::post('tenant/edit/:id', $nameSpace . '\tenant\TenantController@edit');
	Route::post('tenant/delete', $nameSpace . '\tenant\TenantController@delete');
	
	// 文章分类相关路由
	Route::get('article_category/index', $nameSpace . '\ArticleCategoryController@index');
	Route::get('article_category/detail/:id', $nameSpace . '\ArticleCategoryController@detail');
	Route::post('article_category/add', $nameSpace . '\ArticleCategoryController@add');
	Route::post('article_category/edit/:id', $nameSpace . '\ArticleCategoryController@edit');
	Route::post('article_category/delete', $nameSpace . '\ArticleCategoryController@delete');
	Route::post('article_category/updateField', $nameSpace . '\ArticleCategoryController@updateField');
	
	// 文章
	Route::get('article/index', $nameSpace . '\ArticleController@index');
	Route::get('article/detail/:id', $nameSpace . '\ArticleController@detail');
	Route::post('article/add', $nameSpace . '\ArticleController@add');
	Route::post('article/edit/:id', $nameSpace . '\ArticleController@edit');
	Route::post('article/delete', $nameSpace . '\ArticleController@delete');
	Route::post('article/status/:id', $nameSpace . '\ArticleController@status');
	Route::post('article/updateField', $nameSpace . '\ArticleController@updateField');
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
//	     OperationLogMiddleware::class
     ]);


<?php
/**
 * 手动执行权限更新
 */

require_once 'vendor/autoload.php';

echo "=== 手动执行权限更新 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 手动执行关键权限更新:\n";
    
    // 定义需要更新的权限
    $updates = [
        ['id' => 2582, 'old' => 'article', 'new' => 'system:article:index'],
        ['id' => 189, 'old' => 'crm:index', 'new' => 'crm:index:index'],
        ['id' => 210, 'old' => 'crm:product', 'new' => 'crm:product:index'],
        ['id' => 4, 'old' => 'log', 'new' => 'system:log:index'],
        ['id' => 140, 'old' => 'message', 'new' => 'system:message:index'],
    ];
    
    $pdo->beginTransaction();
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($updates as $update) {
        try {
            // 先检查权限是否存在
            $stmt = $pdo->prepare("SELECT name FROM system_menu WHERE id = ?");
            $stmt->execute([$update['id']]);
            $current = $stmt->fetch();
            
            if ($current) {
                echo "  ID {$update['id']}: {$current['name']} -> {$update['new']}\n";
                
                // 执行更新
                $stmt = $pdo->prepare("UPDATE system_menu SET name = ? WHERE id = ?");
                $stmt->execute([$update['new'], $update['id']]);
                
                $successCount++;
            } else {
                echo "  ❌ ID {$update['id']}: 权限不存在\n";
                $errorCount++;
            }
        } catch (Exception $e) {
            echo "  ❌ ID {$update['id']}: 更新失败 - {$e->getMessage()}\n";
            $errorCount++;
        }
    }
    
    if ($errorCount > 0) {
        echo "\n  ⚠️ 发现 {$errorCount} 个错误，回滚事务\n";
        $pdo->rollBack();
    } else {
        $pdo->commit();
        echo "\n  ✅ 成功更新 {$successCount} 个权限\n";
    }
    
    echo "\n2. 验证更新结果:\n";
    
    // 检查格式分布
    $stmt = $pdo->prepare("
        SELECT 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END as format_type,
            COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END
    ");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
    echo "  当前格式分布:\n";
    $totalCount = 0;
    $threePartsCount = 0;
    
    foreach ($formats as $format) {
        echo "    {$format['format_type']}: {$format['count']} 个\n";
        $totalCount += $format['count'];
        if ($format['format_type'] === '三段式') {
            $threePartsCount = $format['count'];
        }
    }
    
    $unificationRate = round($threePartsCount / $totalCount * 100, 1);
    echo "  统一率: {$unificationRate}% ({$threePartsCount}/{$totalCount})\n";
    
    if ($unificationRate >= 80) {
        echo "  ✅ 权限格式基本统一\n";
    } else {
        echo "  ⚠️ 权限格式统一率较低\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 手动更新完成 ===\n";

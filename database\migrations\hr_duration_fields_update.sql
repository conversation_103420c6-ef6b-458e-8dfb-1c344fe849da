-- HR时长字段统一调整SQL脚本
-- 将所有duration字段统一为小时单位，支持0.5小时精度
-- 执行时间：2025-08-01
-- 说明：开发阶段，历史数据已清空，直接调整字段类型

-- 1. 调整出差申请表的时长字段
ALTER TABLE `hr_business_trip` 
MODIFY COLUMN `duration` decimal(6,1) NOT NULL DEFAULT 0.0 COMMENT '出差时长(小时)';

-- 2. 调整请假申请表的时长字段
ALTER TABLE `hr_leave` 
MODIFY COLUMN `duration` decimal(6,1) NOT NULL DEFAULT 0.0 COMMENT '请假时长(小时)';

-- 3. 调整外出申请表的时长字段（确保精度一致）
ALTER TABLE `hr_outing` 
MODIFY COLUMN `duration` decimal(6,1) NOT NULL DEFAULT 0.0 COMMENT '外出时长(小时)';

-- 验证字段修改结果
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN ('hr_business_trip', 'hr_leave', 'hr_outing')
    AND COLUMN_NAME = 'duration';

<?php
/**
 * 重新为测试角色分配权限
 */

require_once 'vendor/autoload.php';

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "=== 重新分配测试角色权限 ===\n\n";
    
    // 查看测试角色
    $stmt = $pdo->query("
        SELECT id, name, data_scope 
        FROM system_role 
        WHERE name IN ('部门经理', '组长', '普通员工', '自定义权限')
        ORDER BY id
    ");
    $testRoles = $stmt->fetchAll();
    
    echo "1. 测试角色列表:\n";
    foreach ($testRoles as $role) {
        echo "  - ID:{$role['id']} - {$role['name']} (数据权限:{$role['data_scope']})\n";
    }
    echo "\n";
    
    // 查看可用的菜单权限（包含CRM相关）
    $stmt = $pdo->query("
        SELECT id, parent_id, title, name, type 
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL 
        AND (
            name LIKE 'crm:%' OR 
            name LIKE 'system:permission:%' OR
            name LIKE 'project:%' OR
            name LIKE 'daily:%'
        )
        AND type IN (1, 2)
        ORDER BY parent_id, sort, id
    ");
    $availableMenus = $stmt->fetchAll();
    
    echo "2. 可分配的权限数量: " . count($availableMenus) . "\n\n";
    
    // 为不同角色分配不同的权限
    $rolePermissions = [
        '部门经理' => [
            // CRM权限 - 完整权限
            'crm:crm_customer_my:index', 'crm:crm_customer_my:add', 'crm:crm_customer_my:edit', 
            'crm:crm_customer_my:detail', 'crm:crm_customer_my:delete', 'crm:crm_customer_my:export',
            'crm:crm_lead:index', 'crm:crm_lead:add', 'crm:crm_lead:edit', 'crm:crm_lead:detail',
            'crm:crm_contact:index', 'crm:crm_contact:add', 'crm:crm_contact:edit', 'crm:crm_contact:detail',
            'crm:crm_contract:index', 'crm:crm_contract:add', 'crm:crm_contract:edit', 'crm:crm_contract:detail',
            // 项目权限
            'project:project:index', 'project:project:add', 'project:project:edit', 'project:project:detail',
            'project:project_member:index', 'project:project_member:add',
            // 每日报价权限
            'daily:daily_price_order:index', 'daily:daily_price_order:add', 'daily:daily_price_order:edit',
            // 系统权限
            'system:permission:admin:index', 'system:permission:role:index', 'system:permission:department:index'
        ],
        '组长' => [
            // CRM权限 - 部分权限
            'crm:crm_customer_my:index', 'crm:crm_customer_my:add', 'crm:crm_customer_my:edit', 'crm:crm_customer_my:detail',
            'crm:crm_lead:index', 'crm:crm_lead:add', 'crm:crm_lead:edit', 'crm:crm_lead:detail',
            'crm:crm_contact:index', 'crm:crm_contact:add', 'crm:crm_contact:edit', 'crm:crm_contact:detail',
            // 项目权限
            'project:project:index', 'project:project:detail',
            'project:project_member:index',
            // 每日报价权限
            'daily:daily_price_order:index', 'daily:daily_price_order:add'
        ],
        '普通员工' => [
            // CRM权限 - 基础权限
            'crm:crm_customer_my:index', 'crm:crm_customer_my:detail',
            'crm:crm_lead:index', 'crm:crm_lead:add', 'crm:crm_lead:detail',
            'crm:crm_contact:index', 'crm:crm_contact:detail',
            // 项目权限
            'project:project:index', 'project:project:detail',
            // 每日报价权限
            'daily:daily_price_order:index'
        ],
        '自定义权限' => [
            // CRM权限 - 自定义权限
            'crm:crm_customer_my:index', 'crm:crm_customer_my:add', 'crm:crm_customer_my:edit', 'crm:crm_customer_my:detail',
            'crm:crm_lead:index', 'crm:crm_lead:add', 'crm:crm_lead:edit', 'crm:crm_lead:detail',
            // 项目权限
            'project:project:index', 'project:project:add', 'project:project:edit', 'project:project:detail',
            // 每日报价权限
            'daily:daily_price_order:index', 'daily:daily_price_order:add', 'daily:daily_price_order:edit'
        ]
    ];
    
    // 开始分配权限
    $pdo->beginTransaction();
    
    try {
        foreach ($testRoles as $role) {
            $roleName = $role['name'];
            $roleId = $role['id'];
            
            if (!isset($rolePermissions[$roleName])) {
                continue;
            }
            
            echo "3. 为角色 '{$roleName}' 分配权限:\n";
            
            // 清除现有权限
            $stmt = $pdo->prepare("DELETE FROM system_role_menu WHERE role_id = ?");
            $stmt->execute([$roleId]);
            echo "  - 清除现有权限\n";
            
            // 分配新权限
            $permissions = $rolePermissions[$roleName];
            $assignedCount = 0;
            
            foreach ($permissions as $permissionName) {
                // 查找菜单ID
                $stmt = $pdo->prepare("SELECT id FROM system_menu WHERE name = ? AND status = 1 AND deleted_at IS NULL");
                $stmt->execute([$permissionName]);
                $menu = $stmt->fetch();
                
                if ($menu) {
                    // 插入权限分配
                    $stmt = $pdo->prepare("INSERT INTO system_role_menu (role_id, menu_id, tenant_id) VALUES (?, ?, 1)");
                    $stmt->execute([$roleId, $menu['id']]);
                    $assignedCount++;
                } else {
                    echo "    ⚠️ 权限不存在: {$permissionName}\n";
                }
            }
            
            echo "  ✅ 成功分配 {$assignedCount} 个权限\n\n";
        }
        
        $pdo->commit();
        echo "=== 权限分配完成 ===\n";
        
        // 验证分配结果
        echo "4. 验证分配结果:\n";
        foreach ($testRoles as $role) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM system_role_menu rm
                LEFT JOIN system_menu m ON rm.menu_id = m.id
                WHERE rm.role_id = ? AND m.status = 1 AND m.deleted_at IS NULL
            ");
            $stmt->execute([$role['id']]);
            $count = $stmt->fetch()['count'];
            echo "  - {$role['name']}: {$count} 个权限\n";
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "❌ 权限分配失败: " . $e->getMessage() . "\n";
        throw $e;
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}

<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 用户角色关联模型
 */
class AdminRoleModel extends BaseModel
{


    /**
     * 表名
     * @var string
     */
    protected $name = 'system_admin_role';

    /**
     * 禁用租户隔离
     * 用户角色关联表需要跨租户查询，因为用户可能在多个租户下有不同的角色
     * @var bool
     */
    protected bool $enableTenantIsolation = true;
    
    /**
     * 角色关联
     * @return BelongsTo
     */
    public function role()
    {
        return $this->belongsTo(RoleModel::class, 'role_id', 'id');
    }
    
    /**
     * 菜单关联
     * @return BelongsTo
     */
    public function menus()
    {
        return $this->belongsTo(MenuModel::class, 'menu_id', 'id');
    }
	
} 
<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\HasMany;

/**
 * 附件模型 - 物理文件表
 * 存储物理文件信息，支持去重
 */
class AttachmentModel extends BaseModel
{

    // 设置表名
    protected $name = 'system_attachment';

    // 禁用租户隔离（物理文件表不包含tenant_id）
    protected bool $enableTenantIsolation = false;

    // 设置字段信息
    protected $schema = [
        'id'            => 'int',
        'name'          => 'string',
        'path'          => 'string',
        'extension'     => 'string',
        'size'          => 'int',
        'mime_type'     => 'string',
        'storage'       => 'string',
        'storage_id'    => 'string',
        'file_md5'      => 'string',
        'file_hash'     => 'string',
        'ref_count'     => 'int',
        'storage_meta'  => 'json',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
        'deleted_at'    => 'datetime',
    ];

    // JSON字段
    protected $json = ['storage_meta'];
	

    // 字段类型转换
    protected $type = [
        'id'            => 'integer',
        'size'          => 'integer',
        'ref_count'     => 'integer',
        'storage_meta'  => 'array',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
        'deleted_at'    => 'datetime',
    ];
	
	protected $append = ['url', 'formatted_size'];

	/**
	 * 获取文件URL
	 */
	public function getUrlAttr($value, $data)
	{
		return getImgUrl($data['path'] ?? '');
	}

    /**
     * 获取文件大小（格式化）
     */
    public function getFormattedSizeAttr($value, $data): string
    {
        $size = $data['size'] ?? 0;

        if ($size < 1024) {
            return $size . 'B';
        } elseif ($size < 1024 * 1024) {
            return round($size / 1024, 2) . 'KB';
        } elseif ($size < 1024 * 1024 * 1024) {
            return round($size / (1024 * 1024), 2) . 'MB';
        } else {
            return round($size / (1024 * 1024 * 1024), 2) . 'GB';
        }
    }

    /**
     * 关联用户文件记录
     */
    public function userFiles(): HasMany
    {
        return $this->hasMany(AttachmentUserModel::class, 'attachment_id', 'id');
    }

    /**
     * 通过MD5查找现有文件
     */
    public static function findByMd5(string $storage, string $fileMd5): ?self
    {
        return self::where('storage', $storage)
            ->where('file_md5', $fileMd5)
            ->where('deleted_at', null)
            ->find();
    }

    /**
     * 通过存储ID查找现有文件（云存储etag）
     */
    public static function findByStorageId(string $storage, string $storageId): ?self
    {
        return self::where('storage', $storage)
            ->where('storage_id', $storageId)
            ->where('deleted_at', null)
            ->find();
    }

    /**
     * 增加引用计数
     */
    public function incrementRefCount(): bool
    {
        return $this->inc('ref_count')->save();
    }

    /**
     * 减少引用计数
     */
    public function decrementRefCount(): bool
    {
        return $this->dec('ref_count')->save();
    }

    /**
     * 检查是否可以删除（引用计数为0）
     */
    public function canDelete(): bool
    {
        return $this->ref_count <= 0;
    }
} 
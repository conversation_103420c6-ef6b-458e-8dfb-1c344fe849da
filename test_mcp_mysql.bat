@echo off
echo Testing MCP MySQL Server Configuration...

set MYSQL_HOST=*************
set MYSQL_PORT=3306
set MYSQL_USER=www_bs_com
set MYSQL_PASSWORD=PdadjMXmNy8Pn9tj
set MYSQL_DATABASE=www_bs_com

echo Environment variables set:
echo MYSQL_HOST=%MYSQL_HOST%
echo MYSQL_PORT=%MYSQL_PORT%
echo MYSQL_USER=%MYSQL_USER%
echo MYSQL_DATABASE=%MYSQL_DATABASE%

echo.
echo Testing MCP MySQL Server import...
C:\ProgramData\anaconda3\envs\python312\python.exe -c "import mcp_server_mysql; print('✅ MCP MySQL服务器配置正确')"

echo.
echo Testing database connection...
C:\ProgramData\anaconda3\envs\python312\python.exe -c "
import os
import mysql.connector
try:
    conn = mysql.connector.connect(
        host=os.getenv('MYSQL_HOST'),
        port=int(os.getenv('MYSQL_PORT')),
        user=os.getenv('MYSQL_USER'),
        password=os.getenv('MYSQL_PASSWORD'),
        database=os.getenv('MYSQL_DATABASE')
    )
    print('✅ 数据库连接成功')
    conn.close()
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
"

pause

<?php
/**
 * 修复租户1用户权限问题
 */

require_once 'vendor/autoload.php';

echo "=== 修复租户1用户权限 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取租户1用户和角色:\n";
    
    // 获取租户1用户
    $stmt = $pdo->prepare("
        SELECT id, username, email, status
        FROM system_admin 
        WHERE tenant_id = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    echo "  租户1用户: " . count($users) . " 个\n";
    foreach ($users as $user) {
        echo "    ID:{$user['id']} {$user['username']}\n";
    }
    
    // 获取租户1角色
    $stmt = $pdo->prepare("
        SELECT id, name, status
        FROM system_role 
        WHERE tenant_id = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $roles = $stmt->fetchAll();
    
    echo "  租户1角色: " . count($roles) . " 个\n";
    foreach ($roles as $role) {
        echo "    ID:{$role['id']} {$role['name']}\n";
    }
    
    echo "\n2. 检查用户角色分配:\n";
    
    foreach ($users as $user) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM system_admin_role 
            WHERE admin_id = ?
        ");
        $stmt->execute([$user['id']]);
        $roleCount = $stmt->fetch()['count'];
        
        echo "  用户 {$user['username']} (ID:{$user['id']}): {$roleCount} 个角色\n";
    }
    
    echo "\n3. 检查角色权限分配:\n";
    
    foreach ($roles as $role) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM system_role_menu rm
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE rm.role_id = ? AND m.status = 1 AND m.deleted_at IS NULL
        ");
        $stmt->execute([$role['id']]);
        $permissionCount = $stmt->fetch()['count'];
        
        echo "  角色 {$role['name']} (ID:{$role['id']}): {$permissionCount} 个权限\n";
    }
    
    echo "\n4. 开始修复权限:\n";
    
    // 获取所有有效权限
    $stmt = $pdo->prepare("
        SELECT id, name, title
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $allPermissions = $stmt->fetchAll();
    
    echo "  系统总权限数: " . count($allPermissions) . " 个\n";
    
    $pdo->beginTransaction();
    
    try {
        // 为每个角色分配所有权限
        foreach ($roles as $role) {
            echo "  为角色 {$role['name']} 分配权限...\n";
            
            // 先清除现有权限
            $stmt = $pdo->prepare("DELETE FROM system_role_menu WHERE role_id = ?");
            $stmt->execute([$role['id']]);
            
            // 分配所有权限
            $assignedCount = 0;
            foreach ($allPermissions as $permission) {
                $stmt = $pdo->prepare("
                    INSERT INTO system_role_menu (role_id, menu_id, created_at, updated_at) 
                    VALUES (?, ?, NOW(), NOW())
                ");
                $stmt->execute([$role['id'], $permission['id']]);
                $assignedCount++;
            }
            
            echo "    ✅ 已分配 {$assignedCount} 个权限\n";
        }
        
        // 确保所有用户都有角色
        foreach ($users as $user) {
            // 检查用户是否有角色
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count
                FROM system_admin_role 
                WHERE admin_id = ?
            ");
            $stmt->execute([$user['id']]);
            $userRoleCount = $stmt->fetch()['count'];
            
            if ($userRoleCount == 0 && !empty($roles)) {
                // 为用户分配第一个角色（通常是超级管理员角色）
                $defaultRole = $roles[0];
                $stmt = $pdo->prepare("
                    INSERT INTO system_admin_role (admin_id, role_id, created_at, updated_at) 
                    VALUES (?, ?, NOW(), NOW())
                ");
                $stmt->execute([$user['id'], $defaultRole['id']]);
                
                echo "  ✅ 为用户 {$user['username']} 分配角色 {$defaultRole['name']}\n";
            }
        }
        
        $pdo->commit();
        echo "\n  ✅ 权限修复完成！\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
    echo "\n5. 验证修复结果:\n";
    
    foreach ($users as $user) {
        // 计算用户权限数
        $stmt = $pdo->prepare("
            SELECT COUNT(DISTINCT m.id) as count
            FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = ? AND m.status = 1 AND m.deleted_at IS NULL
        ");
        $stmt->execute([$user['id']]);
        $userPermissionCount = $stmt->fetch()['count'];
        
        echo "  用户 {$user['username']}: {$userPermissionCount} 个权限\n";
    }
    
    // 特别检查tenant_admin用户
    $stmt = $pdo->prepare("
        SELECT COUNT(DISTINCT m.id) as count
        FROM system_admin_role ar
        LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
        LEFT JOIN system_menu m ON rm.menu_id = m.id
        WHERE ar.admin_id = 201 AND m.status = 1 AND m.deleted_at IS NULL
    ");
    $stmt->execute();
    $tenantAdminPermissions = $stmt->fetch()['count'];
    
    echo "\n  🎯 tenant_admin用户权限数: {$tenantAdminPermissions} 个\n";
    
    if ($tenantAdminPermissions > 0) {
        echo "  ✅ tenant_admin用户权限修复成功！\n";
    } else {
        echo "  ❌ tenant_admin用户权限仍有问题\n";
    }
    
    echo "\n6. 建议的下一步:\n";
    echo "  1. 重新登录系统测试功能\n";
    echo "  2. 检查各模块的访问权限\n";
    echo "  3. 根据实际需要调整用户角色分配\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 权限修复完成 ===\n";

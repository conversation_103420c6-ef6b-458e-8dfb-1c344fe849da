#!/usr/bin/env python3
"""
检查tenant_admin角色和用户配置
"""
import os
import mysql.connector

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

def get_db_connection():
    return mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )

def main():
    print("=== 检查tenant_admin配置 ===\n")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 1. 检查是否有tenant_admin角色
    print("1. 检查tenant_admin角色:")
    cursor.execute("""
        SELECT id, name, remark, status
        FROM system_role
        WHERE name LIKE '%admin%' OR name LIKE '%管理%'
        ORDER BY name
    """)
    
    admin_roles = cursor.fetchall()
    print(f"  管理员相关角色:")
    tenant_admin_role_id = None
    for role in admin_roles:
        role_id, name, desc, status = role
        print(f"    ID:{role_id} - {name} ({desc}) - 状态:{status}")
        if 'tenant_admin' in name.lower() or '租户管理员' in name:
            tenant_admin_role_id = role_id
    
    # 2. 检查是否有tenant_admin用户
    print(f"\n2. 检查tenant_admin用户:")
    cursor.execute("""
        SELECT id, username, real_name, status, tenant_id
        FROM system_admin 
        WHERE username LIKE '%admin%' OR real_name LIKE '%管理%'
        ORDER BY username
    """)
    
    admin_users = cursor.fetchall()
    print(f"  管理员相关用户:")
    tenant_admin_user_id = None
    for user in admin_users:
        user_id, username, real_name, status, tenant_id = user
        print(f"    ID:{user_id} - {username} ({real_name}) - 租户:{tenant_id} - 状态:{status}")
        if 'tenant_admin' in username.lower() or '租户管理员' in real_name:
            tenant_admin_user_id = user_id
    
    # 3. 检查超级管理员权限
    print(f"\n3. 检查超级管理员权限:")
    if tenant_admin_role_id:
        cursor.execute("""
            SELECT COUNT(*) 
            FROM system_role_menu 
            WHERE role_id = %s
        """, (tenant_admin_role_id,))
        permission_count = cursor.fetchone()[0]
        print(f"  租户管理员角色权限数: {permission_count} 个")
        
        if permission_count > 0:
            cursor.execute("""
                SELECT m.name, m.title, m.type
                FROM system_role_menu rm
                LEFT JOIN system_menu m ON rm.menu_id = m.id
                WHERE rm.role_id = %s AND m.status = 1 AND m.deleted_at IS NULL
                ORDER BY m.name
                LIMIT 10
            """, (tenant_admin_role_id,))
            
            permissions = cursor.fetchall()
            print(f"  权限示例 (前10个):")
            for perm in permissions:
                name, title, perm_type = perm
                type_name = "菜单" if perm_type == 1 else "按钮"
                print(f"    {name} ({title}) - {type_name}")
    else:
        print(f"  ❌ 未找到租户管理员角色")
    
    # 4. 检查所有权限总数
    print(f"\n4. 系统权限统计:")
    cursor.execute("""
        SELECT COUNT(*) FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
    """)
    total_permissions = cursor.fetchone()[0]
    print(f"  系统总权限数: {total_permissions} 个")
    
    cursor.execute("""
        SELECT COUNT(*) FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL AND type = 1
    """)
    menu_permissions = cursor.fetchone()[0]
    print(f"  菜单权限数: {menu_permissions} 个")
    
    cursor.execute("""
        SELECT COUNT(*) FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL AND type = 2
    """)
    button_permissions = cursor.fetchone()[0]
    print(f"  按钮权限数: {button_permissions} 个")
    
    # 5. 检查权限模块分布
    print(f"\n5. 权限模块分布:")
    cursor.execute("""
        SELECT 
            SUBSTRING_INDEX(name, ':', 1) as module,
            COUNT(*) as count,
            SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as menu_count,
            SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as button_count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL AND name LIKE '%:%'
        GROUP BY SUBSTRING_INDEX(name, ':', 1)
        ORDER BY count DESC
    """)
    
    modules = cursor.fetchall()
    for module in modules:
        module_name, total, menu_count, button_count = module
        print(f"  {module_name}: {total}个权限 (菜单:{menu_count}, 按钮:{button_count})")
    
    cursor.close()
    conn.close()
    
    print(f"\n=== 检查完成 ===")
    
    # 返回检查结果
    return {
        'tenant_admin_role_id': tenant_admin_role_id,
        'tenant_admin_user_id': tenant_admin_user_id,
        'total_permissions': total_permissions,
        'need_create_tenant_admin': tenant_admin_role_id is None or tenant_admin_user_id is None
    }

if __name__ == "__main__":
    result = main()
    if result['need_create_tenant_admin']:
        print(f"\n⚠️ 需要创建tenant_admin角色和用户配置")
    else:
        print(f"\n✅ tenant_admin配置已存在")

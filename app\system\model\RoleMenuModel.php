<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use think\model\relation\BelongsTo;

/**
 * 角色菜单关联模型
 */
class RoleMenuModel extends BaseModel
{


    /**
     * 表名
     * @var string
     */
    protected $name = 'system_role_menu';

    /**
     * 禁用租户隔离
     * 角色菜单关联表需要跨租户查询，因为菜单是全局共享的
     * @var bool
     */
    protected bool $enableTenantIsolation = true;
    
    /**
     * 角色关联
     * @return BelongsTo
     */
    public function role()
    {
        return $this->belongsTo(RoleModel::class, 'role_id', 'id');
    }
    
    /**
     * 菜单关联
     * @return BelongsTo
     */
    public function menu()
    {
        return $this->belongsTo(MenuModel::class, 'menu_id', 'id');
    }
} 
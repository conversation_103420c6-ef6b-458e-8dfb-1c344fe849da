<?php
/**
 * 调试权限问题
 */

require_once 'vendor/autoload.php';

echo "=== 调试权限问题 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 检查tenant_admin用户权限:\n";
    
    // 检查用户基本信息
    $stmt = $pdo->prepare("
        SELECT a.id, a.username, a.real_name, r.name as role_name 
        FROM system_admin a 
        LEFT JOIN system_admin_role ar ON a.id = ar.admin_id 
        LEFT JOIN system_role r ON ar.role_id = r.id 
        WHERE a.username = 'tenant_admin' AND a.tenant_id = 1
    ");
    $stmt->execute();
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "  ❌ tenant_admin用户不存在\n";
        exit;
    }
    
    echo "  ✅ 用户信息: {$user['real_name']} (ID: {$user['id']}) - 角色: {$user['role_name']}\n";
    
    // 检查用户权限数量
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM system_admin_role ar
        LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
        LEFT JOIN system_menu m ON rm.menu_id = m.id
        WHERE ar.admin_id = ? AND m.status = 1 AND m.deleted_at IS NULL
    ");
    $stmt->execute([$user['id']]);
    $permissionCount = $stmt->fetch()['count'];
    
    echo "  ✅ 权限总数: {$permissionCount} 个\n";
    
    // 检查具体权限
    $stmt = $pdo->prepare("
        SELECT m.name, m.title, m.type
        FROM system_admin_role ar
        LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
        LEFT JOIN system_menu m ON rm.menu_id = m.id
        WHERE ar.admin_id = ? AND m.status = 1 AND m.deleted_at IS NULL
        ORDER BY m.name
        LIMIT 10
    ");
    $stmt->execute([$user['id']]);
    $permissions = $stmt->fetchAll();
    
    echo "  权限示例 (前10个):\n";
    foreach ($permissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "    {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n2. 测试权限解析逻辑:\n";
    
    // 模拟权限解析
    function parsePermissionInfo($ruleName) {
        // 分割类路径和方法名
        [$classPath, $method] = explode('@', $ruleName);
        
        // 分割类路径为命名空间部分
        $parts = explode('\\', $classPath);
        
        // 提取模块名（app\模块\controller\...）
        $module = strtolower($parts[1]); // 第二个部分是模块名
        
        // 提取控制器名（最后一个命名空间部分）
        $controllerClass = $parts[count($parts) - 1];
        
        // 去掉Controller后缀，转换为小写
        $controller = strtolower(str_replace('Controller', '', $controllerClass));
        
        // 处理子目录情况（如：permission/admin, log/login）
        if (count($parts) > 4) {
            // 有子目录，格式：app\system\controller\permission\AdminController
            $subPath = strtolower($parts[3]); // 子目录名
            $controller = $subPath . ':' . $controller;
        }
        
        return "{$module}:{$controller}:{$method}";
    }
    
    // 测试常见路由
    $testRoutes = [
        'app\\system\\controller\\permission\\AdminController@index',
        'app\\crm\\controller\\CrmCustomerMyController@index',
        'app\\project\\controller\\ProjectController@index',
        'app\\system\\controller\\AuthController@login',
    ];
    
    foreach ($testRoutes as $route) {
        $parsed = parsePermissionInfo($route);
        echo "  路由: {$route}\n";
        echo "  解析: {$parsed}\n";
        
        // 检查权限是否存在
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM system_menu 
            WHERE name = ? AND status = 1 AND deleted_at IS NULL
        ");
        $stmt->execute([$parsed]);
        $exists = $stmt->fetch()['count'] > 0;
        
        echo "  存在: " . ($exists ? '✅' : '❌') . "\n";
        
        if (!$exists) {
            // 查找相似权限
            $stmt = $pdo->prepare("
                SELECT name 
                FROM system_menu 
                WHERE name LIKE ? AND status = 1 AND deleted_at IS NULL 
                LIMIT 3
            ");
            $stmt->execute(["%{$parsed}%"]);
            $similar = $stmt->fetchAll();
            
            if ($similar) {
                echo "  相似权限:\n";
                foreach ($similar as $sim) {
                    echo "    - {$sim['name']}\n";
                }
            }
        }
        echo "\n";
    }
    
    echo "3. 检查权限中间件配置:\n";
    
    // 检查路由文件中的中间件配置
    $routeFiles = [
        'route/System.php',
        'route/Auth.php',
        'route/Router.php'
    ];
    
    foreach ($routeFiles as $routeFile) {
        if (file_exists($routeFile)) {
            $content = file_get_contents($routeFile);
            $hasPermissionMiddleware = strpos($content, 'PermissionMiddleware') !== false;
            $hasTokenMiddleware = strpos($content, 'TokenAuthMiddleware') !== false;
            
            echo "  {$routeFile}:\n";
            echo "    权限中间件: " . ($hasPermissionMiddleware ? '✅' : '❌') . "\n";
            echo "    Token中间件: " . ($hasTokenMiddleware ? '✅' : '❌') . "\n";
        }
    }
    
    echo "\n4. 建议的解决方案:\n";
    echo "  1. 检查PermissionService.php中的parsePermissionInfo方法\n";
    echo "  2. 确认权限标识格式是否与数据库匹配\n";
    echo "  3. 检查路由中间件配置是否正确\n";
    echo "  4. 清除权限相关缓存\n";
    echo "  5. 检查前端请求的路由格式\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

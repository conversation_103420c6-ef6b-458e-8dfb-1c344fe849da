<?php
/**
 * 分析剩余未统一的权限
 */

require_once 'vendor/autoload.php';

echo "=== 分析剩余未统一的权限 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取所有非三段式权限:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        AND (
            name NOT LIKE '%:%:%' OR 
            name LIKE '%:%:%:%'
        )
        ORDER BY name
    ");
    $stmt->execute();
    $remainingPermissions = $stmt->fetchAll();
    
    echo "  剩余未统一权限: " . count($remainingPermissions) . " 个\n\n";
    
    // 按格式分类
    $formatGroups = [
        'four_parts' => [],
        'two_parts' => [],
        'single_word' => [],
        'other' => []
    ];
    
    foreach ($remainingPermissions as $perm) {
        $parts = explode(':', $perm['name']);
        
        if (count($parts) == 4) {
            $formatGroups['four_parts'][] = $perm;
        } elseif (count($parts) == 2) {
            $formatGroups['two_parts'][] = $perm;
        } elseif (count($parts) == 1) {
            $formatGroups['single_word'][] = $perm;
        } else {
            $formatGroups['other'][] = $perm;
        }
    }
    
    echo "2. 按格式分类:\n";
    foreach ($formatGroups as $format => $perms) {
        if (!empty($perms)) {
            echo "  {$format}: " . count($perms) . " 个\n";
            foreach ($perms as $perm) {
                echo "    - ID:{$perm['id']} {$perm['name']} ({$perm['title']})\n";
            }
            echo "\n";
        }
    }
    
    echo "3. 生成统一SQL:\n";
    
    $updateSqls = [];
    
    foreach ($remainingPermissions as $perm) {
        $oldName = $perm['name'];
        $newName = generateUnifiedName($oldName);
        
        if ($oldName !== $newName) {
            $updateSqls[] = [
                'id' => $perm['id'],
                'old_name' => $oldName,
                'new_name' => $newName,
                'sql' => "UPDATE system_menu SET name = '{$newName}' WHERE id = {$perm['id']}; -- {$oldName} -> {$newName}"
            ];
        }
    }
    
    echo "  需要更新的权限: " . count($updateSqls) . " 个\n\n";
    
    if (!empty($updateSqls)) {
        echo "  更新预览:\n";
        foreach ($updateSqls as $update) {
            echo "    {$update['old_name']} -> {$update['new_name']}\n";
        }
        
        // 生成SQL脚本
        $sqlScript = "-- 完善权限统一SQL脚本\n";
        $sqlScript .= "-- 生成时间: " . date('Y-m-d H:i:s') . "\n";
        $sqlScript .= "-- 更新数量: " . count($updateSqls) . " 个权限\n\n";
        
        $sqlScript .= "START TRANSACTION;\n\n";
        
        foreach ($updateSqls as $update) {
            $sqlScript .= $update['sql'] . "\n";
        }
        
        $sqlScript .= "\nCOMMIT;\n";
        
        file_put_contents('complete_permission_unification.sql', $sqlScript);
        echo "\n  ✅ SQL脚本已生成: complete_permission_unification.sql\n";
    } else {
        echo "  ✅ 所有权限已经统一，无需更新\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

function generateUnifiedName($oldName) {
    $parts = explode(':', $oldName);
    
    // 单词权限 -> system:word:index
    if (count($parts) == 1) {
        return "system:{$parts[0]}:index";
    }
    
    // 二段式权限 -> module:controller:index
    if (count($parts) == 2) {
        return "{$parts[0]}:{$parts[1]}:index";
    }
    
    // 四段式权限 -> module:sub_controller:action
    if (count($parts) == 4) {
        $module = $parts[0];
        $sub = $parts[1];
        $controller = $parts[2];
        $action = $parts[3];
        
        return "{$module}:{$sub}_{$controller}:{$action}";
    }
    
    // 其他格式保持不变
    return $oldName;
}

echo "\n=== 分析完成 ===\n";

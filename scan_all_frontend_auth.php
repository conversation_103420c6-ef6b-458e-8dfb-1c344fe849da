<?php
/**
 * 扫描所有前端文件中的权限按钮配置
 */

echo "=== 扫描所有前端权限按钮配置 ===\n\n";

function scanDirectory($dir, $pattern = '*.vue') {
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && fnmatch($pattern, $file->getFilename())) {
            $files[] = $file->getPathname();
        }
    }
    
    return $files;
}

function extractAuthFromFile($filePath) {
    $content = file_get_contents($filePath);
    $lines = explode("\n", $content);
    $authButtons = [];
    
    foreach ($lines as $lineNum => $line) {
        // 匹配 v-auth="权限名称" 或 auth="权限名称"
        if (preg_match_all('/(?:v-auth|auth)\s*=\s*["\']([^"\']+)["\']/', $line, $matches, PREG_OFFSET_CAPTURE)) {
            foreach ($matches[1] as $match) {
                $authName = $match[0];
                $authButtons[] = [
                    'file' => basename($filePath),
                    'line' => $lineNum + 1,
                    'auth' => $authName,
                    'context' => trim($line)
                ];
            }
        }
    }
    
    return $authButtons;
}

// 扫描前端目录
$frontendDir = 'frontend/src/views';
if (!is_dir($frontendDir)) {
    echo "❌ 前端目录不存在: {$frontendDir}\n";
    exit(1);
}

echo "1. 扫描前端Vue文件:\n";

$vueFiles = scanDirectory($frontendDir);
echo "  找到Vue文件: " . count($vueFiles) . " 个\n\n";

$allAuthButtons = [];
$fileStats = [];

foreach ($vueFiles as $file) {
    $authButtons = extractAuthFromFile($file);
    if (!empty($authButtons)) {
        $relativePath = str_replace('frontend/src/views/', '', $file);
        $fileStats[$relativePath] = count($authButtons);
        $allAuthButtons = array_merge($allAuthButtons, $authButtons);
    }
}

echo "2. 权限按钮统计:\n";
echo "  包含权限按钮的文件: " . count($fileStats) . " 个\n";
echo "  权限按钮总数: " . count($allAuthButtons) . " 个\n\n";

echo "3. 按文件分组的权限按钮:\n";
foreach ($fileStats as $file => $count) {
    echo "  📁 {$file}: {$count} 个权限按钮\n";
}

echo "\n4. 所有权限按钮详情:\n";

// 按模块分组
$moduleGroups = [];
foreach ($allAuthButtons as $button) {
    $parts = explode(':', $button['auth']);
    $module = $parts[0] ?? 'unknown';
    
    if (!isset($moduleGroups[$module])) {
        $moduleGroups[$module] = [];
    }
    $moduleGroups[$module][] = $button;
}

foreach ($moduleGroups as $module => $buttons) {
    echo "\n  🔧 {$module}模块: " . count($buttons) . " 个权限按钮\n";
    
    foreach ($buttons as $button) {
        echo "    {$button['file']}:{$button['line']} - {$button['auth']}\n";
        echo "      上下文: " . substr($button['context'], 0, 80) . "...\n";
    }
}

echo "\n5. 权限格式分析:\n";

$formatStats = [
    'three_parts' => 0,    // module:controller:action
    'four_parts' => 0,     // module:sub:controller:action
    'two_parts' => 0,      // module:controller
    'other' => 0
];

$needsUpdate = [];

foreach ($allAuthButtons as $button) {
    $parts = explode(':', $button['auth']);
    $partCount = count($parts);
    
    if ($partCount == 3) {
        $formatStats['three_parts']++;
        
        // 检查是否需要转换下划线格式
        if (strpos($button['auth'], ':') !== false) {
            // 可能需要转换的格式
            $converted = convertToUnderscoreFormat($button['auth']);
            if ($converted !== $button['auth']) {
                $needsUpdate[] = [
                    'file' => $button['file'],
                    'line' => $button['line'],
                    'old_auth' => $button['auth'],
                    'new_auth' => $converted,
                    'context' => $button['context']
                ];
            }
        }
    } elseif ($partCount == 4) {
        $formatStats['four_parts']++;
        
        // 四段式需要转换为三段式
        $converted = convertFourPartsToThree($button['auth']);
        $needsUpdate[] = [
            'file' => $button['file'],
            'line' => $button['line'],
            'old_auth' => $button['auth'],
            'new_auth' => $converted,
            'context' => $button['context']
        ];
    } elseif ($partCount == 2) {
        $formatStats['two_parts']++;
    } else {
        $formatStats['other']++;
    }
}

foreach ($formatStats as $format => $count) {
    echo "  {$format}: {$count} 个\n";
}

echo "\n6. 需要更新的权限按钮:\n";
echo "  需要更新总数: " . count($needsUpdate) . " 个\n\n";

if (!empty($needsUpdate)) {
    $updateByFile = [];
    foreach ($needsUpdate as $update) {
        $updateByFile[$update['file']][] = $update;
    }
    
    foreach ($updateByFile as $file => $updates) {
        echo "  📁 {$file}: " . count($updates) . " 处需要更新\n";
        foreach ($updates as $update) {
            echo "    第{$update['line']}行: {$update['old_auth']} → {$update['new_auth']}\n";
        }
        echo "\n";
    }
}

echo "7. 工作量评估:\n";
$totalFiles = count($updateByFile ?? []);
$totalUpdates = count($needsUpdate);

echo "  📁 需要修改的文件: {$totalFiles} 个\n";
echo "  🔧 需要更新的权限按钮: {$totalUpdates} 个\n";
echo "  ⏱️ 预估修改时间: " . ($totalFiles * 10 + $totalUpdates * 3) . " 分钟\n";
echo "  🧪 测试验证时间: " . ($totalFiles * 15) . " 分钟\n";
echo "  📝 总计时间: " . ($totalFiles * 25 + $totalUpdates * 3) . " 分钟\n";

echo "\n8. 实施计划:\n";
echo "  阶段1: 备份所有需要修改的文件\n";
echo "  阶段2: 批量更新权限按钮配置\n";
echo "  阶段3: 逐个页面测试权限控制\n";
echo "  阶段4: 验证按钮显示/隐藏逻辑\n";

// 转换函数
function convertToUnderscoreFormat($auth) {
    $parts = explode(':', $auth);
    if (count($parts) == 3) {
        $module = $parts[0];
        $controller = $parts[1];
        $action = $parts[2];
        
        // 将controller部分的驼峰转换为下划线
        $controller = preg_replace('/([a-z])([A-Z])/', '$1_$2', $controller);
        $controller = strtolower($controller);
        
        // 将action部分的驼峰转换为下划线
        $action = preg_replace('/([a-z])([A-Z])/', '$1_$2', $action);
        $action = strtolower($action);
        
        return "{$module}:{$controller}:{$action}";
    }
    
    return $auth;
}

function convertFourPartsToThree($auth) {
    $parts = explode(':', $auth);
    if (count($parts) == 4) {
        $module = $parts[0];
        $sub = $parts[1];
        $controller = $parts[2];
        $action = $parts[3];
        
        return "{$module}:{$sub}_{$controller}:{$action}";
    }
    
    return $auth;
}

echo "\n=== 扫描完成 ===\n";

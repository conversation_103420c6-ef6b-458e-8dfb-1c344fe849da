-- =====================================================
-- 权限测试执行脚本 - 完整版
-- 执行日期：2025-01-31
-- 说明：一键执行完整的权限测试数据创建和验证
-- =====================================================

-- =====================================================
-- 第一步：清理现有测试数据
-- =====================================================
SELECT '=== 开始清理租户ID=1的现有测试数据 ===' as message;

DELETE FROM system_role_menu WHERE tenant_id = 1;
DELETE FROM system_admin_role WHERE tenant_id = 1;
DELETE FROM system_admin WHERE tenant_id = 1;
DELETE FROM system_role WHERE tenant_id = 1;
DELETE FROM system_dept WHERE tenant_id = 1;

SELECT '=== 数据清理完成 ===' as message;

-- =====================================================
-- 第二步：创建测试部门结构
-- =====================================================
SELECT '=== 开始创建测试部门结构 ===' as message;

INSERT INTO `system_dept` (`id`, `parent_id`, `name`, `code`, `leader_name`, `phone`, `email`, `sort`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `tenant_id`) VALUES
(101, 0, '总公司', 'HQ', '张总', '13800000001', '<EMAIL>', 1, 1, '租户1总公司', 1, NOW(), NOW(), 1),
(102, 101, '销售部', 'SALES', '李经理', '13800000002', '<EMAIL>', 1, 1, '销售部门', 1, NOW(), NOW(), 1),
(103, 102, '销售一组', 'SALES1', '王组长', '13800000003', '<EMAIL>', 1, 1, '销售一组', 1, NOW(), NOW(), 1),
(104, 102, '销售二组', 'SALES2', '赵组长', '13800000004', '<EMAIL>', 2, 1, '销售二组', 1, NOW(), NOW(), 1),
(105, 101, '技术部', 'TECH', '刘经理', '13800000005', '<EMAIL>', 2, 1, '技术部门', 1, NOW(), NOW(), 1),
(106, 105, '前端组', 'FRONTEND', '陈组长', '13800000006', '<EMAIL>', 1, 1, '前端开发组', 1, NOW(), NOW(), 1),
(107, 105, '后端组', 'BACKEND', '周组长', '13800000007', '<EMAIL>', 2, 1, '后端开发组', 1, NOW(), NOW(), 1),
(108, 101, '财务部', 'FINANCE', '孙经理', '13800000008', '<EMAIL>', 3, 1, '财务部门', 1, NOW(), NOW(), 1);

SELECT '=== 部门创建完成 ===' as message;

-- =====================================================
-- 第三步：创建测试角色
-- =====================================================
SELECT '=== 开始创建测试角色 ===' as message;

INSERT INTO `system_role` (`id`, `name`, `sort`, `data_scope`, `data_scope_dept_ids`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `tenant_id`) VALUES
(101, '租户超级管理员', 1, 1, '[]', 1, '租户内全部数据权限', 1, NOW(), NOW(), 1),
(102, '部门经理', 2, 3, '[]', 1, '本部门及以下数据权限', 1, NOW(), NOW(), 1),
(103, '组长', 3, 2, '[]', 1, '本部门数据权限', 1, NOW(), NOW(), 1),
(104, '普通员工', 4, 4, '[]', 1, '仅本人数据权限', 1, NOW(), NOW(), 1),
(105, '自定义权限', 5, 5, '[102,105]', 1, '自定义部门权限：销售部+技术部', 1, NOW(), NOW(), 1);

SELECT '=== 角色创建完成 ===' as message;

-- =====================================================
-- 第四步：创建测试用户
-- =====================================================
SELECT '=== 开始创建测试用户 ===' as message;

INSERT INTO `system_admin` (`id`, `username`, `password`, `salt`, `real_name`, `avatar`, `gender`, `email`, `mobile`, `is_super_admin`, `dept_id`, `post_ids`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `tenant_id`) VALUES
(201, 'tenant_admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '租户管理员', '', 1, '<EMAIL>', '13900000001', 0, 102, '', 1, '租户1超级管理员', 1, NOW(), NOW(), 1),
(202, 'sales_manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '李经理', '', 1, '<EMAIL>', '13900000002', 0, 102, '', 1, '销售部经理', 1, NOW(), NOW(), 1),
(203, 'sales_leader1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '王组长', '', 1, '<EMAIL>', '13900000003', 0, 103, '', 1, '销售一组组长', 1, NOW(), NOW(), 1),
(204, 'sales_staff1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '张员工', '', 2, '<EMAIL>', '13900000004', 0, 103, '', 1, '销售一组员工', 1, NOW(), NOW(), 1),
(205, 'sales_staff2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '赵员工', '', 2, '<EMAIL>', '13900000005', 0, 104, '', 1, '销售二组员工', 1, NOW(), NOW(), 1),
(206, 'tech_manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '刘经理', '', 1, '<EMAIL>', '13900000006', 0, 105, '', 1, '技术部经理', 1, NOW(), NOW(), 1),
(207, 'tech_staff', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '陈开发', '', 1, '<EMAIL>', '13900000007', 0, 106, '', 1, '前端开发', 1, NOW(), NOW(), 1),
(208, 'finance_staff', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '孙会计', '', 2, '<EMAIL>', '13900000008', 0, 108, '', 1, '财务人员', 1, NOW(), NOW(), 1),
(209, 'custom_user', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '周测试', '', 1, '<EMAIL>', '13900000009', 0, 105, '', 1, '自定义权限测试用户', 1, NOW(), NOW(), 1);

SELECT '=== 用户创建完成 ===' as message;

-- =====================================================
-- 第五步：分配用户角色关系
-- =====================================================
SELECT '=== 开始分配用户角色关系 ===' as message;

INSERT INTO `system_admin_role` (`admin_id`, `role_id`, `creator_id`, `created_at`, `updated_at`, `tenant_id`) VALUES
(201, 101, 1, NOW(), NOW(), 1), -- 租户管理员 -> 超级管理员
(202, 102, 1, NOW(), NOW(), 1), -- 销售经理 -> 部门经理
(203, 103, 1, NOW(), NOW(), 1), -- 销售组长 -> 组长
(204, 104, 1, NOW(), NOW(), 1), -- 销售员工1 -> 普通员工
(205, 104, 1, NOW(), NOW(), 1), -- 销售员工2 -> 普通员工
(206, 102, 1, NOW(), NOW(), 1), -- 技术经理 -> 部门经理
(207, 104, 1, NOW(), NOW(), 1), -- 技术员工 -> 普通员工
(208, 104, 1, NOW(), NOW(), 1), -- 财务员工 -> 普通员工
(209, 105, 1, NOW(), NOW(), 1); -- 自定义用户 -> 自定义权限

SELECT '=== 用户角色关系分配完成 ===' as message;

-- =====================================================
-- 第六步：配置角色菜单权限（核心权限）
-- =====================================================
SELECT '=== 开始配置角色菜单权限 ===' as message;

-- 租户超级管理员权限（role_id=101）- 全部权限
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- 权限管理模块
(101, 1, 1, NOW(), NOW()),   -- 权限管理主菜单
(101, 3, 1, NOW(), NOW()),   -- 管理员管理菜单
(101, 37, 1, NOW(), NOW()),  -- 列表
(101, 16, 1, NOW(), NOW()),  -- 新增
(101, 17, 1, NOW(), NOW()),  -- 编辑
(101, 18, 1, NOW(), NOW()),  -- 删除
(101, 38, 1, NOW(), NOW()),  -- 详情
(101, 31, 1, NOW(), NOW()),  -- 重置密码
(101, 6, 1, NOW(), NOW()),   -- 角色管理菜单
(101, 19, 1, NOW(), NOW()),  -- 新增角色
(101, 20, 1, NOW(), NOW()),  -- 编辑角色
(101, 21, 1, NOW(), NOW()),  -- 删除角色
(101, 36, 1, NOW(), NOW()),  -- 角色详情
-- CRM模块
(101, 189, 1, NOW(), NOW()), -- 客户管理主菜单
(101, 192, 1, NOW(), NOW()), -- 我的客户菜单
(101, 2420, 1, NOW(), NOW()), -- 新增客户
(101, 2421, 1, NOW(), NOW()), -- 编辑客户
(101, 2422, 1, NOW(), NOW()), -- 删除客户
(101, 2423, 1, NOW(), NOW()), -- 客户详情
(101, 2424, 1, NOW(), NOW()), -- 导入客户
(101, 2425, 1, NOW(), NOW()), -- 导出客户
(101, 2426, 1, NOW(), NOW()), -- 转移客户
(101, 2432, 1, NOW(), NOW()); -- 共享客户

-- 部门经理权限（role_id=102）- 管理权限
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(102, 3, 1, NOW(), NOW()),   -- 管理员管理菜单
(102, 37, 1, NOW(), NOW()),  -- 列表
(102, 17, 1, NOW(), NOW()),  -- 编辑
(102, 38, 1, NOW(), NOW()),  -- 详情
(102, 189, 1, NOW(), NOW()), -- 客户管理主菜单
(102, 192, 1, NOW(), NOW()), -- 我的客户菜单
(102, 2420, 1, NOW(), NOW()), -- 新增客户
(102, 2421, 1, NOW(), NOW()), -- 编辑客户
(102, 2423, 1, NOW(), NOW()), -- 客户详情
(102, 2425, 1, NOW(), NOW()), -- 导出客户
(102, 2426, 1, NOW(), NOW()); -- 转移客户

-- 组长权限（role_id=103）- 基础操作权限
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(103, 3, 1, NOW(), NOW()),   -- 管理员管理菜单
(103, 37, 1, NOW(), NOW()),  -- 列表
(103, 38, 1, NOW(), NOW()),  -- 详情
(103, 189, 1, NOW(), NOW()), -- 客户管理主菜单
(103, 192, 1, NOW(), NOW()), -- 我的客户菜单
(103, 2420, 1, NOW(), NOW()), -- 新增客户
(103, 2421, 1, NOW(), NOW()), -- 编辑客户
(103, 2423, 1, NOW(), NOW()); -- 客户详情

-- 普通员工权限（role_id=104）- 基础权限
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(104, 189, 1, NOW(), NOW()), -- 客户管理主菜单
(104, 192, 1, NOW(), NOW()), -- 我的客户菜单
(104, 2420, 1, NOW(), NOW()), -- 新增客户
(104, 2421, 1, NOW(), NOW()), -- 编辑客户
(104, 2423, 1, NOW(), NOW()); -- 客户详情

-- 自定义权限（role_id=105）- 特定权限
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(105, 2440, 1, NOW(), NOW()); -- 项目管理主菜单

SELECT '=== 角色菜单权限配置完成 ===' as message;

-- =====================================================
-- 第七步：验证测试数据创建结果
-- =====================================================
SELECT '=== 开始验证测试数据创建结果 ===' as message;

-- 统计各类数据数量
SELECT 
    '部门数据' as type, COUNT(*) as count FROM system_dept WHERE tenant_id = 1
UNION ALL
SELECT 
    '角色数据', COUNT(*) FROM system_role WHERE tenant_id = 1
UNION ALL
SELECT 
    '用户数据', COUNT(*) FROM system_admin WHERE tenant_id = 1
UNION ALL
SELECT 
    '用户角色关联', COUNT(*) FROM system_admin_role WHERE tenant_id = 1
UNION ALL
SELECT 
    '角色菜单权限', COUNT(*) FROM system_role_menu WHERE tenant_id = 1;

-- 显示创建的测试账号详细信息
SELECT '=== 测试账号信息 ===' as message;

SELECT 
    a.id as user_id,
    a.username,
    a.real_name,
    d.name as dept_name,
    r.name as role_name,
    r.data_scope,
    CASE r.data_scope
        WHEN 1 THEN '全部数据'
        WHEN 2 THEN '本部门'
        WHEN 3 THEN '本部门及以下'
        WHEN 4 THEN '仅本人'
        WHEN 5 THEN '自定义'
    END as data_scope_text
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
LEFT JOIN system_admin_role ar ON a.id = ar.admin_id
LEFT JOIN system_role r ON ar.role_id = r.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- 显示角色权限统计
SELECT '=== 角色权限统计 ===' as message;

SELECT 
    r.id as role_id,
    r.name as role_name,
    COUNT(rm.menu_id) as menu_count
FROM system_role r
LEFT JOIN system_role_menu rm ON r.id = rm.role_id AND rm.tenant_id = 1
WHERE r.tenant_id = 1
GROUP BY r.id, r.name
ORDER BY r.id;

SELECT '=== 权限测试数据创建完成！===' as message;
SELECT '=== 所有测试账号密码均为：password ===' as note;
SELECT '=== 现在可以开始进行权限测试了！===' as final_message;

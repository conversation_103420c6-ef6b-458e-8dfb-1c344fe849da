<!--部门人员选择器 - 表单专用版本-->
<template>
  <div class="dept-person-form">
    <!-- 加载状态 -->
    <div v-if="shouldShowLoading" class="loading-placeholder">
      <el-skeleton :rows="1" animated />
    </div>

    <!-- 触发器 -->
    <el-select
      v-else
      v-model="displayValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :size="size"
      :clearable="clearable"
      :multiple="multiple"
      :collapse-tags="collapseTags"
      :collapse-tags-tooltip="collapseTagsTooltip"
      :filterable="false"
      @click="openSelector"
      @clear="handleClear"
      @remove-tag="handleRemoveTag"
    >
      <template #empty>
        <div style="text-align: center; padding: 10px">
          <el-button type="primary" size="small" @click="openSelector"> 选择人员 </el-button>
        </div>
      </template>
    </el-select>

    <!-- 人员选择器弹窗 -->
    <DepartmentPersonSelector
      v-model="selectorVisible"
      :selected-data="selectedPersonsData"
      :multiple="multiple"
      :title="selectorTitle"
      :department-api="departmentApi"
      :user-api="userApi"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script setup lang="ts">
  import DepartmentPersonSelector from './DepartmentPersonSelector.vue'
  import { WorkflowApi } from './workflow/api/workflowApi'

  // 简化的数据类型定义
  interface PersonItem {
    id: string | number
  }

  // 内部使用的完整人员信息
  interface FullPersonItem {
    id: string | number
    name: string
    avatar?: string
    position?: string
    department?: string

    [key: string]: any
  }

  // Props 定义
  interface Props {
    modelValue?: PersonItem[] | PersonItem | null
    multiple?: boolean
    placeholder?: string
    disabled?: boolean
    size?: 'large' | 'default' | 'small'
    clearable?: boolean
    collapseTags?: boolean
    collapseTagsTooltip?: boolean

    // 选择器配置
    selectorTitle?: string
    departmentApi?: () => Promise<any>
    userApi?: (params?: any) => Promise<any>
  }

  const props = withDefaults(defineProps<Props>(), {
    multiple: false,
    placeholder: '请选择人员',
    disabled: false,
    size: 'default',
    clearable: true,
    collapseTags: true,
    collapseTagsTooltip: true,
    selectorTitle: '选择人员'
  })

  // Emits 定义
  interface Emits {
    'update:modelValue': [value: PersonItem[] | PersonItem | null]
    change: [value: PersonItem[] | PersonItem | null]
  }

  const emit = defineEmits<Emits>()

  // 响应式数据
  const selectorVisible = ref(false)
  const personInfoCache = ref<Map<string | number, FullPersonItem>>(new Map())
  const isLoading = ref(false)

  // 默认API配置 - 适配WorkflowApi的数据格式
  const departmentApi =
    props.departmentApi ||
    (async () => {
      const res = await WorkflowApi.getDepartmentList()
      if (res.code === 1 && res.data) {
        return res.data // 直接返回树形结构数据
      }
      throw new Error(res.message || '获取部门列表失败')
    })

  const userApi =
    props.userApi ||
    (async (params?: any) => {
      const res = await WorkflowApi.getUserList(params)
      if (res.code === 1 && res.data) {
        return res.data // 直接返回用户数组
      }
      throw new Error(res.message || '获取用户列表失败')
    })

  // 计算属性
  const selectedPersonsData = computed(() => {
    if (!props.modelValue) return props.multiple ? [] : null

    const getPersonInfo = (id: string | number): FullPersonItem | null => {
      return personInfoCache.value.get(id) || { id, name: `用户${id}` }
    }

    if (props.multiple) {
      const items = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
      return items
        .map((item) => getPersonInfo(item.id))
        .filter((person): person is FullPersonItem => person !== null)
    } else {
      const item = Array.isArray(props.modelValue) ? props.modelValue[0] : props.modelValue
      return item ? getPersonInfo(item.id) : null
    }
  })

  // 检查是否需要显示加载状态
  const shouldShowLoading = computed(() => {
    if (!props.modelValue || isLoading.value) return isLoading.value

    // 检查是否有未加载的人员信息
    const ids: (string | number)[] = []
    if (Array.isArray(props.modelValue)) {
      ids.push(...props.modelValue.map((item) => item.id))
    } else {
      ids.push(props.modelValue.id)
    }

    return ids.some(id => !personInfoCache.value.has(id))
  })

  const displayValue = computed({
    get: () => {
      if (!props.modelValue) return props.multiple ? [] : ''

      if (props.multiple) {
        const items = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
        return items.map((item) => {
          const personInfo = personInfoCache.value.get(item.id)
          return {
            label: personInfo?.name || `用户${item.id}`,
            value: item.id
          }
        })
      } else {
        const item = Array.isArray(props.modelValue) ? props.modelValue[0] : props.modelValue
        if (item) {
          const personInfo = personInfoCache.value.get(item.id)
          return personInfo?.name || `用户${item.id}`
        }
        return ''
      }
    },
    set: () => {
      // 不允许直接设置，只能通过选择器
    }
  })

  // 方法
  const openSelector = () => {
    if (!props.disabled) {
      selectorVisible.value = true
    }
  }

  const handleConfirm = (selected: FullPersonItem[] | FullPersonItem | null) => {
    console.log('DepartmentPersonForm handleConfirm received:', selected)

    // 缓存人员信息
    if (selected) {
      if (Array.isArray(selected)) {
        selected.forEach((person) => {
          personInfoCache.value.set(person.id, person)
        })
      } else {
        personInfoCache.value.set(selected.id, selected)
      }
    }

    // 转换为简化格式
    let result: PersonItem[] | PersonItem | null = null

    if (props.multiple) {
      result = Array.isArray(selected)
        ? selected.map((person) => ({ id: person.id }))
        : selected
          ? [{ id: selected.id }]
          : []
    } else {
      result = Array.isArray(selected)
        ? selected.length > 0
          ? { id: selected[0].id }
          : null
        : selected
          ? { id: selected.id }
          : null
    }

    console.log('DepartmentPersonForm emitting result:', result)
    emit('update:modelValue', result)
    emit('change', result)
  }

  const handleClear = () => {
    const value = props.multiple ? [] : null
    emit('update:modelValue', value)
    emit('change', value)
  }

  const handleRemoveTag = (tag: any) => {
    if (!props.multiple || !Array.isArray(props.modelValue)) return

    const newValue = props.modelValue.filter((item) => item.id !== tag.value)
    emit('update:modelValue', newValue)
    emit('change', newValue)
  }

  // 防重复请求的标记
  const isLoadingRequest = ref(false)
  const lastRequestIds = ref<string>('')

  // 预加载人员信息
  const preloadPersonInfo = async () => {
    if (!props.modelValue) {
      isLoading.value = false
      return
    }

    const ids: (string | number)[] = []

    if (Array.isArray(props.modelValue)) {
      ids.push(...props.modelValue.map((item) => item.id))
    } else {
      ids.push(props.modelValue.id)
    }

    // 过滤掉已缓存的ID
    const uncachedIds = ids.filter((id) => !personInfoCache.value.has(id))

    if (uncachedIds.length === 0) {
      isLoading.value = false
      return
    }

    // 生成请求标识，防止重复请求
    const requestKey = uncachedIds.sort().join(',')
    if (isLoadingRequest.value && lastRequestIds.value === requestKey) {
      console.log('DepartmentPersonForm: 跳过重复请求', requestKey)
      return
    }

    // 开始加载
    isLoading.value = true
    isLoadingRequest.value = true
    lastRequestIds.value = requestKey

    try {
      console.log('DepartmentPersonForm: 发起用户信息请求', uncachedIds)
      const res = await WorkflowApi.getUserList({ ids: uncachedIds })

      if (res.code === 1 && res.data) {
        const users = Array.isArray(res.data) ? res.data : res.data.list || []
        users.forEach((user: any) => {
          // 同时使用字符串和数字ID作为缓存键，确保匹配成功
          const userInfo = {
            id: user.id,
            name: user.name,
            avatar: user.avatar,
            position: user.position,
            department: user.department
          }
          personInfoCache.value.set(user.id, userInfo)
          personInfoCache.value.set(String(user.id), userInfo)
        })
        console.log('DepartmentPersonForm: 用户信息加载完成', users.length, '个用户')
      }
    } catch (error) {
      console.warn('预加载人员信息失败:', error)
    } finally {
      // 加载完成
      isLoading.value = false
      isLoadingRequest.value = false
    }
  }

  // 监听 modelValue 变化，智能处理用户信息
  watch(
    () => props.modelValue,
    (newValue) => {
      if (!newValue) {
        isLoading.value = false
        return
      }

      // 检查传入的数据是否包含完整的用户信息
      let hasCompleteUserData = false
      const usersToCache: any[] = []

      if (Array.isArray(newValue)) {
        // 多选模式：检查是否有包含 name 字段的完整用户数据
        newValue.forEach((item: any) => {
          if (item && typeof item === 'object' && item.name && item.id) {
            hasCompleteUserData = true
            usersToCache.push(item)
          }
        })
      } else if (newValue && typeof newValue === 'object' && newValue.name && newValue.id) {
        // 单选模式：检查是否有完整用户数据
        hasCompleteUserData = true
        usersToCache.push(newValue)
      }

      if (hasCompleteUserData && usersToCache.length > 0) {
        // 如果有完整数据，直接缓存这些用户信息，避免API请求
        console.log('DepartmentPersonForm: 检测到完整用户数据，直接缓存', usersToCache)

        usersToCache.forEach((user: any) => {
          const userInfo = {
            id: user.id,
            name: user.name,
            avatar: user.avatar || '',
            position: user.position || '',
            department: user.department || ''
          }
          // 使用多种格式作为缓存键，确保匹配成功
          personInfoCache.value.set(user.id, userInfo)
          personInfoCache.value.set(String(user.id), userInfo)
          personInfoCache.value.set(Number(user.id), userInfo)
        })

        // 直接停止加载状态，不需要API请求
        isLoading.value = false
        return
      }

      // 如果没有完整数据，按原逻辑通过API加载
      preloadPersonInfo()
    },
    { immediate: true, deep: true }
  )
</script>

<style lang="scss" scoped>
  .dept-person-form {
    width: 100%;

    :deep(.el-select) {
      width: 100%;
    }

    :deep(.el-select__tags) {
      max-width: calc(100% - 30px);
    }

    :deep(.el-tag) {
      max-width: 120px;

      .el-tag__content {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .loading-placeholder {
    width: 100%;
    min-height: 32px;
    display: flex;
    align-items: center;
  }
</style>

<?php
/**
 * 调试workflow权限解析问题
 */

echo "=== 调试workflow权限解析问题 ===\n\n";

echo "1. 分析路由 api/workflow/task/index:\n";

// 模拟路由解析过程
$route = 'api/workflow/task/index';
echo "  实际路由: {$route}\n";

// 这个路由应该对应的控制器
$expectedController = 'app\\workflow\\controller\\TaskController@index';
echo "  预期控制器: {$expectedController}\n";

echo "\n2. 模拟权限解析过程:\n";

// 模拟PermissionMiddleware的解析过程
function parsePermissionInfo($ruleName) {
    [$classPath, $method] = explode('@', $ruleName);
    $parts = explode('\\', $classPath);
    $module = strtolower($parts[1]);
    $controllerClass = $parts[count($parts) - 1];
    $controllerName = str_replace('Controller', '', $controllerClass);
    
    // 生成权限名称
    $permissionPath = generatePermissionName($module, $parts, $controllerName, $method);
    
    return [$module, $permissionPath, $method];
}

function generatePermissionName($module, $parts, $controllerName, $method) {
    switch ($module) {
        case 'workflow':
            return generateWorkflowPermissionName($controllerName, $method);
        default:
            return strtolower($module . '_' . $controllerName) . ':' . $method;
    }
}

function generateWorkflowPermissionName($controllerName, $method) {
    $snakeName = camelToSnake($controllerName);
    
    // Workflow模块的权限格式: workflow_模块名:方法
    if (!str_starts_with($snakeName, 'workflow_')) {
        $snakeName = 'workflow_' . $snakeName;
    }
    
    return $snakeName . ':' . $method;
}

function camelToSnake($input) {
    return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
}

// 模拟中间件的权限构建逻辑
function buildPermission($ruleName) {
    [$module, $permissionPath, $action] = parsePermissionInfo($ruleName);
    return strtolower("{$module}:{$permissionPath}");
}

// 测试权限解析
$testController = 'app\\workflow\\controller\\TaskController@index';
$parsedPermission = buildPermission($testController);

echo "  控制器: {$testController}\n";
echo "  解析结果: {$parsedPermission}\n";

echo "\n3. 检查数据库中的实际权限:\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    // 检查解析的权限是否存在
    $stmt = $pdo->prepare("SELECT name, title FROM system_menu WHERE name = ? AND status = 1 AND deleted_at IS NULL");
    $stmt->execute([$parsedPermission]);
    $permission = $stmt->fetch();
    
    if ($permission) {
        echo "  ✅ 解析的权限存在: {$permission['name']} ({$permission['title']})\n";
    } else {
        echo "  ❌ 解析的权限不存在: {$parsedPermission}\n";
        
        // 查找相似的权限
        $stmt = $pdo->prepare("SELECT name, title FROM system_menu WHERE name LIKE ? AND status = 1 AND deleted_at IS NULL");
        $stmt->execute(['%task%']);
        $similarPermissions = $stmt->fetchAll();
        
        echo "  相似的权限:\n";
        foreach ($similarPermissions as $perm) {
            echo "    - {$perm['name']} ({$perm['title']})\n";
        }
    }
    
    echo "\n4. 问题分析:\n";
    
    // 检查实际的workflow:task权限格式
    $stmt = $pdo->prepare("SELECT name, title FROM system_menu WHERE name LIKE 'workflow:task%' AND status = 1 AND deleted_at IS NULL");
    $stmt->execute();
    $workflowTaskPermissions = $stmt->fetchAll();
    
    echo "  数据库中的workflow:task权限:\n";
    foreach ($workflowTaskPermissions as $perm) {
        echo "    - {$perm['name']} ({$perm['title']})\n";
    }
    
    // 分析权限格式差异
    $expectedFormat = $parsedPermission;
    $actualFormat = $workflowTaskPermissions[0]['name'] ?? '';
    
    echo "\n  权限格式对比:\n";
    echo "    我们解析的: {$expectedFormat}\n";
    echo "    数据库实际: {$actualFormat}\n";
    
    if ($expectedFormat !== $actualFormat && !empty($actualFormat)) {
        echo "    ❌ 权限格式不匹配！\n";
        echo "    \n";
        echo "    问题原因: workflow模块的权限格式与我们的解析逻辑不匹配\n";
        echo "    数据库中是: workflow:task:index\n";
        echo "    我们解析的: workflow:workflow_task:index\n";
        echo "    \n";
        echo "    解决方案: 需要修正workflow模块的权限解析逻辑\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n5. 解决方案:\n";
echo "  需要修正PermissionService.php中的generateWorkflowPermissionName方法\n";
echo "  \n";
echo "  当前逻辑: workflow_task:index\n";
echo "  应该改为: task:index\n";
echo "  \n";
echo "  修正后的方法:\n";
echo "  private function generateWorkflowPermissionName(\$controllerName, \$method): string\n";
echo "  {\n";
echo "      \$snakeName = \$this->camelToSnake(\$controllerName);\n";
echo "      // workflow模块不需要添加workflow_前缀\n";
echo "      return \$snakeName . ':' . \$method;\n";
echo "  }\n";

echo "\n=== 调试完成 ===\n";

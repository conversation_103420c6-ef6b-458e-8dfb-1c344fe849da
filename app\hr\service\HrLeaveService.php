<?php
declare(strict_types=1);

namespace app\hr\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\exception\BusinessException;
use app\common\utils\DateCalculator;
use app\hr\model\HrLeave;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\service\BusinessWorkflowService;
use think\facade\Db;
use think\facade\Log;

/**
 * 请假申请服务类（通用对接工作流）
 *
 * 功能说明：
 * 1. 实现FormServiceInterface接口，支持工作流表单操作
 * 2. 提供完整的CRUD操作
 * 3. 集成新的工作流业务服务
 * 4. 支持请假申请的业务验证和状态管理
 *
 * 主要方法：
 * - saveForm(): 保存表单数据
 * - updateForm(): 更新表单数据
 * - updateFormStatus(): 更新表单状态
 * - validateLeaveData(): 验证请假数据
 */
class HrLeaveService extends BaseService implements FormServiceInterface
{
	use CrudServiceTrait;
	
	/**
	 * 业务工作流服务实例
	 *
	 * @var BusinessWorkflowService
	 */
	protected BusinessWorkflowService $businessWorkflowService;
	
	/**
	 * 构造函数
	 * 初始化模型和工作流服务
	 */
	public function __construct()
	{
		$this->model = new HrLeave();
		parent::__construct();
	}
	
	// ==================== FormServiceInterface 实现 ====================
	
	/**
	 * 获取表单数据
	 *
	 * @param int $id 记录ID
	 * @return array 表单数据
	 *                返回数据结构：
	 *                [
	 *                "id" => 1,
	 *                "leave_type" => 1,
	 *                "start_time" => "2025-01-19 09:00:00",
	 *                "end_time" => "2025-01-22 18:00:00",
	 *                "duration" => 3.0,
	 *                "reason" => "年假休息",
	 *                "approval_status" => 0,
	 *                "submitter_name" => "张三",
	 *                "leave_type_text" => "年假",
	 *                "approval_status_text" => "草稿",
	 *                "can_edit" => true,
	 *                "can_submit" => true,
	 *                "can_withdraw" => false,
	 *                "can_delete" => true
	 *                ]
	 */
	public function getFormData(int $id): array
	{
		// 直接查询避免关联查询的字段歧义问题
		$leave = $this->model->find($id);
		
		if (!$leave) {
			throw new BusinessException('请假申请不存在');
		}
		
		$data = $leave->toArray();
		
		// 手动添加关联数据
		if ($leave->submitter_id) {
			try {
				$submitter              = Db::table('system_admin')
				                            ->where('id', $leave->submitter_id)
				                            ->find();
				$data['submitter']      = $submitter;
				$data['submitter_name'] = $submitter['real_name'] ?? '';
			}
			catch (\Exception $e) {
				// 如果查询失败，设置默认值
				$data['submitter']      = null;
				$data['submitter_name'] = '';
			}
		}
		
		return $data;
	}
	
	/**
	 * 保存表单数据（新增）
	 *
	 * @param array $data 表单数据
	 * @return array 返回格式：[记录ID, 表单数据]
	 *                    输入数据结构：
	 *                    [
	 *                    "leave_type" => 1,
	 *                    "start_time" => "2025-01-19 09:00:00",
	 *                    "end_time" => "2025-01-22 18:00:00",
	 *                    "duration" => 3.0,
	 *                    "reason" => "年假休息",
	 *                    "emergency_contact" => "李四",
	 *                    "emergency_phone" => "***********",
	 *                    "attachment" => ["file1.pdf", "file2.jpg"],
	 *                    "remark" => "备注信息"
	 *                    ]
	 *
	 * 返回数据结构：
	 * [
	 *   123,  // 新增记录的ID
	 *   [     // 完整的表单数据
	 *     "id" => 123,
	 *     "leave_type" => 1,
	 *     "start_time" => "2025-01-19 09:00:00",
	 *     ...
	 *   ]
	 * ]
	 */
	public function saveForm(array $data): array
	{
		$formData = $data['business_data'];
		// 1. 数据预处理和验证
		$data = $this->preprocessLeaveData($formData, 'create');

		// 自动计算请假时长
		if (isset($data['start_time']) && isset($data['end_time'])) {
			$data['duration'] = $this->calculateLeaveDuration($data['start_time'], $data['end_time']);
		}

		$this->validateLeaveData($data, 'create');
		
		// 2. 设置默认值
		$data['approval_status']      = HrLeave::APPROVAL_STATUS_DRAFT;
		$data['workflow_instance_id'] = 0;
		$data['submitter_id']         = $formData['submitter_id'] ?? get_user_id();
		
		// 3. 保存数据（使用数据库直接插入避免关联查询歧义）
		$leaveId = (new HrLeave())->saveByCreate($data);
		if (!$leaveId) {
			throw new BusinessException('保存请假申请失败');
		}
		
		// 4. 获取完整数据
		$leaveData = $this->getFormData($leaveId);
		
		Log::info('请假申请保存成功', [
			'leave_id'     => $leaveId,
			'submitter_id' => $data['submitter_id'],
			'leave_type'   => $data['leave_type'],
			'duration'     => $data['duration']
		]);
		
		return [
			$leaveId,
			$leaveData
		];
	}
	
	/**
	 * 更新表单数据
	 *
	 * @param int   $id   记录ID
	 * @param array $data 更新数据
	 * @return bool 是否成功
	 *                    输入数据结构：
	 *                    [
	 *                    "leave_type" => 2,
	 *                    "start_time" => "2025-01-20 09:00:00",
	 *                    "end_time" => "2025-01-23 18:00:00",
	 *                    "duration" => 4.0,
	 *                    "reason" => "修改后的请假原因",
	 *                    ...
	 *                    ]
	 */
	public function updateForm(int $id, array $data): bool
	{
		// 1. 检查记录是否存在
		$leave = $this->model->find($id);
		if (!$leave) {
			throw new BusinessException('请假申请不存在');
		}
		
		// 2. 检查是否可以编辑
		if (!$leave->can_edit) {
			throw new BusinessException('当前状态不允许编辑');
		}
		
		// 3. 数据预处理和验证
		$data = $this->preprocessLeaveData($data, 'update', $leave);

		// 自动计算请假时长
		if (isset($data['start_time']) && isset($data['end_time'])) {
			$data['duration'] = $this->calculateLeaveDuration($data['start_time'], $data['end_time']);
		}

		$this->validateLeaveData($data, 'update');
		
		// 4. 执行更新
		$result = $this->crudService->edit($data, ['id' => $id]);
		
		if ($result) {
			Log::info('请假申请更新成功', [
				'leave_id'       => $id,
				'updated_fields' => array_keys($data)
			]);
		}
		
		return $result;
	}
	
	/**
	 * 更新表单状态
	 *
	 * @param int   $id     记录ID
	 * @param int   $status 新状态
	 * @param array $extra  额外数据
	 * @return bool 是否成功
	 *                      额外数据结构：
	 *                      [
	 *                      "approval_time" => "2025-01-19 15:30:00",
	 *                      "approval_opinion" => "同意请假",
	 *                      "workflow_instance_id" => 123
	 *                      ]
	 */
	public function updateFormStatus(int $id, int $status, array $extra = []): bool
	{
		$updateData = [
			'approval_status' => $status
		];
		
		// 合并额外数据
		if (!empty($extra)) {
			$updateData = array_merge($updateData, $extra);
		}
		
		// 直接使用数据库更新避免CRUD服务的问题
		$result = Db::table('hr_leave')
		            ->where('id', $id)
		            ->update($updateData);
		
		if ($result !== false) {
			Log::info('请假申请状态更新成功', [
				'leave_id'   => $id,
				'new_status' => $status,
				'extra_data' => $extra
			]);
		}
		
		return $result !== false;
	}
	
	/**
	 * 删除表单
	 *
	 * @param int $id 记录ID
	 * @return bool 是否成功
	 */
	public function deleteForm(int $id): bool
	{
		// 1. 检查记录是否存在
		$leave = $this->model->find($id);
		if (!$leave) {
			throw new BusinessException('请假申请不存在');
		}
		
		// 2. 检查是否可以删除（只有草稿状态可以删除）
		if ($leave->approval_status !== HrLeave::APPROVAL_STATUS_DRAFT) {
			throw new BusinessException('只有草稿状态的申请可以删除');
		}
		
		// 3. 执行删除（直接使用数据库删除）
		$result = Db::table('hr_leave')
		            ->where('id', $id)
		            ->delete();
		
		if ($result !== false) {
			Log::info('请假申请删除成功', ['leave_id' => $id]);
		}
		
		return $result !== false;
	}
	
	/**
	 * 获取实例标题（用于工作流）
	 *
	 * @param array $formData 表单数据
	 * @return string 实例标题
	 *                        输入数据结构：
	 *                        [
	 *                        "submitter_name" => "张三",
	 *                        "leave_type" => 1,
	 *                        "duration" => 3.0,
	 *                        ...
	 *                        ]
	 */
	public function getInstanceTitle($formData): string
	{
		$submitterName = $formData['submitter_name'] ?? '';
		$leaveType     = $formData['leave_type'] ?? 1;
		$duration      = $formData['duration'] ?? 0;
		
		$leaveTypeMap = [
			HrLeave::LEAVE_TYPE_ANNUAL    => '年假',
			HrLeave::LEAVE_TYPE_PERSONAL  => '事假',
			HrLeave::LEAVE_TYPE_SICK      => '病假',
			HrLeave::LEAVE_TYPE_MARRIAGE  => '婚假',
			HrLeave::LEAVE_TYPE_MATERNITY => '产假',
			HrLeave::LEAVE_TYPE_FUNERAL   => '丧假',
			HrLeave::LEAVE_TYPE_OTHER     => '其他',
		];
		
		$leaveTypeName = $leaveTypeMap[$leaveType] ?? '其他';
		
		return "{$submitterName}的{$leaveTypeName}申请({$duration}小时)";
	}
	
	
	// ==================== 数据处理方法 ====================
	
	/**
	 * 预处理请假数据
	 *
	 * @param array       $data          原始数据
	 * @param string      $scene         场景：create|update
	 * @param object|null $originalLeave 原请假数据（更新时）
	 * @return array 预处理后的数据
	 */
	private function preprocessLeaveData(array $data, string $scene, $originalLeave = null): array
	{
		// 1. 处理时间字段
		$dateFields = [
			'start_time',
			'end_time',
			'submit_time',
			'approval_time'
		];
		foreach ($dateFields as $field) {
			if (isset($data[$field]) && empty($data[$field])) {
				$data[$field] = null;
			}
		}
		
		// 2. 处理附件字段
		if (isset($data['attachment']) && is_array($data['attachment'])) {
			$data['attachment'] = json_encode($data['attachment']);
		}
		
		// 3. 处理数值字段
		if (isset($data['duration'])) {
			$data['duration'] = (float)$data['duration'];
		}
		
		// 4. 自动计算请假时长（如果未提供）
		if (isset($data['start_time']) && isset($data['end_time']) && !isset($data['duration'])) {
			$data['duration'] = $this->calculateLeaveDuration($data['start_time'], $data['end_time']);
		}
		
		return $data;
	}

	/**
	 * 计算请假时长（小时）
	 * 使用半小时向上取整规则，考虑工作时间配置
	 *
	 * @param string $startTime 开始时间
	 * @param string $endTime 结束时间
	 * @return float 请假时长（小时）
	 */
	private function calculateLeaveDuration(string $startTime, string $endTime): float
	{
		// 获取工作时间配置
		$workTimeConfig = (new HrWorkTimeService())->getWorkTimeConfig();

		// 使用工作时间计算方法
		return DateCalculator::calculateWorkingHoursWithHalfHourRule($startTime, $endTime, $workTimeConfig['work_time']);
	}

	/**
	 * 验证请假数据
	 *
	 * @param array  $data  数据
	 * @param string $scene 场景：create|update
	 * @throws BusinessException
	 */
	private function validateLeaveData(array $data, string $scene): void
	{
		// 1. 必填字段验证
		$requiredFields = [
			'leave_type',
			'start_time',
			'end_time',
			'duration',
			'reason'
		];
		foreach ($requiredFields as $field) {
			if (!isset($data[$field]) || empty($data[$field])) {
				throw new BusinessException("字段 {$field} 不能为空");
			}
		}
		
		// 2. 请假类型验证
		$validLeaveTypes = [
			HrLeave::LEAVE_TYPE_ANNUAL,
			HrLeave::LEAVE_TYPE_PERSONAL,
			HrLeave::LEAVE_TYPE_SICK,
			HrLeave::LEAVE_TYPE_MARRIAGE,
			HrLeave::LEAVE_TYPE_MATERNITY,
			HrLeave::LEAVE_TYPE_FUNERAL,
			HrLeave::LEAVE_TYPE_OTHER
		];
		if (!in_array($data['leave_type'], $validLeaveTypes)) {
			throw new BusinessException('请假类型无效');
		}
		
		// 3. 时间验证
		$startTime = strtotime($data['start_time']);
		$endTime   = strtotime($data['end_time']);
		
		if (!$startTime || !$endTime) {
			throw new BusinessException('时间格式无效');
		}
		
		if ($startTime >= $endTime) {
			throw new BusinessException('结束时间必须晚于开始时间');
		}
		
		if ($startTime < time() - 24 * 3600) {
			throw new BusinessException('开始时间不能早于昨天');
		}
		
		// 4. 请假时长验证
		if ($data['duration'] <= 0 || $data['duration'] > 2920) { // 365天 * 8小时 = 2920小时
			throw new BusinessException('请假时长必须在0.5-2920小时之间');
		}
		
		// 5. 请假原因长度验证
		if (mb_strlen($data['reason']) > 500) {
			throw new BusinessException('请假原因不能超过500个字符');
		}
	}
	
	public function validateFormData(array $data, string $scene = 'create'): array
	{
		// TODO: Implement validateFormData() method.
		return $data;
	}
	
	// ==================== 工作流回调处理 ====================
	
	/**
	 * 工作流状态变更后的业务处理（覆盖默认实现）
	 *
	 * @param int   $businessId 请假申请ID
	 * @param int   $status     新的工作流状态
	 * @param array $extra      额外数据
	 * @return bool 处理结果
	 */
	public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
	{
		/*try {
			switch ($status) {
				case WorkflowStatusConstant::STATUS_COMPLETED:
					// 审批通过后的处理
					return $this->handleLeaveApproved($businessId, $extra);
				
				case WorkflowStatusConstant::STATUS_REJECTED:
					// 审批拒绝后的处理
					return $this->handleLeaveRejected($businessId, $extra);
				
				case WorkflowStatusConstant::STATUS_RECALLED:
					// 撤回后的处理
					return $this->handleLeaveRecalled($businessId, $extra);
				
				case WorkflowStatusConstant::STATUS_VOID:
					// 作废后的处理
					return $this->handleLeaveVoided($businessId, $extra);
				
				default:
					// 其他状态使用默认处理
					return true;
			}
			
		}
		catch (\Exception $e) {
			Log::error('请假申请工作流状态变更处理失败', [
				'leave_id' => $businessId,
				'status'   => $status,
				'error'    => $e->getMessage(),
				'trace'    => $e->getTraceAsString()
			]);
			
			return false;
		}*/
		
		return true;
	}
	
}
# 附件系统需求分析与现状调研

**版本**: v1.0  
**更新日期**: 2025-01-31  
**文档状态**: 草稿

---

## 🎯 需求背景

### 业务需求
1. **存储成本控制**：随着业务增长，附件存储成本快速上升
2. **用户体验优化**：相同文件重复上传，影响用户体验
3. **数据安全要求**：需要严格的文件访问权限控制
4. **系统性能优化**：大量重复文件影响系统性能

### 技术需求
1. **文件去重**：实现基于内容的文件去重机制
2. **权限隔离**：确保用户只能访问自己的文件
3. **云存储优化**：充分利用云存储平台的去重能力
4. **架构优化**：提高系统的可维护性和扩展性

---

## 📊 现状调研

### 当前系统架构

#### 数据库表结构
```sql
-- 当前附件表结构
CREATE TABLE `system_attachment` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类',
    `name` varchar(100) NOT NULL COMMENT '附件名称',
    `real_name` text COMMENT '原始文件名',
    `path` varchar(255) NOT NULL COMMENT '附件路径',
    `extension` varchar(10) DEFAULT NULL COMMENT '文件扩展名',
    `size` bigint(20) NOT NULL DEFAULT 0 COMMENT '文件大小',
    `mime_type` varchar(100) DEFAULT NULL COMMENT '媒体类型',
    `storage` varchar(20) NOT NULL DEFAULT 'local' COMMENT '存储位置',
    `storage_id` varchar(100) DEFAULT NULL COMMENT '存储平台ID',
    `file_md5` varchar(32) NOT NULL DEFAULT '' COMMENT '文件MD5值',
    `is_shared` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否共享文件',
    `ref_count` int(11) NOT NULL DEFAULT 1 COMMENT '引用计数',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    -- 其他字段...
);
```

#### 上传流程分析
1. **本地存储**：文件通过后端上传，可以预先计算MD5
2. **云存储直传**：前端直接上传到云存储，后端通过回调获取信息
3. **去重逻辑**：已有基础的MD5去重，但受creator_id限制

#### 权限控制现状
```php
// AttachmentController.php
$this->service->getCrudService()->setEnableDataPermission(false);
```
**问题**：数据权限被禁用，存在安全隐患

---

## ❌ 现存问题分析

### 1. 存储效率问题

#### 重复存储严重
- **现象**：不同用户上传相同文件时，系统会创建多个物理文件
- **原因**：去重逻辑受`creator_id`限制，只在同一用户范围内去重
- **影响**：存储空间浪费，成本增加

#### 示例场景
```
用户A上传：company_logo.png (MD5: abc123...)
用户B上传：company_logo.png (MD5: abc123...)
结果：存储了两个相同的物理文件
```

### 2. 权限控制问题

#### 数据权限被禁用
```php
// 当前代码
$this->service->getCrudService()->setEnableDataPermission(false);
```

#### 潜在安全风险
- 用户可能看到其他用户的文件
- 缺乏细粒度的访问控制
- 不符合多租户安全要求

### 3. 架构设计问题

#### 职责混乱
- 物理文件存储与用户权限耦合
- 难以实现跨用户的文件共享
- 扩展性差

#### 云存储利用不充分
- 未充分利用云存储平台的去重能力（etag）
- 回调处理逻辑不完善
- 缺乏统一的存储策略

---

## 🎯 需求分析

### 功能性需求

#### FR1: 文件去重
- **需求描述**：相同内容的文件只存储一份物理文件
- **优先级**：高
- **验收标准**：
  - 相同MD5的文件只有一个物理存储
  - 支持跨用户、跨租户的去重
  - 去重率达到90%以上

#### FR2: 权限隔离
- **需求描述**：用户只能访问自己的文件
- **优先级**：高
- **验收标准**：
  - 用户无法看到其他用户的文件
  - 支持租户级别的数据隔离
  - 通过权限测试验证

#### FR3: 文件管理
- **需求描述**：完整的文件管理功能
- **优先级**：中
- **验收标准**：
  - 文件上传、删除、移动
  - 文件分类管理
  - 文件搜索和筛选

#### FR4: 云存储支持
- **需求描述**：支持多种云存储平台
- **优先级**：中
- **验收标准**：
  - 支持阿里云、腾讯云、七牛云
  - 统一的存储接口
  - 充分利用云存储去重能力

### 非功能性需求

#### NFR1: 性能要求
- **上传性能**：相同文件实现秒传
- **查询性能**：文件列表查询响应时间<500ms
- **并发支持**：支持100个并发上传

#### NFR2: 可靠性要求
- **数据一致性**：确保文件记录与物理文件一致
- **事务安全**：上传过程支持事务回滚
- **错误处理**：完善的异常处理机制

#### NFR3: 安全性要求
- **访问控制**：严格的文件访问权限
- **数据隔离**：租户间数据完全隔离
- **审计日志**：记录文件操作日志

#### NFR4: 可维护性要求
- **代码质量**：遵循编码规范
- **文档完整**：完整的技术文档
- **测试覆盖**：单元测试覆盖率>80%

---

## 📈 预期收益分析

### 存储成本节省
- **预计节省**：50-80%的存储空间
- **成本影响**：显著降低云存储费用
- **ROI计算**：预计6个月内收回开发成本

### 性能提升
- **上传体验**：相同文件秒传，提升用户满意度
- **系统性能**：减少存储I/O，提升整体性能
- **带宽节省**：减少重复文件传输

### 安全性提升
- **数据安全**：严格的权限控制，降低数据泄露风险
- **合规性**：满足数据安全合规要求
- **审计能力**：完整的操作审计

### 可维护性提升
- **架构清晰**：职责分离，易于维护
- **扩展性强**：支持未来功能扩展
- **代码质量**：提高代码可读性和可维护性

---

## 🔍 技术调研

### 业界最佳实践
1. **阿里云OSS**：基于etag的去重机制
2. **腾讯云COS**：智能分层存储
3. **七牛云**：内容去重和智能压缩
4. **GitHub**：基于SHA的文件去重

### 技术选型建议
1. **去重策略**：MD5 + 存储平台etag双重保障
2. **数据库设计**：关联表模式，职责分离
3. **缓存策略**：Redis缓存热点文件信息
4. **监控方案**：完整的性能和业务监控

---

## 📋 下一步计划

1. **技术方案设计**：详细的技术实现方案
2. **数据库设计**：新表结构和迁移方案
3. **API设计**：接口规范和兼容性方案
4. **开发计划**：详细的开发任务分解
5. **测试方案**：完整的测试计划
6. **部署方案**：安全的上线部署方案

# 待办任务简化设计方案

## 📋 设计调整说明

**调整日期**：2025-01-14  
**调整原因**：快速上线需求，简化功能实现  
**调整内容**：去掉tabs分类和底部操作按钮，统一显示所有待办任务

## 🎯 简化后的功能范围

### 保留功能
1. **工作流审批任务** - 复用现有API
2. **CRM客户跟进** - 需要开发聚合API
3. **项目任务管理** - 需要筛选当前用户任务

### 移除功能
- ❌ **Tabs分类显示** - 改为统一列表显示
- ❌ **[查看全部]按钮** - 直接点击任务项跳转
- ❌ **[标记已读]按钮** - 简化操作流程
- ❌ **合同到期提醒** - 暂不实施
- ❌ **消息通知** - 顶部已有入口

## 🎨 UI设计方案

### 整体布局
```
┌─────────────────────────────────────────┬───────────────────────────────────┐
│              我的待办任务 (6)            │           快捷操作面板             │
├─────────────────────────────────────────┼───────────────────────────────────┤
│                                         │  ┌─────────────┬─────────────────┐ │
│  🔄 [审批] 张三的请假申请 (2小时前)     │  │ 👥 我的客户  │ 📋 项目管理     │ │
│  👥 [跟进] ABC公司需要回访 (今天) ⚠️    │  ├─────────────┼─────────────────┤ │
│  📋 [任务] 项目A设计稿审核 (明天截止)   │  │ 📄 工作汇报  │ 📊 我的申请     │ │
│  🔄 [审批] 销售合同审批 (4小时前)       │  ├─────────────┼─────────────────┤ │
│  👥 [跟进] XYZ客户报价跟进 (明天)       │  │ 💰 每日报价  │                 │ │
│  📋 [任务] 项目B测试报告 (后天截止)     │  └─────────────┴─────────────────┘ │
│                                         │                                   │
│  暂无更多待办任务                       │                                   │
│                                         │                                   │
└─────────────────────────────────────────┴───────────────────────────────────┘
```

### 任务项设计
每个任务项包含：
- **类型标签**：[审批] [跟进] [任务] 用不同颜色区分
- **任务标题**：简洁明了的任务描述
- **时间信息**：相对时间显示（2小时前、今天、明天等）
- **紧急标识**：⚠️ 标识紧急任务

### 排序规则
1. **紧急任务优先**：逾期、当天到期的任务排在前面
2. **时间排序**：按照任务时间从近到远排序
3. **类型权重**：审批 > 跟进 > 任务

## 🔧 技术实现方案

### 前端组件结构
```
TodoTasks.vue (主组件)
├── styles/
│   └── todo-tasks.scss    # 样式文件
└── composables/
    └── useTodoTasks.ts    # 逻辑复用
```

### 组件实现
```vue
<template>
  <div class="todo-tasks-widget">
    <div class="widget-header">
      <h4>我的待办任务</h4>
      <span class="total-count">({{ totalCount }})</span>
    </div>
    
    <div class="todo-list">
      <div 
        v-for="task in allTasks" 
        :key="`${task.type}-${task.id}`"
        class="task-item"
        :class="{ 'urgent': task.urgent }"
        @click="handleTaskClick(task)"
      >
        <div class="task-content">
          <ElTag :type="getTaskTypeColor(task.type)" size="small">
            {{ getTaskTypeText(task.type) }}
          </ElTag>
          <span class="task-title">{{ task.title }}</span>
          <span class="task-time">{{ formatTime(task.time) }}</span>
          <ElTag 
            v-if="task.urgent" 
            type="danger" 
            size="small"
            effect="plain"
          >
            ⚠️
          </ElTag>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="allTasks.length === 0" class="empty-state">
        <ElEmpty 
          :image-size="60"
          description="暂无待办任务"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { WorkbenchApi } from '@/api/workbench'
import { useRouter } from 'vue-router'

const router = useRouter()
const workflowTasks = ref([])
const customerFollow = ref([])
const projectTasks = ref([])

// 聚合所有待办任务
const allTasks = computed(() => {
  const tasks = []
  
  // 添加工作流任务
  workflowTasks.value.forEach(task => {
    tasks.push({
      ...task,
      type: 'workflow',
      urgent: isUrgentTask(task, 'workflow')
    })
  })
  
  // 添加CRM跟进任务
  customerFollow.value.forEach(task => {
    tasks.push({
      ...task,
      type: 'crm',
      urgent: isUrgentTask(task, 'crm')
    })
  })
  
  // 添加项目任务
  projectTasks.value.forEach(task => {
    tasks.push({
      ...task,
      type: 'project',
      urgent: isUrgentTask(task, 'project')
    })
  })
  
  // 按优先级和时间排序
  return tasks.sort((a, b) => {
    // 紧急任务优先
    if (a.urgent && !b.urgent) return -1
    if (!a.urgent && b.urgent) return 1
    
    // 按时间排序
    return new Date(a.time) - new Date(b.time)
  })
})

const totalCount = computed(() => allTasks.value.length)

const loadTodoTasks = async () => {
  try {
    const res = await WorkbenchApi.getTodoTasks()
    if (res.code === 200) {
      workflowTasks.value = res.data.workflow_tasks || []
      customerFollow.value = res.data.customer_follow || []
      projectTasks.value = res.data.project_tasks || []
    }
  } catch (error) {
    console.error('加载待办任务失败:', error)
  }
}

const handleTaskClick = (task) => {
  // 根据任务类型跳转到对应页面
  switch (task.type) {
    case 'workflow':
      router.push('/workflow/task')
      break
    case 'crm':
      router.push('/crm/crm_follow_record')
      break
    case 'project':
      router.push(`/project/detail/${task.project_id}`)
      break
  }
}

const getTaskTypeText = (type) => {
  const typeMap = {
    workflow: '审批',
    crm: '跟进',
    project: '任务'
  }
  return typeMap[type] || '其他'
}

const getTaskTypeColor = (type) => {
  const colorMap = {
    workflow: 'warning',
    crm: 'primary', 
    project: 'success'
  }
  return colorMap[type] || 'info'
}

const isUrgentTask = (task, type) => {
  const now = Date.now()
  
  if (type === 'workflow') {
    // 审批任务超过24小时为紧急
    return now - new Date(task.created_at).getTime() > 24 * 60 * 60 * 1000
  }
  
  if (type === 'crm') {
    // 跟进任务当天或逾期为紧急
    const followDate = new Date(task.next_date)
    return followDate.toDateString() <= new Date().toDateString()
  }
  
  if (type === 'project') {
    // 项目任务明天截止为紧急
    const dueDate = new Date(task.due_date)
    return dueDate.getTime() - now < 24 * 60 * 60 * 1000
  }
  
  return false
}

const formatTime = (time) => {
  const now = new Date()
  const taskTime = new Date(time)
  const diffMs = now.getTime() - taskTime.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffHours < 1) return '刚刚'
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays === 1) return '昨天'
  if (diffDays < 7) return `${diffDays}天前`
  
  return taskTime.toLocaleDateString()
}

onMounted(() => {
  loadTodoTasks()
})
</script>

<style lang="scss" scoped>
.todo-tasks-widget {
  .widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    
    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    
    .total-count {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .todo-list {
    .task-item {
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 6px;
      border: 1px solid #e4e7ed;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }
      
      &.urgent {
        border-color: #f56c6c;
        background-color: #fef0f0;
      }
      
      .task-content {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .task-title {
          flex: 1;
          font-size: 14px;
          color: #303133;
        }
        
        .task-time {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 40px 20px;
    }
  }
}
</style>
```

## 📊 后端API需求

### 统一待办任务接口
```php
// GET /api/workbench/todo-tasks
{
  "code": 200,
  "data": {
    "workflow_tasks": [
      {
        "id": 1,
        "title": "张三的请假申请",
        "created_at": "2025-01-14 10:00:00",
        "type": "workflow"
      }
    ],
    "customer_follow": [
      {
        "id": 1,
        "title": "ABC公司需要回访",
        "next_date": "2025-01-14",
        "customer_name": "ABC公司",
        "type": "crm"
      }
    ],
    "project_tasks": [
      {
        "id": 1,
        "title": "项目A设计稿审核",
        "due_date": "2025-01-15",
        "project_id": 1,
        "type": "project"
      }
    ]
  }
}
```

## 🎯 实施计划

### 第一阶段：后端API (1-2天)
- [ ] 创建WorkbenchController
- [ ] 实现getTodoTasks接口
- [ ] 聚合各模块待办数据

### 第二阶段：前端组件 (1-2天)
- [ ] 创建TodoTasks.vue组件
- [ ] 实现数据加载和显示
- [ ] 添加点击跳转功能

### 第三阶段：集成测试 (0.5天)
- [ ] 组件集成到工作台
- [ ] 功能测试和样式调优

**总工作量**：2.5-4.5天，满足快速上线需求

## ✅ 确认要点

1. **去掉tabs分类** - 统一列表显示 ✅
2. **去掉底部按钮** - 简化操作流程 ✅  
3. **保持核心功能** - 审批、跟进、任务 ✅
4. **点击跳转** - 直接跳转到对应页面 ✅
5. **快速上线** - 工作量控制在5天内 ✅

---

**等待确认**：请确认以上设计方案，确认后开始实施代码修改。

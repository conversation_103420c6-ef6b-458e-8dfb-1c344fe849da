<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\core\constants\SystemConstant;
use app\common\exception\BusinessException;
use app\common\exception\ValidateFailedException;
use app\common\utils\DataPermissionCacheUtil;
use app\system\model\AdminModel;
use app\system\validate\AdminValidate;
use think\exception\ValidateException;
use think\facade\Db;
use Throwable;

/**
 * 管理员服务类
 * 负责管理员的CRUD操作，支持数据权限和租户权限
 */
class AdminService extends BaseService
{
	/**
	 * 构造函数
	 */
	protected function __construct()
	{
		$this->model = new AdminModel();
		parent::__construct();
	}
	
	/**
	 * 设置密码属性
	 *
	 * @param string $password 密码
	 * @return string
	 */
	public function setPasswordAttr(string $password): string
	{
		return password_hash($password, PASSWORD_DEFAULT);
	}
	
	/**
	 * 获取是否为租户超级管理员
	 *
	 * @return bool 管理员ID
	 */
	public function isTenantSuperAdmin($id): bool
	{
		return $this->model->where([
				'id'     => $id,
				'status' => 1
			])
		                   ->value('is_super_admin') == 1;
	}
	
	/**
	 * 获取管理员列表(有数据权限)
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getList(array $params): array
	{
		// 构建查询条件
		$where = [];
		
		if (!is_super_admin() && !is_tenant_super_admin()) {
			$where[] = [
				'id',
				'>',
				1
			];
		}
		
		// 用户名搜索
		if (!empty($params['username'])) {
			$where[] = [
				'username|real_name',
				'like',
				'%' . $params['username'] . '%'
			];
		}
		
		// 手机号搜索
		if (!empty($params['mobile'])) {
			$where[] = [
				'mobile',
				'like',
				'%' . $params['mobile'] . '%'
			];
		}
		
		// 状态搜索
		if (isset($params['status']) && $params['status'] !== '') {
			$where[] = [
				'status',
				'=',
				(int)$params['status']
			];
		}
		
		// 部门搜索
		if (!empty($params['dept_id'])) {
			$where[] = [
				'dept_id',
				'=',
				$params['dept_id']
			];
		}
		
		// 排序
		$order = 'created_at desc';
		
		// 分页参数
		$page  = $params['page'] ?? 1;
		$limit = $params['limit'] ?? 10;
		
		// 关联查询
		$with = [
			'roles' => function ($query) {
				$query->field('id,role_id');
			},
			'dept'  => function ($query) {
				$query->field('id,name');
			}
		];
		
		// 使用CRUD服务的方法，自动应用租户隔离和数据权限
		$total = $this->crudService->getCount($where);
		$list  = $this->crudService->getPageList($where, $order, (int)$page, (int)$limit, $with);
		
		return [
			'list'  => $list->hidden([
				'password',
				'salt'
			]),
			'total' => $total,
			'page'  => (int)$page,
			'limit' => (int)$limit,
		];
	}
	
	/**
	 * 获取管理员详情(有数据权限)
	 *
	 * @param int $id 管理员ID
	 * @return \app\system\model\AdminModel|null
	 * @throws BusinessException
	 */
	public function getDetail(int $id): ?AdminModel
	{
		$info = $this->crudService->getOne([
			[
				'id',
				'=',
				$id
			],
			[
				'roles',
				'dept'
			]
		]);
		if ($info->isEmpty()) {
			throw new BusinessException('管理员不存在');
		}
		$roles = [];
		if (!$info->roles->isEmpty()) {
			$info->roles->each(function ($item) use (&$roles) {
				$roles[] = $item->role_id;
			});
			$info['role_ids'] = $roles;
			$info->hidden([
				'roles',
				'password',
				'salt'
			]);
		}
		
		return $info;
	}
	
	/**
	 * 处理管理员数据
	 *
	 * @param array                             $data      管理员数据
	 * @param int                               $tenantId  租户ID
	 * @param string                            $scene     验证场景
	 * @param \app\system\model\AdminModel|null $adminInfo 管理员信息（更新时使用）
	 * @return array 处理后的数据和角色ID数组
	 * @throws BusinessException
	 * @throws ValidateException
	 */
	private function processAdminData(array $data, int $tenantId, string $scene = 'add', ?AdminModel $adminInfo = null
	): array
	{
		// 数据验证
		try {
			validate(AdminValidate::class)
				->scene($scene)
				->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateException($e->getMessage());
		}
		
		// 检查用户名是否存在/重复
		if ($scene === 'add') {
			// 添加时必须检查用户名是否存在
			$existUser = $this->model->getAdminByUsername($data['username']);
			if (!$existUser->isEmpty()) {
				throw new ValidateException('用户名已存在');
			}
		}
		else if ($scene === 'edit' && isset($data['username'])) {
			// 编辑时，如果提供了用户名且与原用户名不同，则需要检查是否重复
			if ($adminInfo && $data['username'] !== $adminInfo->username) {
				$existUser = $this->model->getAdminByUsername($data['username']);
				if (!$existUser->isEmpty()) {
					throw new ValidateException('用户名已存在');
				}
			}
		}
		
		if (empty($data['dept_id'])) {
			$data['dept_id'] = 0;
		}
		
		// 处理密码
		if (!empty($data['password'])) {
			$data['password'] = $this->setPasswordAttr($data['password']);
		}
		else if ($scene === 'add') {
			// 添加时设置默认密码
			$data['password'] = $this->setPasswordAttr('123456');
		}
		else {
			// 更新时如果没有提供密码，则不修改
			unset($data['password']);
		}
		
		// 处理岗位ID
		$postIds = $data['post_ids'] ?? [];
		if (!empty($postIds)) {
			$postCount = PostService::getInstance()
			                        ->getModel()
			                        ->where([
				                        [
					                        'id',
					                        'in',
					                        $postIds
				                        ],
				                        [
					                        'status',
					                        '=',
					                        1
				                        ],
				                        [
					                        'tenant_id',
					                        '=',
					                        $tenantId
				                        ],
			                        ])
			                        ->count();
			
			if ($postCount !== count($postIds)) {
				throw new BusinessException('岗位不存在或已禁用');
			}
		}
		
		// 提取角色ID
		$roleIds = $data['role_ids'] ?? [];
		unset($data['role_ids']);
		
		// 更新时移除不允许修改的字段
		if ($scene === 'edit') {
			unset($data['creator_id']);
			unset($data['tenant_id']);
		}
		
		return [
			'data'    => $data,
			'roleIds' => $roleIds
		];
	}
	
	/**
	 * 创建管理员
	 *
	 * @param array $data     管理员数据
	 * @param int   $tenantId 租户ID
	 * @return bool
	 * @throws BusinessException
	 * @throws ValidateException|Throwable
	 */
	public function create(array $data, int $tenantId): bool
	{
		// 处理管理员数据
		$processed = $this->processAdminData($data, $tenantId, 'add');
		$data      = $processed['data'];
		$roleIds   = $processed['roleIds'];
		
		
		Db::startTrans();
		try {
			// 使用CRUD服务添加数据
			$adminId = $this->crudService->add($data);
			
			if (!$adminId) {
				throw new BusinessException('创建失败');
			}
			
			// 分配角色
			if (!empty($roleIds)) {
				$result = RoleService::getInstance()
				                     ->assignRoles($adminId, $roleIds, $tenantId, $adminId);
				
				if (!$result) {
					throw new BusinessException('角色分配失败');
				}
			}
			Db::commit();
		}
		catch (BusinessException $e) {
			Db::rollback();
			throw new BusinessException('角色分配失败');
		}
		
		return true;
	}
	
	/**
	 * 更新管理员(有数据权限)
	 *
	 * @param int   $id       管理员ID
	 * @param int   $adminId  操作者ID
	 * @param int   $tenantId 租户ID
	 * @param array $data     管理员数据
	 * @return bool
	 * @throws BusinessException
	 * @throws ValidateException|Throwable
	 */
	public function update(int $id, int $adminId, int $tenantId, array $data): bool
	{
		Db::startTrans();
		try {
			
			// 获取管理员信息
			$adminInfo = $this->crudService->getOne([
				[
					'id',
					'=',
					$id
				]
			]);
			
			if ($adminInfo->isEmpty()) {
				throw new BusinessException('管理员不存在');
			}
			
			// 处理管理员数据
			$processed = $this->processAdminData($data, $tenantId, 'edit', $adminInfo);
			$data      = $processed['data'];
			$roleIds   = $processed['roleIds'];
			
			// 使用CRUD服务更新数据
			$result = $this->crudService->edit($data, ['id' => $id]);
			
			if (!$result) {
				throw new BusinessException('更新失败');
			}
			
			// 更新角色关联
			if (!empty($roleIds)) {
				$result = RoleService::getInstance()
				                     ->assignRoles($id, $roleIds, $tenantId, $adminId);
				
				if (!$result) {
					throw new BusinessException('角色分配失败');
				}
			}
			
			DataPermissionCacheUtil::clearUserDataPermission($id, $tenantId);
			
			Db::commit();
			
			return true;
			
		}
		catch (\Exception $e) {
			
			Db::rollback();
			
			throw new BusinessException($e->getMessage());
		}
		
	}
	
	/**
	 * 删除管理员(有数据权限)
	 *
	 * @param int $id       管理员ID
	 * @param int $tenantId 租户ID
	 * @return bool
	 * @throws BusinessException
	 */
	public function delete(int $id, int $tenantId): bool
	{
		
		$adminInfo = $this->crudService->getOne([
			[
				'id',
				'=',
				$id
			]
		], ['roles']);
		
		if ($adminInfo->isEmpty()) {
			throw new BusinessException('管理员不存在');
		}
		
		// 检查是否为超级管理员
		if ($id == SystemConstant::SUPER_ADMIN_ID) {
			throw new BusinessException('无法删除超级管理员');
		}
		
		// 使用CRUD服务删除数据
		$result = $this->crudService->delete(['id' => $id]);
		
		// 清除数据权限缓存
		if ($result) {
			DataPermissionCacheUtil::clearUserDataPermission($id, $tenantId);
		}
		
		return $result;
	}
	
	/**
	 * 重置密码(有数据权限)
	 *
	 * @param int|string $id   管理员ID
	 * @param array      $data 新密码
	 * @return bool
	 * @throws BusinessException
	 * @throws ValidateException
	 */
	public function resetPassword(int|string $id, array $data): bool
	{
		if ($id == SystemConstant::SUPER_ADMIN_ID && !is_super_admin()) {
			throw new BusinessException('暂无权限');
		}
		
		try {
			validate(AdminValidate::class)
				->scene('resetPassword')
				->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateException($e->getMessage());
		}
		
		// 使用CRUD服务更新数据
		return $this->crudService->edit(['password' => $this->setPasswordAttr($data['password'])], ['id' => $id]);
	}
	
	/**
	 * 更新头像(有数据权限)
	 *
	 * @param int|string $id       管理员ID
	 * @param int|string $tenantId 租户ID
	 * @param string     $avatar   头像URL
	 * @return bool
	 * @throws BusinessException
	 * @throws ValidateException
	 */
	public function updateAvatar(int|string $id, string $avatar): bool
	{
		try {
			validate(AdminValidate::class)
				->scene('avatar')
				->check(['avatar' => $avatar]);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateException($e->getMessage());
		}
		
		// 使用CRUD服务更新数据
		return $this->crudService->edit(['avatar' => $avatar], ['id' => $id]);
	}
	
	//	changePassword
	public function changePassword(int|string $id, $data): bool
	{
		try {
			validate(AdminValidate::class)
				->scene('changePassword')
				->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateException($e->getMessage());
		}
		
		$info = $this->crudService->getOne([
			[
				'id',
				'=',
				$id
			]
		]);
		if ($info->isEmpty()) {
			throw new BusinessException('管理员不存在');
		}
		if (!password_verify($data['password'], $info->password)) {
			throw new BusinessException('当前密码错误');
		}
		
		return $info->save(['password' => $this->setPasswordAttr($data['new_password'])]);
	}
	
	
	/**
	 * 获取下拉
	 *
	 * @param array $where
	 * @return array
	 */
	public function getOptions(array $where = []): array
	{
		if (empty($where)) {
			$where = [
				[
					'status',
					'=',
					1
				]
			];
		}
		return $this->model->field('id,real_name as name,status,gender')
		                   ->where($where)
		                   ->select()
		                   ->toArray();
	}
}
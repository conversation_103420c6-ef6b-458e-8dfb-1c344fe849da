#!/usr/bin/env python3
"""
配置tenant_admin租户超级管理员权限
为租户管理员分配所有权限
"""
import os
import mysql.connector

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

def get_db_connection():
    return mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )

def main():
    print("=== 配置tenant_admin租户超级管理员权限 ===\n")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        conn.autocommit = False
        
        # 1. 确认租户超级管理员角色和用户
        print("1. 确认租户超级管理员配置:")
        
        # 查找租户超级管理员角色
        cursor.execute("""
            SELECT id, name, remark 
            FROM system_role 
            WHERE name = '租户超级管理员' OR id = 101
        """)
        tenant_admin_role = cursor.fetchone()
        
        if not tenant_admin_role:
            print("  ❌ 租户超级管理员角色不存在，正在创建...")
            cursor.execute("""
                INSERT INTO system_role (id, name, remark, status, tenant_id, created_at, updated_at)
                VALUES (101, '租户超级管理员', '租户内全部数据权限', 1, 1, NOW(), NOW())
            """)
            tenant_admin_role_id = 101
            print("  ✅ 创建租户超级管理员角色成功")
        else:
            tenant_admin_role_id = tenant_admin_role[0]
            print(f"  ✅ 找到租户超级管理员角色: ID={tenant_admin_role_id}, 名称={tenant_admin_role[1]}")
        
        # 查找tenant_admin用户
        cursor.execute("""
            SELECT id, username, real_name 
            FROM system_admin 
            WHERE username = 'tenant_admin' OR id = 201
        """)
        tenant_admin_user = cursor.fetchone()
        
        if not tenant_admin_user:
            print("  ❌ tenant_admin用户不存在，正在创建...")
            cursor.execute("""
                INSERT INTO system_admin (
                    id, username, password, real_name, email, phone, 
                    status, tenant_id, created_at, updated_at
                ) VALUES (
                    201, 'tenant_admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
                    '租户管理员', '<EMAIL>', '13800000201', 
                    1, 1, NOW(), NOW()
                )
            """)
            tenant_admin_user_id = 201
            print("  ✅ 创建tenant_admin用户成功")
        else:
            tenant_admin_user_id = tenant_admin_user[0]
            print(f"  ✅ 找到tenant_admin用户: ID={tenant_admin_user_id}, 用户名={tenant_admin_user[1]}")
        
        # 2. 为租户超级管理员分配所有权限
        print(f"\n2. 为租户超级管理员分配权限:")
        
        # 清除现有权限
        cursor.execute("DELETE FROM system_role_menu WHERE role_id = %s", (tenant_admin_role_id,))
        print(f"  ✅ 清除现有权限")
        
        # 获取所有可用权限
        cursor.execute("""
            SELECT id, name, title, type 
            FROM system_menu 
            WHERE status = 1 AND deleted_at IS NULL
            ORDER BY parent_id, sort, id
        """)
        all_permissions = cursor.fetchall()
        
        print(f"  系统总权限数: {len(all_permissions)} 个")
        
        # 分配所有权限给租户超级管理员
        assigned_count = 0
        for permission in all_permissions:
            menu_id, name, title, perm_type = permission
            cursor.execute("""
                INSERT INTO system_role_menu (role_id, menu_id, tenant_id) 
                VALUES (%s, %s, 1)
            """, (tenant_admin_role_id, menu_id))
            assigned_count += 1
        
        print(f"  ✅ 成功分配 {assigned_count} 个权限")
        
        # 3. 为tenant_admin用户分配角色
        print(f"\n3. 为tenant_admin用户分配角色:")
        
        # 清除现有角色分配
        cursor.execute("DELETE FROM system_admin_role WHERE admin_id = %s", (tenant_admin_user_id,))
        
        # 分配租户超级管理员角色
        cursor.execute("""
            INSERT INTO system_admin_role (admin_id, role_id, tenant_id) 
            VALUES (%s, %s, 1)
        """, (tenant_admin_user_id, tenant_admin_role_id))
        
        print(f"  ✅ 为用户 {tenant_admin_user_id} 分配角色 {tenant_admin_role_id}")
        
        # 4. 验证权限分配
        print(f"\n4. 验证权限分配:")
        
        # 验证角色权限
        cursor.execute("""
            SELECT COUNT(*) 
            FROM system_role_menu 
            WHERE role_id = %s
        """, (tenant_admin_role_id,))
        role_permission_count = cursor.fetchone()[0]
        
        # 验证用户权限
        cursor.execute("""
            SELECT COUNT(*) 
            FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = %s AND m.status = 1 AND m.deleted_at IS NULL
        """, (tenant_admin_user_id,))
        user_permission_count = cursor.fetchone()[0]
        
        print(f"  角色权限数: {role_permission_count} 个")
        print(f"  用户权限数: {user_permission_count} 个")
        
        # 按模块统计权限
        cursor.execute("""
            SELECT 
                SUBSTRING_INDEX(m.name, ':', 1) as module,
                COUNT(*) as count,
                SUM(CASE WHEN m.type = 1 THEN 1 ELSE 0 END) as menu_count,
                SUM(CASE WHEN m.type = 2 THEN 1 ELSE 0 END) as button_count
            FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = %s AND m.status = 1 AND m.deleted_at IS NULL 
            AND m.name LIKE '%:%'
            GROUP BY SUBSTRING_INDEX(m.name, ':', 1)
            ORDER BY count DESC
        """, (tenant_admin_user_id,))
        
        module_permissions = cursor.fetchall()
        print(f"  权限模块分布:")
        for module in module_permissions:
            module_name, total, menu_count, button_count = module
            print(f"    {module_name}: {total}个权限 (菜单:{menu_count}, 按钮:{button_count})")
        
        # 5. 测试关键权限
        print(f"\n5. 测试关键权限:")
        key_permissions = [
            'crm:crm_customer_my:index',
            'crm:crm_customer_my:add',
            'crm:crm_customer_my:edit',
            'crm:crm_customer_my:delete',
            'system:permission:admin:index',
            'system:permission:role:index',
            'system:permission:menu:index',
            'system:log:login:index',
            'system:log:operation:index',
            'project:project:index',
            'workflow:workflow_type:index',
            'notice:notice_template:index'
        ]
        
        cursor.execute("""
            SELECT m.name
            FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = %s AND m.status = 1 AND m.deleted_at IS NULL
        """, (tenant_admin_user_id,))
        
        user_permissions = [row[0] for row in cursor.fetchall()]
        
        print(f"  关键权限测试:")
        for perm in key_permissions:
            has_perm = perm in user_permissions
            status = "✅" if has_perm else "❌"
            print(f"    {status} {perm}")
        
        # 提交事务
        conn.commit()
        print(f"\n✅ 租户超级管理员权限配置完成！")
        
        # 6. 生成登录信息
        print(f"\n6. 租户超级管理员登录信息:")
        print(f"  用户名: tenant_admin")
        print(f"  密码: password (默认密码)")
        print(f"  角色: 租户超级管理员")
        print(f"  权限: 全部 {user_permission_count} 个权限")
        print(f"  说明: 拥有租户内所有功能的完整权限")
        
    except Exception as e:
        conn.rollback()
        print(f"\n❌ 配置失败: {e}")
        raise
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    main()

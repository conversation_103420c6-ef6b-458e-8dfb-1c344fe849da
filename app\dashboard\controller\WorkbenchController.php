<?php
declare(strict_types=1);

namespace app\dashboard\controller;

use app\common\core\base\BaseController;
use app\dashboard\service\WorkbenchStatisticsService;
use app\dashboard\service\TodoAggregationService;
use think\response\Json;

/**
 * 工作台控制器
 */
class WorkbenchController extends BaseController
{
    /**
     * @var WorkbenchStatisticsService
     */
    protected $statisticsService;

    /**
     * @var TodoAggregationService
     */
    protected $todoService;

    /**
     * 初始化
     */
    public function initialize(): void
    {
        parent::initialize();

        // 使用单例模式获取Service实例
        $this->statisticsService = WorkbenchStatisticsService::getInstance();
        $this->todoService = TodoAggregationService::getInstance();
    }

    /**
     * 获取关键指标统计
     * @return Json
     */
    public function getKeyStatistics(): Json
    {
        try {
            $userId = $this->request->adminId ?? 0;
            
            $data = [
                'customer_stats' => $this->statisticsService->getCustomerStatistics($userId),
                'contract_stats' => $this->statisticsService->getContractStatistics($userId),
                'project_stats' => $this->statisticsService->getProjectStatistics($userId)
            ];

            return $this->success('获取成功', $data);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取聚合待办任务
     * @return Json
     */
    public function getTodoTasks(): Json
    {
        try {
            $userId = $this->request->adminId ?? 0;
            
            if (!$userId) {
                return $this->error('用户未登录');
            }

            $data = $this->todoService->aggregateUserTodos($userId);

            return $this->success('获取成功', $data);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取工作汇报摘要
     * @return Json
     */
    public function getWorkReports(): Json
    {
        try {
            $userId = $this->request->adminId ?? 0;
            $limit = $this->request->get('limit', 5);

            $data = $this->statisticsService->getWorkReportsSummary($userId, (int)$limit);

            return $this->success('获取成功', $data);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取企业新闻
     * @return Json
     */
    public function getCompanyNews(): Json
    {
        try {
            $limit = $this->request->get('limit', 5);

            $data = $this->statisticsService->getCompanyNews((int)$limit);

            return $this->success('获取成功', $data);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取工作台所有数据（一次性接口）
     * @return Json
     */
    public function getWorkbenchData(): Json
    {
        try {
            $userId = $this->request->adminId ?? 0;
            
            if (!$userId) {
                return $this->error('用户未登录');
            }

            $data = [
                'key_statistics' => [
                    'customer_stats' => $this->statisticsService->getCustomerStatistics($userId),
                    'contract_stats' => $this->statisticsService->getContractStatistics($userId),
                    'project_stats' => $this->statisticsService->getProjectStatistics($userId)
                ],
                'todo_tasks' => $this->todoService->aggregateUserTodos($userId),
                'work_reports' => $this->statisticsService->getWorkReportsSummary($userId, 5),
                'company_news' => $this->statisticsService->getCompanyNews(5)
            ];

            return $this->success('获取成功', $data);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
}

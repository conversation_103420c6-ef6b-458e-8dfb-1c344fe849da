<template>
  <div class="quick-actions-widget art-custom-card">
    <div class="widget-header">
      <h4 class="box-title">快捷操作</h4>
    </div>

    <div class="actions-list">
      <div
        v-for="action in quickActions"
        :key="action.name"
        class="action-item"
        @click="handleActionClick(action)"
      >
        <span class="action-name">{{ action.name }}</span>
        <i class="action-arrow">›</i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'

  const router = useRouter()

  interface QuickAction {
    name: string
    path: string
    menuId?: number
  }

  // 基于system_menu表的实际数据配置
  const quickActions: QuickAction[] = [
    {
      name: '我的客户',
      path: '/crm/crm_customer_my'
    },
    {
      name: '工作汇报',
      path: '/office/crm_work_report',
      menuId: 2581
    },
    {
      name: '我的申请',
      path: '/office/workflow/application'
    },
    {
      name: '项目管理',
      path: '/project/project_list',
      menuId: 2441
    },
    {
      name: '每日报价',
      path: '/daily_price_order',
      menuId: 2600
    }
  ]

  /**
   * 处理快捷操作点击
   */
  const handleActionClick = (action: QuickAction) => {
    try {
      router.push(action.path)
    } catch (error) {
      console.error('跳转失败:', error)
      ElMessage.error(`跳转到${action.name}失败`)
    }
  }
</script>

<style lang="scss" scoped>
  .quick-actions-widget {
    height: 400px;
    padding: 20px;
    background: var(--art-main-bg-color);
    border-radius: calc(var(--custom-radius) + 4px) !important;

    .widget-header {
      margin-bottom: 20px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--art-text-color-1);
      }
    }

    .actions-list {
      height: calc(100% - 60px);

      .action-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);
        cursor: pointer;
        transition: all 0.2s ease;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: var(--el-color-primary-light-9);
          padding-left: 8px;
          padding-right: 8px;
          border-radius: 6px;

          .action-arrow {
            color: var(--el-color-primary);
            transform: translateX(4px);
          }
        }

        .action-name {
          font-size: 15px;
          font-weight: 500;
          color: var(--art-text-color-1);
          flex: 1;
        }

        .action-arrow {
          font-size: 18px;
          color: var(--art-text-color-3);
          font-weight: 300;
          transition: all 0.2s ease;
          line-height: 1;
        }
      }
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .quick-actions-widget {
      height: auto;
      padding: 16px;

      .actions-list {
        height: auto;

        .action-item {
          padding: 14px 0;

          .action-name {
            font-size: 14px;
          }

          .action-arrow {
            font-size: 16px;
          }
        }
      }
    }
  }
</style>

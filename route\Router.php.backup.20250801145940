<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/system', function () {
	
	$nameSpace = '\app\system\controller';
	
	Route::post('upload', $nameSpace . '\Upload@index');
	Route::get('getUploadToken', $nameSpace . '\Upload@getUploadToken');
	Route::get('getUploadConfig', $nameSpace . '\Upload@getUploadConfig');
	
	Route::post('logout', $nameSpace . '\Auth@logout');
	
	Route::get('admin/info', $nameSpace . '\permission\Admin@info');
	Route::get('admin/options', $nameSpace . '\permission\Admin@options');
	
	
	Route::get('admin/permissions', $nameSpace . '\permission\Admin@permissions');
	
	Route::post('admin/change_password', $nameSpace . '\permission\Admin@changePassword');
	
	Route::get('menu/options', $nameSpace . '\permission\Menu@options');
	Route::get('role/options', $nameSpace . '\permission\Role@options');
	Route::get('department/options', $nameSpace . '\permission\Department@options');
	Route::get('post/options', $nameSpace . '\permission\Post@options');
	Route::get('tenant/options', $nameSpace . '\tenant\TenantList@options');
	
	
	Route::get('api/workflow/form_field/:id', '\app\workflow\controller\TypeController@getFormField');
	
	Route::get('type/businessOptions', '\app\workflow\controller\TypeController@businessOptions');
	
	Route::get('type/options', '\app\workflow\controller\TypeController@options');
	
	Route::get('article_category/options', $nameSpace . '\ArticleCategoryController@options');

	// 每日报价相关路由
	Route::get('daily/daily_price_order/check_duplicate_date', '\app\daily\controller\DailyPriceOrderController@checkDuplicateDate');
	Route::get('daily/daily_price_order/get_yesterday_prices', '\app\daily\controller\DailyPriceOrderController@getYesterdayPrices');

})
     ->middleware([
	     TokenAuthMiddleware::class,
	     OperationLogMiddleware::class
     ]);


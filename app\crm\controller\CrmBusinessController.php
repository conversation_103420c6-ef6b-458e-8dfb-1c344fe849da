<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmBusinessService;
use app\crm\service\BusinessConversionService;
use think\response\Json;

/**
 * 商机表控制器
 */
class CrmBusinessController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	
	/**
	 * @var CrmBusinessService
	 */
	protected CrmBusinessService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = CrmBusinessService::getInstance();
	}
	
	/**
	 * 状态切换
	 */
	public function status($id)
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	/**
	 * 检查商机转化条件
	 */
	public function checkConversion($id)
	{
		try {
			$conversionService = BusinessConversionService::getInstance();
			$result            = $conversionService->checkConversionConditions((int)$id);
			
			if ($result['can_convert']) {
				return $this->success('检查完成', $result);
			}
			else {
				return $this->error($result['message']);
			}
		}
		catch (\Exception $e) {
			return $this->error('检查失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 商机转化为合同
	 */
	public function convertContract($id)
	{
		try {
			$contractData = $this->request->post('contract_data', []);
			
			$conversionService = BusinessConversionService::getInstance();
			$result            = $conversionService->convertToContract((int)$id, $contractData);
			
			return $this->success($result['message'], $result['data']);
		}
		catch (\Exception $e) {
			return $this->error('转化失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 获取下拉选项
	 *
	 * @return Json
	 */
	public function options(): Json
	{
		$params     = $this->request->param();
		$labelField = $params['label_field'] ?? 'business_name';
		$valueField = $params['value_field'] ?? 'id';
		$where      = $params['where'] ?? [];
		
		$result = $this->service->getSelectOptions($where, $labelField, $valueField);
		return $this->success('获取成功', $result);
	}
	
}
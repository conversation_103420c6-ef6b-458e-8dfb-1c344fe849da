<?php
/**
 * 测试权限修复
 */

echo "=== 测试权限修复 ===\n\n";

// 模拟修复后的权限解析逻辑
function camelToSnake($input) {
    return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
}

function generateCrmPermissionName($controllerName, $method) {
    $snakeName = camelToSnake($controllerName);
    if (!str_starts_with($snakeName, 'crm_')) {
        $snakeName = 'crm_' . $snakeName;
    }
    return $snakeName . ':' . $method;
}

function generateSystemPermissionName($parts, $controllerName, $method) {
    if (count($parts) > 4) {
        $subPath = strtolower($parts[3]);
        $controller = strtolower($controllerName);
        return $subPath . ':' . $controller . ':' . $method;
    } else {
        $controller = strtolower($controllerName);

        // 特殊处理：某些基础控制器的权限格式是 controller 而不是 controller:method
        $baseControllers = ['config', 'attachment', 'attachmentcat', 'upload'];

        if (in_array($controller, $baseControllers)) {
            return $controller;
        } else {
            return $controller . ':' . $method;
        }
    }
}

function generateProjectPermissionName($controllerName, $method) {
    $snakeName = camelToSnake($controllerName);

    // 特殊处理：如果是Project控制器，映射为project:project
    if ($controllerName === 'Project') {
        return 'project:' . $method;
    }

    // 其他控制器直接使用转换后的snake_case名称
    return $snakeName . ':' . $method;
}

function parsePermissionInfo($ruleName) {
    [$classPath, $method] = explode('@', $ruleName);
    $parts = explode('\\', $classPath);
    $module = strtolower($parts[1]);
    $controllerClass = $parts[count($parts) - 1];
    $controllerName = str_replace('Controller', '', $controllerClass);
    
    switch ($module) {
        case 'crm':
            $permissionPath = generateCrmPermissionName($controllerName, $method);
            break;
        case 'system':
            $permissionPath = generateSystemPermissionName($parts, $controllerName, $method);
            break;
        case 'project':
            $permissionPath = generateProjectPermissionName($controllerName, $method);
            break;
        default:
            $permissionPath = strtolower($module . '_' . $controllerName) . ':' . $method;
    }
    
    return [$module, $permissionPath, $method];
}

// 模拟中间件的权限构建逻辑
function buildPermission($ruleName) {
    [$module, $permissionPath, $action] = parsePermissionInfo($ruleName);
    return strtolower("{$module}:{$permissionPath}");
}

echo "1. 测试修复后的权限构建:\n";

$testCases = [
    'app\\crm\\controller\\CrmCustomerMyController@index' => 'crm:crm_customer_my:index',
    'app\\crm\\controller\\CrmLeadController@add' => 'crm:crm_lead:add',
    'app\\system\\controller\\AuthController@login' => 'system:auth:login',
    'app\\system\\controller\\ConfigController@index' => 'system:config',
    'app\\system\\controller\\permission\\AdminController@index' => 'system:permission:admin:index',
    'app\\system\\controller\\log\\LoginController@index' => 'system:log:login:index',
    'app\\project\\controller\\ProjectController@index' => 'project:project:index',
];

$successCount = 0;
$totalCount = count($testCases);

foreach ($testCases as $input => $expected) {
    $result = buildPermission($input);
    
    $status = ($result === $expected) ? '✅' : '❌';
    echo "  {$status} {$input}\n";
    echo "    期望: {$expected}\n";
    echo "    实际: {$result}\n";
    
    if ($result === $expected) {
        $successCount++;
    }
    echo "\n";
}

echo "2. 测试结果:\n";
echo "  成功: {$successCount}/{$totalCount}\n";
echo "  成功率: " . round($successCount / $totalCount * 100, 1) . "%\n";

if ($successCount === $totalCount) {
    echo "  ✅ 所有权限解析测试通过！\n";
    echo "\n3. 下一步操作:\n";
    echo "  1. 清除权限缓存\n";
    echo "  2. 重新登录系统\n";
    echo "  3. 测试权限功能\n";
} else {
    echo "  ❌ 部分权限解析测试失败\n";
}

echo "\n=== 测试完成 ===\n";

# 完整测试用户手册

**版本**: v2.0  
**创建日期**: 2025-01-31  
**适用系统**: CRM管理系统  
**技术栈**: ThinkPHP 8 + Vue 3

---

## 🎯 测试目标

本手册提供完整的权限测试用户配置，用于验证：
- ✅ 权限系统的完整性和安全性
- ✅ 不同角色的权限边界
- ✅ 菜单和按钮权限的精确控制
- ✅ 数据权限和数据隔离
- ✅ 业务流程的权限验证

---

## 👥 测试用户概览

| 序号 | 用户名 | 密码 | 真实姓名 | 角色 | 权限数 | 权限级别 |
|------|--------|------|----------|------|--------|----------|
| 1 | **tenant_admin** | password | 租户超级管理员 | 租户超级管理员 | **280个** | 🔴 超级管理员 |
| 2 | **system_admin** | password | 系统管理员 | 系统管理员 | **36个** | 🟠 系统管理员 |
| 3 | **crm_admin** | password | CRM管理员 | CRM管理员 | **53个** | 🟠 业务管理员 |
| 4 | **project_admin** | password | 项目管理员 | 项目管理员 | **13个** | 🟡 专业管理员 |
| 5 | **business_manager** | password | 业务经理 | 业务经理 | **20个** | 🟡 中级管理员 |
| 6 | **staff_user** | password | 普通员工 | 普通员工 | **11个** | 🟢 普通用户 |

---

## 🔴 租户超级管理员 (tenant_admin)

### 基本信息
- **用户ID**: 201
- **用户名**: tenant_admin
- **密码**: password
- **真实姓名**: 租户超级管理员
- **角色**: 租户超级管理员
- **邮箱**: <EMAIL>
- **手机**: ***********

### 权限配置
- **总权限数**: 280个 (100%覆盖率)
- **权限范围**: 租户内所有功能的完整权限，包括所有菜单和按钮权限

#### 权限模块分布
| 模块 | 权限数 | 菜单权限 | 按钮权限 | 主要功能 |
|------|--------|----------|----------|----------|
| **CRM** | 116个 | 15个 | 101个 | 客户、线索、商机、合同全功能 |
| **系统管理** | 78个 | 27个 | 51个 | 用户、角色、权限、菜单管理 |
| **工作流** | 27个 | 5个 | 22个 | 流程设计、审批管理 |
| **项目管理** | 21个 | 3个 | 18个 | 项目、任务全功能 |
| **通知管理** | 10个 | 2个 | 8个 | 消息模板、通知配置 |
| **每日报价** | 9个 | 1个 | 8个 | 报价管理 |
| **库存管理** | 6个 | 0个 | 6个 | 供应商、库存管理 |
| **办公管理** | 3个 | 3个 | 0个 | 办公功能 |

### 测试重点
- ✅ **系统管理**: 用户、角色、权限、菜单的完整管理
- ✅ **数据管理**: 所有模块的数据增删改查
- ✅ **权限分配**: 为其他用户分配和调整权限
- ✅ **系统监控**: 日志查看、系统配置
- ✅ **业务管理**: 所有业务模块的完整功能

---

## 🟠 系统管理员 (system_admin)

### 基本信息
- **用户ID**: 202
- **用户名**: system_admin
- **密码**: password
- **真实姓名**: 系统管理员
- **角色**: 系统管理员
- **邮箱**: <EMAIL>
- **手机**: 13800000202

### 权限配置
- **总权限数**: 36个
- **权限特点**: 专注系统管理，包含完整的用户、角色、权限管理功能

#### 核心权限
- ✅ **用户管理**: 用户增删改查、用户详情、用户导出
- ✅ **角色管理**: 角色增删改查、角色详情
- ✅ **权限管理**: 菜单增删改查、菜单详情
- ✅ **部门管理**: 部门增删改查、部门详情
- ✅ **岗位管理**: 岗位增删改查、岗位详情
- ✅ **日志管理**: 登录日志、操作日志查看和导出
- ✅ **系统配置**: 系统参数配置
- ✅ **附件管理**: 附件管理和分类
- ✅ **租户配置**: 租户参数配置
- ❌ **业务数据**: 仅有基础CRM查看权限

### 测试重点
- ✅ **权限分配**: 创建角色并分配权限
- ✅ **用户管理**: 创建用户并分配角色
- ✅ **系统配置**: 修改系统参数
- ✅ **日志审计**: 查看系统操作日志
- ❌ **业务操作**: 验证无法进行业务数据操作

---

## 🟠 CRM管理员 (crm_admin)

### 基本信息
- **用户ID**: 203
- **用户名**: crm_admin
- **密码**: password
- **真实姓名**: CRM管理员
- **角色**: CRM管理员
- **邮箱**: <EMAIL>
- **手机**: 13800000203

### 权限配置
- **总权限数**: 53个
- **权限特点**: CRM模块完整权限，包括所有客户、线索、商机、合同管理功能

#### 核心权限
- ✅ **客户管理**: 我的客户、公海客户、客户共享的完整管理
- ✅ **线索管理**: 线索和线索池的完整管理
- ✅ **商机管理**: 商机和商机产品的完整管理
- ✅ **合同管理**: 合同、合同产品、应收款的完整管理
- ✅ **联系人管理**: 客户联系人管理
- ✅ **跟进记录**: 客户跟进记录管理
- ✅ **产品管理**: 产品、产品分类、规格、单位管理
- ✅ **标签管理**: 客户标签管理
- ✅ **公海规则**: 客户公海规则配置
- ✅ **工作报告**: 工作报告和模板管理
- ❌ **系统管理**: 无系统管理权限

### 测试数据
- **创建客户**: 2个 (北京科技有限公司、广州服务公司)
- **负责客户**: 2个 (北京科技有限公司、杭州互联网公司)
- **创建线索**: 1个 (孙主任)
- **负责线索**: 1个 (孙主任)

### 测试重点
- ✅ **客户全流程**: 客户创建、编辑、删除、转移、导入导出
- ✅ **线索转化**: 线索创建、跟进、转化为客户
- ✅ **商机管理**: 商机创建、产品关联、状态跟进
- ✅ **合同管理**: 合同创建、产品关联、应收款管理
- ✅ **数据权限**: 只能看到自己创建和负责的数据

---

## 🟡 项目管理员 (project_admin)

### 基本信息
- **用户ID**: 204
- **用户名**: project_admin
- **密码**: password
- **真实姓名**: 项目管理员
- **角色**: 项目管理员
- **邮箱**: <EMAIL>
- **手机**: 13800000204

### 权限配置
- **总权限数**: 13个
- **权限特点**: 项目管理完整权限，包括项目、任务、成员管理

#### 核心权限
- ✅ **项目管理**: 项目增删改查、项目详情、项目导出
- ✅ **项目成员**: 项目成员增删改查
- ✅ **任务管理**: 项目任务增删改查、任务详情
- ✅ **任务日志**: 任务日志增删改查
- ✅ **任务成员**: 任务成员增删改查
- ✅ **基础CRM**: 客户和线索的查看权限
- ❌ **系统管理**: 无系统管理权限

### 测试数据
- **创建项目**: 2个 (CRM系统升级项目、客户数据迁移项目)
- **负责项目**: 1个 (权限系统测试项目)

### 测试重点
- ✅ **项目创建**: 创建项目并设置基本信息
- ✅ **成员管理**: 添加项目成员并分配角色
- ✅ **任务管理**: 创建任务、分配成员、跟踪进度
- ✅ **进度跟踪**: 任务日志记录和进度更新
- ❌ **CRM操作**: 验证只能查看CRM数据，无法操作

---

## 🟡 业务经理 (business_manager)

### 基本信息
- **用户ID**: 205
- **用户名**: business_manager
- **密码**: password
- **真实姓名**: 业务经理
- **角色**: 业务经理
- **邮箱**: <EMAIL>
- **手机**: ***********

### 权限配置
- **总权限数**: 20个
- **权限特点**: CRM和项目的部分管理权限，适合中层管理人员

#### 核心权限
- ✅ **客户管理**: 客户增改查、客户详情 (无删除权限)
- ✅ **线索管理**: 线索增改查、线索详情
- ✅ **商机管理**: 商机增改查、商机详情
- ✅ **合同查看**: 合同查看、合同详情 (无操作权限)
- ✅ **联系人管理**: 联系人增改查、联系人详情
- ✅ **跟进记录**: 跟进记录增改查
- ✅ **项目查看**: 项目查看、项目详情 (无操作权限)
- ✅ **任务查看**: 任务查看、任务详情 (无操作权限)
- ❌ **客户删除**: 无客户删除权限
- ❌ **系统管理**: 无系统管理权限

### 测试数据
- **创建客户**: 2个 (上海贸易集团、杭州互联网公司)
- **负责客户**: 2个 (上海贸易集团、广州服务公司)
- **创建线索**: 1个 (钱总)
- **负责线索**: 1个 (钱总)
- **负责项目**: 1个 (客户数据迁移项目)

### 测试重点
- ✅ **客户管理**: 客户创建、编辑、查看
- ✅ **线索跟进**: 线索创建、跟进、状态更新
- ✅ **商机管理**: 商机创建、编辑、状态跟进
- ✅ **权限边界**: 验证无法删除客户
- ✅ **数据权限**: 只能操作自己创建和负责的数据
- ❌ **项目操作**: 验证只能查看项目，无法操作

---

## 🟢 普通员工 (staff_user)

### 基本信息
- **用户ID**: 206
- **用户名**: staff_user
- **密码**: password
- **真实姓名**: 普通员工
- **角色**: 普通员工
- **邮箱**: <EMAIL>
- **手机**: 13800000206

### 权限配置
- **总权限数**: 11个
- **权限特点**: 基础业务权限，主要是查看和基础操作

#### 核心权限
- ✅ **客户查看**: 客户查看、客户详情
- ✅ **线索管理**: 线索增加、线索详情 (无编辑删除权限)
- ✅ **商机查看**: 商机查看、商机详情
- ✅ **联系人查看**: 联系人查看、联系人详情
- ✅ **跟进记录**: 跟进记录增加
- ✅ **项目查看**: 项目查看、项目详情
- ✅ **任务查看**: 任务查看、任务详情
- ❌ **客户操作**: 无客户创建、编辑、删除权限
- ❌ **商机操作**: 无商机操作权限
- ❌ **系统管理**: 无系统管理权限

### 测试数据
- **创建客户**: 1个 (深圳制造企业)
- **负责客户**: 1个 (深圳制造企业)
- **创建线索**: 1个 (赵经理)
- **负责线索**: 1个 (赵经理)

### 测试重点
- ✅ **基础查看**: 验证可以查看客户、商机、项目信息
- ✅ **线索操作**: 创建线索、添加跟进记录
- ✅ **权限边界**: 验证无法创建、编辑客户
- ✅ **数据权限**: 只能看到自己创建和负责的数据
- ❌ **高级操作**: 验证无法进行删除、导出等操作

---

## 🧪 测试场景

### 1. 权限验证测试
#### 登录测试
- 使用每个测试账号登录系统
- 验证登录成功后的菜单显示
- 检查按钮权限是否正确显示

#### 功能权限测试
- 验证每个角色只能访问被授权的功能
- 尝试访问无权限的功能，验证是否被正确拦截
- 检查按钮级别的权限控制

### 2. 数据权限测试
#### 数据可见性测试
- 验证用户只能看到自己创建和负责的数据
- 测试数据列表的过滤是否正确
- 检查详情页面的数据权限

#### 数据操作权限测试
- 验证用户只能操作有权限的数据
- 测试跨用户数据操作是否被拦截
- 检查数据修改权限的边界

### 3. 业务流程测试
#### CRM业务流程
- **线索转客户**: 普通员工创建线索 → CRM管理员转化为客户
- **客户分配**: CRM管理员创建客户 → 分配给业务经理
- **商机跟进**: 业务经理创建商机 → 跟进状态更新
- **合同管理**: CRM管理员创建合同 → 应收款管理

#### 项目管理流程
- **项目创建**: 项目管理员创建项目 → 分配给业务经理
- **任务分配**: 项目管理员创建任务 → 分配给团队成员
- **进度跟踪**: 任务执行者更新进度 → 项目管理员查看

### 4. 系统管理测试
#### 用户管理测试
- 系统管理员创建新用户
- 分配角色和权限
- 修改用户状态和信息

#### 权限管理测试
- 创建新角色
- 分配权限给角色
- 修改现有角色权限

---

## 🔒 安全测试

### 1. 越权测试
- 尝试访问无权限的URL
- 尝试操作无权限的数据
- 验证API接口的权限验证

### 2. 数据泄露测试
- 验证用户无法看到其他用户的私有数据
- 检查数据导出功能的权限控制
- 测试数据接口的安全性

### 3. 权限提升测试
- 尝试修改自己的权限
- 验证权限分配的安全性
- 检查角色权限的继承关系

---

## 📊 测试报告模板

### 测试结果记录
| 测试项目 | 测试用户 | 预期结果 | 实际结果 | 状态 | 备注 |
|----------|----------|----------|----------|------|------|
| 登录功能 | tenant_admin | 成功登录 | | ✅/❌ | |
| 菜单显示 | system_admin | 显示系统管理菜单 | | ✅/❌ | |
| 权限验证 | crm_admin | 可访问CRM功能 | | ✅/❌ | |
| 数据权限 | business_manager | 只看到自己的数据 | | ✅/❌ | |
| 越权测试 | staff_user | 无法访问管理功能 | | ✅/❌ | |

### 问题记录
| 问题ID | 发现时间 | 测试用户 | 问题描述 | 严重程度 | 状态 |
|--------|----------|----------|----------|----------|------|
| P001 | 2025-01-31 | crm_admin | 无法删除客户 | 中 | 待修复 |

---

## 📞 技术支持

### 密码重置
如需重置测试用户密码：
```sql
UPDATE system_admin 
SET password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' 
WHERE username = 'tenant_admin';
-- 重置密码为: password
```

### 权限调整
如需调整测试用户权限，请参考权限配置文档或联系开发团队。

### 数据重置
如需重置测试数据，请运行相应的数据初始化脚本。

---

**最后更新**: 2025-01-31  
**文档维护**: 开发团队  
**测试负责人**: 系统架构师

# 权限测试用户说明

**版本**: v2.0  
**创建日期**: 2025-01-31  
**适用系统**: CRM管理系统  
**技术栈**: ThinkPHP 8 + Vue 3

---

## 📋 测试用户概览

系统已配置完整的权限测试用户，涵盖从超级管理员到普通员工的所有角色层级，用于验证权限系统的完整性和安全性。

### 用户角色层级

| 序号 | 用户名 | 真实姓名 | 角色 | 权限数 | 权限级别 |
|------|--------|----------|------|--------|----------|
| 1 | **tenant_admin** | 租户管理员 | 租户超级管理员 | **280个** | 🔴 超级管理员 |
| 2 | **sales_manager** | 李经理 | 部门经理 | **30个** | 🟠 高级管理员 |
| 3 | **sales_leader1** | 王组长 | 组长 | **15个** | 🟡 中级管理员 |
| 4 | **custom_user** | 周测试 | 自定义权限 | **15个** | 🟡 中级管理员 |
| 5 | **sales_staff1** | 张员工 | 普通员工 | **10个** | 🟢 普通用户 |

---

## 🔴 租户超级管理员 (tenant_admin)

### 基本信息
- **用户ID**: 201
- **用户名**: tenant_admin
- **密码**: password (建议首次登录后修改)
- **真实姓名**: 租户管理员
- **角色**: 租户超级管理员
- **租户ID**: 1

### 权限配置
- **总权限数**: 280个 (100%覆盖率)
- **权限范围**: 租户内所有功能的完整权限

#### 权限模块分布
| 模块 | 权限数 | 菜单权限 | 按钮权限 |
|------|--------|----------|----------|
| **CRM** | 116个 | 15个 | 101个 |
| **系统管理** | 78个 | 27个 | 51个 |
| **工作流** | 27个 | 5个 | 22个 |
| **项目管理** | 21个 | 3个 | 18个 |
| **通知管理** | 10个 | 2个 | 8个 |
| **每日报价** | 9个 | 1个 | 8个 |
| **库存管理** | 6个 | 0个 | 6个 |
| **办公管理** | 3个 | 3个 | 0个 |
| **用户管理** | 1个 | 1个 | 0个 |

### 核心权限
- ✅ **CRM管理**: 客户、线索、商机、合同等全部功能
- ✅ **系统管理**: 用户、角色、权限、菜单管理
- ✅ **日志管理**: 登录日志、操作日志查看
- ✅ **项目管理**: 项目、任务全部功能
- ✅ **工作流管理**: 流程设计、审批管理
- ✅ **通知管理**: 消息模板、通知配置
- ✅ **数据管理**: 导入、导出、统计分析

### 使用场景
- 系统初始化和配置
- 用户和角色管理
- 权限分配和调整
- 系统监控和维护
- 数据备份和恢复

---

## 🟠 部门经理 (sales_manager)

### 基本信息
- **用户ID**: 202
- **用户名**: sales_manager
- **真实姓名**: 李经理
- **角色**: 部门经理
- **部门**: 销售部 (ID: 102)

### 权限配置
- **总权限数**: 30个
- **权限特点**: 业务管理 + 部分系统管理权限

#### 权限模块分布
| 模块 | 权限数 | 主要功能 |
|------|--------|----------|
| **CRM** | 21个 | 客户、线索、商机、合同完整管理 |
| **系统管理** | 5个 | 用户管理、日志查看 |
| **项目管理** | 4个 | 项目创建、编辑、查看 |

### 核心权限
- ✅ **CRM完整权限**: 增删改查、导入导出
- ✅ **用户管理**: 查看用户列表、用户详情
- ✅ **日志查看**: 登录日志、操作日志
- ✅ **项目管理**: 项目创建、编辑、分配
- ❌ **系统配置**: 无权限修改系统设置
- ❌ **角色权限**: 无权限管理角色和权限

### 测试数据
- **创建客户**: 2个
- **负责客户**: 2个
- **创建线索**: 1个
- **创建项目**: 1个

---

## 🟡 组长 (sales_leader1)

### 基本信息
- **用户ID**: 203
- **用户名**: sales_leader1
- **真实姓名**: 王组长
- **角色**: 组长
- **部门**: 销售一组 (ID: 103)

### 权限配置
- **总权限数**: 15个
- **权限特点**: 业务操作权限，无系统管理权限

#### 权限模块分布
| 模块 | 权限数 | 主要功能 |
|------|--------|----------|
| **CRM** | 13个 | 客户、线索基础管理 |
| **项目管理** | 2个 | 项目查看 |

### 核心权限
- ✅ **CRM基础权限**: 查看、新增、编辑（无删除）
- ✅ **项目查看**: 项目列表、项目详情
- ❌ **客户删除**: 无删除权限
- ❌ **项目创建**: 无创建权限
- ❌ **系统管理**: 无任何系统管理权限

### 测试数据
- **创建客户**: 2个
- **负责客户**: 2个
- **创建线索**: 1个
- **创建项目**: 0个

---

## 🟢 普通员工 (sales_staff1)

### 基本信息
- **用户ID**: 204
- **用户名**: sales_staff1
- **真实姓名**: 张员工
- **角色**: 普通员工
- **部门**: 销售一组 (ID: 103)

### 权限配置
- **总权限数**: 10个
- **权限特点**: 基础业务查看和操作权限

#### 权限模块分布
| 模块 | 权限数 | 主要功能 |
|------|--------|----------|
| **CRM** | 8个 | 基础查看和线索管理 |
| **项目管理** | 2个 | 项目查看 |

### 核心权限
- ✅ **客户查看**: 客户列表、客户详情
- ✅ **线索管理**: 线索增删改查
- ✅ **项目查看**: 项目列表、项目详情
- ❌ **客户编辑**: 无客户编辑权限
- ❌ **客户创建**: 无客户创建权限
- ❌ **项目管理**: 无项目管理权限

### 测试数据
- **创建客户**: 2个
- **负责客户**: 2个
- **创建线索**: 1个
- **创建项目**: 0个

---

## 🟡 自定义权限用户 (custom_user)

### 基本信息
- **用户ID**: 209
- **用户名**: custom_user
- **真实姓名**: 周测试
- **角色**: 自定义权限
- **部门**: 技术部 (ID: 105)

### 权限配置
- **总权限数**: 15个
- **权限特点**: 自定义业务权限组合

#### 权限模块分布
| 模块 | 权限数 | 主要功能 |
|------|--------|----------|
| **CRM** | 11个 | 客户、线索、商机管理 |
| **项目管理** | 4个 | 项目完整管理 |

### 核心权限
- ✅ **CRM管理**: 客户、线索、商机增删改查
- ✅ **项目管理**: 项目创建、编辑、查看、删除
- ❌ **系统管理**: 无系统管理权限

### 测试数据
- **创建客户**: 1个
- **负责客户**: 1个
- **创建线索**: 1个
- **创建项目**: 1个

---

## 🔒 安全性配置

### 权限层级设计
1. **超级管理员**: 1个用户，拥有全部权限
2. **高级管理员**: 1个用户，拥有业务+部分系统权限
3. **中级管理员**: 2个用户，拥有业务权限
4. **普通用户**: 1个用户，拥有基础权限

### 权限覆盖率
- **CRM模块**: 100%覆盖率（所有用户都有CRM权限）
- **项目模块**: 100%覆盖率（所有用户都有项目权限）
- **系统模块**: 40%覆盖率（仅管理员有系统权限）
- **其他模块**: 20%覆盖率（仅超级管理员有权限）

### 数据权限
- **数据隔离**: 基于租户ID的数据隔离
- **创建者权限**: 用户可以管理自己创建的数据
- **负责人权限**: 用户可以管理自己负责的数据
- **部门权限**: 部门经理可以查看部门数据

---

## 🧪 测试建议

### 功能测试
1. **登录测试**: 使用各个测试账号登录系统
2. **权限验证**: 验证每个角色的权限边界
3. **数据访问**: 测试数据权限和数据隔离
4. **操作日志**: 验证操作日志记录功能

### 安全测试
1. **越权测试**: 尝试访问无权限的功能
2. **数据泄露**: 验证数据访问边界
3. **权限提升**: 测试权限提升攻击
4. **会话安全**: 验证会话管理和超时

### 性能测试
1. **权限查询**: 测试权限验证的性能
2. **大数据量**: 测试大量权限的性能影响
3. **并发访问**: 测试多用户并发访问

---

## 📞 技术支持

### 密码重置
如需重置测试用户密码，请联系系统管理员或使用以下SQL：
```sql
UPDATE system_admin 
SET password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' 
WHERE username = 'tenant_admin';
-- 密码重置为: password
```

### 权限调整
如需调整测试用户权限，请参考权限配置文档或联系开发团队。

### 问题反馈
如在测试过程中发现问题，请及时反馈给开发团队，包含：
- 用户账号
- 操作步骤
- 预期结果
- 实际结果
- 错误信息

---

**最后更新**: 2025-01-31  
**文档维护**: 开发团队

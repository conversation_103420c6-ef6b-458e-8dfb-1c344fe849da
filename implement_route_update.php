<?php
/**
 * 实施路由文件修改
 */

echo "=== 开始实施路由文件修改 ===\n\n";

// 需要修改的路由文件
$routeFiles = [
    'route/hr.php',
    'route/Auth.php', 
    'route/Router.php',
    'route/System.php'
];

// 控制器名称映射
$controllerMap = [
    'HrMonthlyStats' => 'HrMonthlyStatsController',
    'Attachment' => 'AttachmentController',
    'AttachmentCat' => 'AttachmentCatController',
    'Auth' => 'AuthController',
    'Config' => 'ConfigController',
    'Upload' => 'UploadController',
    'Login' => 'LoginController',
    'Operation' => 'OperationController',
    'Admin' => 'AdminController',
    'Department' => 'DepartmentController',
    'Menu' => 'MenuController',
    'Post' => 'PostController',
    'Role' => 'RoleController',
    'TenantConfig' => 'TenantConfigController',
];

$totalUpdates = 0;
$successFiles = 0;
$errorFiles = 0;

echo "1. 开始修改路由文件:\n";

foreach ($routeFiles as $routeFile) {
    echo "  处理文件: {$routeFile}\n";
    
    if (!file_exists($routeFile)) {
        echo "    ❌ 文件不存在: {$routeFile}\n";
        $errorFiles++;
        continue;
    }
    
    // 备份原文件
    $backupFile = $routeFile . '.backup.' . date('YmdHis');
    if (!copy($routeFile, $backupFile)) {
        echo "    ❌ 备份文件失败: {$routeFile}\n";
        $errorFiles++;
        continue;
    }
    echo "    ✅ 已备份到: {$backupFile}\n";
    
    // 读取文件内容
    $content = file_get_contents($routeFile);
    $originalContent = $content;
    $fileUpdates = 0;
    
    // 执行替换
    foreach ($controllerMap as $oldController => $newController) {
        // 替换各种格式的引用
        $patterns = [
            // $nameSpace . '\ControllerName@method'
            "/(\\\$nameSpace\s*\.\s*['\"][^'\"]*\\\\{$oldController})(@\w+)['\"]/",
            // 'app\module\controller\ControllerName@method'
            "/(['\"]app\\\\[^\\\\]+\\\\controller\\\\[^\\\\]*\\\\{$oldController})(@\w+)['\"]/",
            // '\ControllerName@method'
            "/(['\"]\\\\{$oldController})(@\w+)['\"]/",
            // 'ControllerName@method'
            "/(['\"](?!.*\\\\){$oldController})(@\w+)['\"]/",
        ];
        
        foreach ($patterns as $pattern) {
            $newContent = preg_replace_callback(
                $pattern,
                function($matches) use ($oldController, $newController) {
                    return str_replace($oldController, $newController, $matches[0]);
                },
                $content
            );
            
            if ($newContent !== $content) {
                $updateCount = preg_match_all($pattern, $content);
                $fileUpdates += $updateCount;
                $content = $newContent;
                echo "    ✅ 替换 {$oldController} → {$newController}: {$updateCount} 处\n";
            }
        }
    }
    
    // 写入修改后的内容
    if ($content !== $originalContent) {
        if (file_put_contents($routeFile, $content) !== false) {
            echo "    ✅ 文件修改成功，共更新 {$fileUpdates} 处引用\n";
            $totalUpdates += $fileUpdates;
            $successFiles++;
        } else {
            echo "    ❌ 文件写入失败: {$routeFile}\n";
            $errorFiles++;
        }
    } else {
        echo "    ℹ️ 文件无需修改\n";
        $successFiles++;
    }
    
    echo "\n";
}

echo "2. 路由文件修改结果统计:\n";
echo "  成功处理: {$successFiles} 个文件\n";
echo "  处理失败: {$errorFiles} 个文件\n";
echo "  总更新数: {$totalUpdates} 处引用\n\n";

if ($successFiles > 0 && $errorFiles == 0) {
    echo "✅ 路由文件修改完成！\n";
} else {
    echo "⚠️ 路由文件修改部分完成，请检查错误信息\n";
}

echo "\n=== 路由文件修改完成 ===\n";

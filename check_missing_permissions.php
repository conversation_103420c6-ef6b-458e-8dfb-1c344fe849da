<?php
/**
 * 检查缺失的权限
 */

require_once 'vendor/autoload.php';

echo "=== 检查缺失的权限 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 查找包含'auth'的权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE '%auth%' AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $authPermissions = $stmt->fetchAll();
    
    if (empty($authPermissions)) {
        echo "  ❌ 没有找到包含'auth'的权限\n";
        echo "  这说明AuthController可能不需要权限验证\n";
    } else {
        foreach ($authPermissions as $perm) {
            $type = $perm['type'] == 1 ? '菜单' : '按钮';
            echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
        }
    }
    
    echo "\n2. 查找包含'config'的权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE '%config%' AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $configPermissions = $stmt->fetchAll();
    
    if (empty($configPermissions)) {
        echo "  ❌ 没有找到包含'config'的权限\n";
        echo "  这说明ConfigController可能不需要权限验证\n";
    } else {
        foreach ($configPermissions as $perm) {
            $type = $perm['type'] == 1 ? '菜单' : '按钮';
            echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
        }
    }
    
    echo "\n3. 查找system模块的基础权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'system:%' 
        AND name NOT LIKE 'system:%:%'
        AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $systemBasePermissions = $stmt->fetchAll();
    
    echo "  system模块基础权限:\n";
    foreach ($systemBasePermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "    {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n4. 分析结论:\n";
    
    if (empty($authPermissions)) {
        echo "  AuthController@login 可能不需要权限验证（登录功能）\n";
    }
    
    if (empty($configPermissions)) {
        echo "  ConfigController@index 可能不需要权限验证（基础配置）\n";
    }
    
    echo "\n5. 建议的解决方案:\n";
    echo "  1. 对于不需要权限验证的控制器，在路由中不使用PermissionMiddleware\n";
    echo "  2. 或者在PermissionMiddleware中添加白名单，跳过这些控制器的权限验证\n";
    echo "  3. 或者在数据库中添加这些权限\n";
    
    echo "\n6. 检查路由配置:\n";
    echo "  需要检查这些控制器在路由文件中是否配置了权限中间件\n";
    echo "  如果配置了但数据库中没有对应权限，就会出现403错误\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";

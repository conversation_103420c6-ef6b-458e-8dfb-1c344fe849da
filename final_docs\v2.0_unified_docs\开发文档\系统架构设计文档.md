# 系统架构设计文档

**版本**: v2.0  
**更新日期**: 2025-01-31  
**适用系统**: CRM管理系统

---

## 🏗️ 系统整体架构

### 技术栈
- **后端框架**: ThinkPHP 8.0
- **前端框架**: Vue 3 + Element Plus
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **Web服务器**: Nginx
- **PHP版本**: PHP 8.1+

### 架构模式
- **MVC架构**: Model-View-Controller分层架构
- **模块化设计**: 按业务功能划分模块
- **RESTful API**: 统一的API接口设计
- **权限控制**: 基于RBAC的权限管理

---

## 📁 目录结构

### 后端目录结构
```
app/
├── common/                 # 公共模块
│   ├── controller/        # 公共控制器
│   ├── middleware/        # 中间件
│   ├── model/            # 公共模型
│   └── service/          # 公共服务
├── system/               # 系统管理模块
│   ├── controller/       # 控制器
│   │   └── permission/   # 权限管理子模块
│   ├── model/           # 模型
│   └── service/         # 服务层
├── crm/                 # CRM模块
├── project/             # 项目管理模块
├── workflow/            # 工作流模块
├── notice/              # 通知模块
├── office/              # 办公模块
├── daily/               # 每日报价模块
└── ims/                 # 库存管理模块
```

### 路由文件结构
```
route/
├── Auth.php             # 认证路由
├── System.php           # 系统管理路由
├── Crm.php             # CRM模块路由
├── Project.php         # 项目管理路由
├── Workflow.php        # 工作流路由
├── Notice.php          # 通知模块路由
├── Office.php          # 办公模块路由
├── Daily.php           # 每日报价路由
├── Ims.php             # 库存管理路由
└── Router.php          # 主路由文件
```

---

## 🔐 权限系统架构

### 权限模型
- **用户(Admin)**: 系统用户
- **角色(Role)**: 用户角色
- **权限(Menu)**: 功能权限
- **租户(Tenant)**: 多租户隔离

### 权限格式规范
```
格式: module:controller:action
示例:
- crm:customer_my:index
- system:permission_admin:add
- project:project:edit
- workflow:task:delete
```

### 权限验证流程
1. **Token验证**: TokenAuthMiddleware验证用户身份
2. **权限解析**: PermissionService解析当前请求的权限名称
3. **权限检查**: PermissionMiddleware检查用户是否拥有该权限
4. **访问控制**: 根据权限检查结果允许或拒绝访问

---

## 🌐 API设计规范

### RESTful API规范
```
GET    /api/module/resource          # 获取资源列表
GET    /api/module/resource/:id      # 获取单个资源
POST   /api/module/resource          # 创建资源
PUT    /api/module/resource/:id      # 更新资源
DELETE /api/module/resource/:id      # 删除资源
```

### 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    },
    "timestamp": 1643723400
}
```

### 错误码规范
- **200**: 成功
- **400**: 请求参数错误
- **401**: 未授权
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器内部错误

---

## 🔄 数据流架构

### 请求处理流程
```
用户请求 → Nginx → PHP-FPM → ThinkPHP路由 → 中间件 → 控制器 → 服务层 → 模型层 → 数据库
```

### 中间件链
```
TokenAuthMiddleware → PermissionMiddleware → OperationLogMiddleware → Controller
```

### 服务层设计
- **业务逻辑封装**: 将复杂业务逻辑封装在服务层
- **数据处理**: 统一的数据验证和处理
- **缓存管理**: 统一的缓存策略
- **事务管理**: 数据库事务的统一管理

---

## 📊 数据库设计

### 核心表结构
- **system_admin**: 用户表
- **system_role**: 角色表
- **system_menu**: 权限菜单表
- **system_admin_role**: 用户角色关联表
- **system_role_menu**: 角色权限关联表
- **system_tenant**: 租户表

### 数据库设计原则
- **规范化设计**: 遵循数据库范式
- **索引优化**: 合理设计索引提升查询性能
- **软删除**: 使用deleted_at字段实现软删除
- **时间戳**: 统一使用created_at和updated_at字段
- **多租户**: 通过tenant_id实现数据隔离

---

## 🚀 性能优化

### 缓存策略
- **权限缓存**: 缓存用户权限信息
- **菜单缓存**: 缓存用户菜单数据
- **配置缓存**: 缓存系统配置信息
- **查询缓存**: 缓存频繁查询的数据

### 数据库优化
- **索引优化**: 为常用查询字段添加索引
- **查询优化**: 避免N+1查询问题
- **连接池**: 使用数据库连接池
- **读写分离**: 支持主从数据库配置

### 前端优化
- **组件懒加载**: 按需加载Vue组件
- **路由懒加载**: 按需加载路由组件
- **资源压缩**: 压缩CSS和JavaScript文件
- **CDN加速**: 使用CDN加速静态资源

---

## 🔧 扩展性设计

### 模块化设计
- **独立模块**: 每个业务模块相对独立
- **统一接口**: 模块间通过统一接口通信
- **插件机制**: 支持插件式功能扩展
- **配置驱动**: 通过配置文件控制功能开关

### 多租户支持
- **数据隔离**: 通过tenant_id实现数据隔离
- **权限隔离**: 租户间权限完全隔离
- **配置隔离**: 支持租户级别的配置
- **资源隔离**: 支持租户级别的资源配额

### API版本管理
- **版本控制**: 支持API版本管理
- **向后兼容**: 保持API向后兼容性
- **文档管理**: 维护不同版本的API文档
- **废弃策略**: 合理的API废弃策略

---

## 📈 监控与日志

### 日志系统
- **操作日志**: 记录用户操作行为
- **错误日志**: 记录系统错误信息
- **性能日志**: 记录系统性能指标
- **安全日志**: 记录安全相关事件

### 监控指标
- **系统性能**: CPU、内存、磁盘使用率
- **数据库性能**: 查询响应时间、连接数
- **API性能**: 接口响应时间、错误率
- **用户行为**: 用户活跃度、功能使用率

---

## 🔒 安全架构

### 身份认证
- **JWT Token**: 基于JWT的身份认证
- **Token刷新**: 支持Token自动刷新
- **多设备登录**: 支持多设备同时登录
- **登录限制**: 支持登录失败次数限制

### 数据安全
- **数据加密**: 敏感数据加密存储
- **传输加密**: HTTPS加密传输
- **SQL注入防护**: 参数化查询防止SQL注入
- **XSS防护**: 输出过滤防止XSS攻击

### 权限控制
- **最小权限原则**: 用户只拥有必要的权限
- **权限继承**: 支持角色权限继承
- **动态权限**: 支持动态权限分配
- **权限审计**: 记录权限变更历史

---

**本文档将随着系统的发展持续更新，请关注最新版本。**

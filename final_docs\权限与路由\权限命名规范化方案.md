# 权限命名规范化方案

**版本**: v1.0  
**创建日期**: 2025-01-31  
**适用系统**: CRM管理系统  
**技术栈**: ThinkPHP 8 + Vue 3

---

## 🎯 问题分析

### 当前权限命名现状

基于对system_menu表的深入分析，发现以下问题：

#### 1. **命名模式不统一**
- **总权限数**: 279个
- **命名模式**: 存在5种不同的命名格式

| 模式 | 格式 | 示例 | 数量 |
|------|------|------|------|
| **三段式下划线** | `module:controller_name:action` | `crm:crm_customer_my:index` | 133个 |
| **三段式简单** | `module:controller:action` | `notice:message:delete` | 77个 |
| **二段式** | `module:controller` | `office:console` | 6个 |
| **四段式** | `module:sub:controller:action` | `system:log:login:index` | 38个 |
| **单词** | `word` | `article` | 9个 |

#### 2. **模块内部不一致**

| 模块 | 下划线格式 | 简单格式 | 其他格式 | 不一致性 |
|------|------------|----------|----------|----------|
| **crm** | 112个 | 1个 | 3个 | ❌ 严重不一致 |
| **project** | 6个 | 9个 | 6个 | ❌ 严重不一致 |
| **system** | 6个 | 30个 | 41个 | ❌ 严重不一致 |
| **daily** | 9个 | 0个 | 0个 | ✅ 一致 |
| **ims** | 6个 | 0个 | 0个 | ✅ 一致 |
| **notice** | 0个 | 10个 | 0个 | ✅ 一致 |
| **workflow** | 0个 | 27个 | 0个 | ✅ 一致 |

#### 3. **控制器文件不匹配**

**CRM模块示例**：
- **权限缺失**: 7个控制器有文件但无权限
  - `crm_business_product`
  - `crm_contract_product`
  - `crm_customer_sea`
  - `crm_customer_share`
  - `crm_customer_share_log`
  - `crm_follow_record`
  - `crm_statistics`
  - `crm_work_report`

- **权限冗余**: 6个权限无对应控制器文件
  - `customer_sea`
  - `follow`
  - `follow_record`
  - `index`
  - `product`
  - `work_report/index`

---

## 🎯 规范化方案

### 方案A：统一为下划线格式（推荐）

#### 优势
- ✅ **与控制器命名一致**：控制器已统一为`XxxController`格式
- ✅ **语义清晰**：`crm_customer_my`比`customer`更明确
- ✅ **扩展性好**：便于添加新的控制器权限
- ✅ **维护性强**：权限名称与文件名直接对应

#### 统一规则
```
格式: module:controller_name:action
示例: crm:crm_customer_my:index
```

#### 具体规范
1. **模块名**: 小写，如`crm`、`system`、`project`
2. **控制器名**: 下划线格式，如`crm_customer_my`
3. **操作名**: 小写，如`index`、`add`、`edit`、`delete`
4. **子目录**: 使用四段式，如`system:permission:admin:index`

#### 转换示例

| 当前格式 | 统一后格式 | 说明 |
|----------|------------|------|
| `crm:follow:index` | `crm:crm_follow_record:index` | 统一为下划线格式 |
| `notice:message:delete` | `notice:notice_message:delete` | 添加模块前缀 |
| `project:project:add` | `project:project:add` | 保持不变 |
| `system:article:add` | `system:system_article:add` | 添加模块前缀 |
| `office:console` | `office:office_console:index` | 补充action |

### 方案B：统一为简单格式

#### 优势
- ✅ **简洁明了**：权限名称较短
- ✅ **易于理解**：减少冗余信息

#### 劣势
- ❌ **语义不明确**：`message`不如`notice_message`清晰
- ❌ **冲突风险**：不同模块可能有同名控制器
- ❌ **维护困难**：权限名称与文件名不对应

### 方案C：混合格式（不推荐）

#### 劣势
- ❌ **不一致性**：维持现状的混乱
- ❌ **维护困难**：需要记住每个模块的规则
- ❌ **扩展性差**：新功能难以确定命名规则

---

## 🔧 实施方案

### 阶段1：数据备份与分析

#### 1.1 备份当前权限数据
```sql
-- 备份system_menu表
CREATE TABLE system_menu_backup_20250131 AS SELECT * FROM system_menu;

-- 备份system_role_menu表
CREATE TABLE system_role_menu_backup_20250131 AS SELECT * FROM system_role_menu;
```

#### 1.2 生成权限映射表
```sql
-- 创建权限映射表
CREATE TABLE permission_name_mapping (
    id INT AUTO_INCREMENT PRIMARY KEY,
    old_name VARCHAR(255) NOT NULL,
    new_name VARCHAR(255) NOT NULL,
    module VARCHAR(50) NOT NULL,
    controller VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    change_type ENUM('rename', 'add', 'remove') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 阶段2：权限名称规范化

#### 2.1 批量更新权限名称
```sql
-- 示例：更新CRM模块权限
UPDATE system_menu 
SET name = 'crm:crm_follow_record:index' 
WHERE name = 'crm:follow:index';

UPDATE system_menu 
SET name = 'crm:crm_customer_sea:index' 
WHERE name = 'crm:customer_sea:index';
```

#### 2.2 添加缺失权限
```sql
-- 为缺失的控制器添加权限
INSERT INTO system_menu (name, title, type, status, sort, tenant_id) VALUES
('crm:crm_business_product:index', '商机产品管理', 1, 1, 100, 1),
('crm:crm_contract_product:index', '合同产品管理', 1, 1, 100, 1),
('crm:crm_customer_share:index', '客户共享管理', 1, 1, 100, 1);
```

#### 2.3 清理冗余权限
```sql
-- 删除无对应控制器的权限
DELETE FROM system_menu WHERE name IN (
    'crm:index',
    'crm:product',
    'crm:work_report/index'
);
```

### 阶段3：权限解析逻辑更新

#### 3.1 更新PermissionService.php
```php
private function generatePermissionName(string $module, array $parts, string $controllerName, string $method): string
{
    // 统一使用下划线格式
    $snakeName = $this->camelToSnake($controllerName);
    
    // 确保控制器名包含模块前缀
    if (!str_starts_with($snakeName, $module . '_')) {
        $snakeName = $module . '_' . $snakeName;
    }
    
    // 处理子目录
    if (count($parts) > 4) {
        $subPath = strtolower($parts[3]);
        return $subPath . ':' . $snakeName . ':' . $method;
    }
    
    return $snakeName . ':' . $method;
}
```

### 阶段4：测试与验证

#### 4.1 权限验证测试
- 验证所有测试用户的权限是否正常
- 测试权限解析逻辑是否正确
- 检查前端菜单显示是否正常

#### 4.2 回滚方案
```sql
-- 如有问题，可快速回滚
DROP TABLE system_menu;
DROP TABLE system_role_menu;
RENAME TABLE system_menu_backup_20250131 TO system_menu;
RENAME TABLE system_role_menu_backup_20250131 TO system_role_menu;
```

---

## 📊 影响评估

### 正面影响
- ✅ **一致性提升**：所有权限命名统一规范
- ✅ **维护性增强**：权限与控制器文件一一对应
- ✅ **扩展性改善**：新功能权限命名有明确规则
- ✅ **可读性提高**：权限名称语义更清晰

### 风险评估
- ⚠️ **数据风险**：批量更新可能导致数据丢失
- ⚠️ **功能风险**：权限名称变更可能影响现有功能
- ⚠️ **测试风险**：需要全面测试所有权限功能

### 风险缓解
- 🛡️ **完整备份**：操作前完整备份相关表
- 🛡️ **分步实施**：按模块逐步进行，便于问题定位
- 🛡️ **充分测试**：每个阶段都进行完整测试
- 🛡️ **回滚准备**：准备快速回滚方案

---

## 🎯 推荐决策

### 建议选择：方案A（统一为下划线格式）

#### 理由
1. **与现有架构匹配**：控制器已统一命名规范
2. **最小化风险**：大部分权限已是下划线格式
3. **长期收益**：提升系统整体一致性和维护性
4. **扩展友好**：为未来功能扩展奠定基础

#### 实施建议
1. **优先级**：先处理不一致性最严重的模块（crm、project、system）
2. **测试策略**：在测试环境完整验证后再应用到生产环境
3. **监控方案**：实施后密切监控系统功能和性能
4. **文档更新**：同步更新相关技术文档

---

**请确认是否采用方案A进行权限命名规范化？确认后我将开始实施具体的代码修改。**

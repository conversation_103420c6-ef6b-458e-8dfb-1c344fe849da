<?php
/**
 * 重新整理权限菜单ID顺序
 * 按照模块和功能逻辑顺序重新分配ID
 */

require_once 'vendor/autoload.php';

echo "=== 重新整理权限菜单ID顺序 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取当前权限菜单:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type, status
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $allMenus = $stmt->fetchAll();
    
    echo "  当前权限总数: " . count($allMenus) . " 个\n\n";
    
    echo "2. 按模块和功能逻辑重新排序:\n";
    
    // 定义菜单逻辑顺序
    $menuOrder = [
        // 系统管理模块 (1-100)
        'system' => [
            'system:article:index' => ['title' => '文章列表', 'new_id' => 1],
            'system:attachment:index' => ['title' => '附件管理', 'new_id' => 2],
            'system:auth:index' => ['title' => '认证管理', 'new_id' => 3],
            'system:config:index' => ['title' => '系统配置', 'new_id' => 4],
            'system:dict:index' => ['title' => '字典管理', 'new_id' => 5],
            'system:log:index' => ['title' => '日志管理', 'new_id' => 6],
            'system:message:index' => ['title' => '消息管理', 'new_id' => 7],
            'system:permission_admin:index' => ['title' => '管理员列表', 'new_id' => 10],
            'system:permission_admin:add' => ['title' => '添加管理员', 'new_id' => 11],
            'system:permission_admin:edit' => ['title' => '编辑管理员', 'new_id' => 12],
            'system:permission_admin:delete' => ['title' => '删除管理员', 'new_id' => 13],
            'system:permission_role:index' => ['title' => '角色列表', 'new_id' => 20],
            'system:permission_role:add' => ['title' => '添加角色', 'new_id' => 21],
            'system:permission_role:edit' => ['title' => '编辑角色', 'new_id' => 22],
            'system:permission_role:delete' => ['title' => '删除角色', 'new_id' => 23],
            'system:permission_menu:index' => ['title' => '权限列表', 'new_id' => 30],
            'system:permission_menu:add' => ['title' => '添加权限', 'new_id' => 31],
            'system:permission_menu:edit' => ['title' => '编辑权限', 'new_id' => 32],
            'system:permission_menu:delete' => ['title' => '删除权限', 'new_id' => 33],
        ],
        
        // CRM模块 (101-200)
        'crm' => [
            'crm:crm_customer_my:index' => ['title' => '我的客户列表', 'new_id' => 101],
            'crm:crm_customer_my:add' => ['title' => '添加客户', 'new_id' => 102],
            'crm:crm_customer_my:edit' => ['title' => '编辑客户', 'new_id' => 103],
            'crm:crm_customer_my:delete' => ['title' => '删除客户', 'new_id' => 104],
            'crm:crm_customer_my:detail' => ['title' => '客户详情', 'new_id' => 105],
            'crm:crm_customer:index' => ['title' => '公海客户列表', 'new_id' => 110],
            'crm:crm_customer:add' => ['title' => '添加公海客户', 'new_id' => 111],
            'crm:crm_customer:edit' => ['title' => '编辑公海客户', 'new_id' => 112],
            'crm:crm_customer:delete' => ['title' => '删除公海客户', 'new_id' => 113],
            'crm:crm_lead:index' => ['title' => '线索列表', 'new_id' => 120],
            'crm:crm_lead:add' => ['title' => '添加线索', 'new_id' => 121],
            'crm:crm_lead:edit' => ['title' => '编辑线索', 'new_id' => 122],
            'crm:crm_lead:delete' => ['title' => '删除线索', 'new_id' => 123],
            'crm:crm_business:index' => ['title' => '商机列表', 'new_id' => 130],
            'crm:crm_business:add' => ['title' => '添加商机', 'new_id' => 131],
            'crm:crm_business:edit' => ['title' => '编辑商机', 'new_id' => 132],
            'crm:crm_business:delete' => ['title' => '删除商机', 'new_id' => 133],
            'crm:crm_contract:index' => ['title' => '合同列表', 'new_id' => 140],
            'crm:crm_contract:add' => ['title' => '添加合同', 'new_id' => 141],
            'crm:crm_contract:edit' => ['title' => '编辑合同', 'new_id' => 142],
            'crm:crm_contract:delete' => ['title' => '删除合同', 'new_id' => 143],
            'crm:crm_product:index' => ['title' => '产品列表', 'new_id' => 150],
            'crm:crm_product:add' => ['title' => '添加产品', 'new_id' => 151],
            'crm:crm_product:edit' => ['title' => '编辑产品', 'new_id' => 152],
            'crm:crm_product:delete' => ['title' => '删除产品', 'new_id' => 153],
        ],
        
        // 项目管理模块 (201-250)
        'project' => [
            'project:project:index' => ['title' => '项目列表', 'new_id' => 201],
            'project:project:add' => ['title' => '添加项目', 'new_id' => 202],
            'project:project:edit' => ['title' => '编辑项目', 'new_id' => 203],
            'project:project:delete' => ['title' => '删除项目', 'new_id' => 204],
            'project:project_task:index' => ['title' => '任务列表', 'new_id' => 210],
            'project:project_task:add' => ['title' => '添加任务', 'new_id' => 211],
            'project:project_task:edit' => ['title' => '编辑任务', 'new_id' => 212],
            'project:project_task:delete' => ['title' => '删除任务', 'new_id' => 213],
            'project:project_member:index' => ['title' => '成员列表', 'new_id' => 220],
            'project:project_member:add' => ['title' => '添加成员', 'new_id' => 221],
            'project:project_member:edit' => ['title' => '编辑成员', 'new_id' => 222],
            'project:project_member:delete' => ['title' => '删除成员', 'new_id' => 223],
        ],
        
        // 工作流模块 (251-300)
        'workflow' => [
            'workflow:task:index' => ['title' => '工作流任务列表', 'new_id' => 251],
            'workflow:task:add' => ['title' => '添加工作流任务', 'new_id' => 252],
            'workflow:task:edit' => ['title' => '编辑工作流任务', 'new_id' => 253],
            'workflow:task:delete' => ['title' => '删除工作流任务', 'new_id' => 254],
            'workflow:application:index' => ['title' => '申请列表', 'new_id' => 260],
            'workflow:application:add' => ['title' => '提交申请', 'new_id' => 261],
            'workflow:application:edit' => ['title' => '编辑申请', 'new_id' => 262],
            'workflow:application:delete' => ['title' => '删除申请', 'new_id' => 263],
        ],
        
        // 通知模块 (301-320)
        'notice' => [
            'notice:notice_message:index' => ['title' => '消息列表', 'new_id' => 301],
            'notice:notice_message:add' => ['title' => '发送消息', 'new_id' => 302],
            'notice:notice_message:edit' => ['title' => '编辑消息', 'new_id' => 303],
            'notice:notice_message:delete' => ['title' => '删除消息', 'new_id' => 304],
        ],
        
        // 办公模块 (321-340)
        'office' => [
            'office:office_console:index' => ['title' => '办公控制台', 'new_id' => 321],
        ],
        
        // 每日报价模块 (341-360)
        'daily' => [
            'daily:daily_price_order:index' => ['title' => '每日报价列表', 'new_id' => 341],
            'daily:daily_price_order:add' => ['title' => '添加报价', 'new_id' => 342],
            'daily:daily_price_order:edit' => ['title' => '编辑报价', 'new_id' => 343],
            'daily:daily_price_order:delete' => ['title' => '删除报价', 'new_id' => 344],
        ],
        
        // 库存管理模块 (361-380)
        'ims' => [
            'ims:ims_inventory:index' => ['title' => '库存列表', 'new_id' => 361],
            'ims:ims_inventory:add' => ['title' => '添加库存', 'new_id' => 362],
            'ims:ims_inventory:edit' => ['title' => '编辑库存', 'new_id' => 363],
            'ims:ims_inventory:delete' => ['title' => '删除库存', 'new_id' => 364],
        ],
    ];
    
    // 收集所有需要重新排序的菜单
    $reorderMappings = [];
    $foundMenus = [];
    $missingMenus = [];
    
    foreach ($menuOrder as $module => $menus) {
        foreach ($menus as $menuName => $config) {
            $found = false;
            foreach ($allMenus as $menu) {
                if ($menu['name'] === $menuName) {
                    $reorderMappings[] = [
                        'old_id' => $menu['id'],
                        'new_id' => $config['new_id'],
                        'name' => $menuName,
                        'title' => $menu['title'],
                        'expected_title' => $config['title']
                    ];
                    $foundMenus[] = $menuName;
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $missingMenus[] = [
                    'name' => $menuName,
                    'title' => $config['title'],
                    'new_id' => $config['new_id']
                ];
            }
        }
    }
    
    echo "  找到的菜单: " . count($foundMenus) . " 个\n";
    echo "  缺失的菜单: " . count($missingMenus) . " 个\n";
    echo "  需要重新排序: " . count($reorderMappings) . " 个\n\n";
    
    if (!empty($missingMenus)) {
        echo "3. 缺失的菜单:\n";
        foreach ($missingMenus as $missing) {
            echo "    ID:{$missing['new_id']} {$missing['name']} ({$missing['title']})\n";
        }
        echo "\n";
    }
    
    echo "4. 开始重新排序菜单ID:\n";
    
    if (empty($reorderMappings)) {
        echo "  ✅ 所有菜单ID已经是有序的\n";
        return;
    }
    
    $pdo->beginTransaction();
    
    try {
        // 第一步：将所有ID临时设置为负数，避免主键冲突
        foreach ($reorderMappings as $mapping) {
            $stmt = $pdo->prepare("UPDATE system_menu SET id = ? WHERE id = ?");
            $stmt->execute([-$mapping['old_id'], $mapping['old_id']]);
        }
        
        // 第二步：设置为新的ID
        $successCount = 0;
        foreach ($reorderMappings as $mapping) {
            $stmt = $pdo->prepare("UPDATE system_menu SET id = ? WHERE id = ?");
            $stmt->execute([$mapping['new_id'], -$mapping['old_id']]);
            
            echo "    ✅ ID:{$mapping['old_id']} -> {$mapping['new_id']} {$mapping['name']}\n";
            $successCount++;
        }
        
        // 第三步：更新相关联表的外键
        echo "\n  更新关联表外键:\n";
        
        // 更新角色权限关联表
        foreach ($reorderMappings as $mapping) {
            $stmt = $pdo->prepare("UPDATE system_role_menu SET menu_id = ? WHERE menu_id = ?");
            $stmt->execute([$mapping['new_id'], -$mapping['old_id']]);
        }
        echo "    ✅ 更新 system_role_menu 表\n";
        
        // 重置自增ID
        $maxId = max(array_column($reorderMappings, 'new_id'));
        $nextAutoIncrement = $maxId + 1;
        $stmt = $pdo->prepare("ALTER TABLE system_menu AUTO_INCREMENT = ?");
        $stmt->execute([$nextAutoIncrement]);
        echo "    ✅ 重置自增ID为: {$nextAutoIncrement}\n";
        
        $pdo->commit();
        
        echo "\n  ✅ 成功重新排序 {$successCount} 个菜单ID\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
    echo "\n5. 验证重新排序结果:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $reorderedMenus = $stmt->fetchAll();
    
    echo "  重新排序后的菜单 (前20个):\n";
    for ($i = 0; $i < min(20, count($reorderedMenus)); $i++) {
        $menu = $reorderedMenus[$i];
        echo "    ID:{$menu['id']} {$menu['name']} ({$menu['title']})\n";
    }
    
    if (count($reorderedMenus) > 20) {
        echo "    ... 还有 " . (count($reorderedMenus) - 20) . " 个菜单\n";
    }
    
    echo "\n🎉 菜单ID重新排序完成！\n";
    echo "  - 系统管理: ID 1-100\n";
    echo "  - CRM模块: ID 101-200\n";
    echo "  - 项目管理: ID 201-250\n";
    echo "  - 工作流: ID 251-300\n";
    echo "  - 通知: ID 301-320\n";
    echo "  - 办公: ID 321-340\n";
    echo "  - 每日报价: ID 341-360\n";
    echo "  - 库存管理: ID 361-380\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 菜单重新排序完成 ===\n";

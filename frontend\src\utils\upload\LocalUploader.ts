import { BaseUploader } from './BaseUploader'
import { UploadResult } from './IUploader'
import axios, { AxiosRequestConfig } from 'axios'
// import { useUserStore } from '@/store/modules/user'
import { UploadApi } from '@/api/uploadApi'

/**
 * 本地上传器
 */
export class LocalUploader extends BaseUploader {
  // 当前上传的取消令牌
  private cancelTokenSource: any = null

  /**
   * 构造函数
   */
  constructor() {
    super('local')
  }

  /**
   * 上传文件
   * @param file 文件对象
   * @param mediaType 媒体类型
   * @param params 额外参数
   */
  public async upload(
    file: File,
    mediaType: string,
    params?: Record<string, any>
  ): Promise<UploadResult> {
    // 验证文件
    const validateResult = this.validateFile(file, mediaType)
    if (!validateResult.valid) {
      return {
        success: false,
        message: validateResult.message
      }
    }

    if (!this.config) {
      return {
        success: false,
        message: '上传配置未初始化'
      }
    }

    try {
      // 创建FormData
      const formData = new FormData()
      formData.append('file', file)

      // 添加额外参数
      if (params) {
        Object.keys(params).forEach((key) => {
          formData.append(key, params[key])
        })
      }

      // 添加媒体类型
      formData.append('media_type', mediaType)

      // 创建取消令牌
      this.cancelTokenSource = axios.CancelToken.source()

      // 配置请求
      const config: AxiosRequestConfig = {
        cancelToken: this.cancelTokenSource.token,
        timeout: 60000 // 60秒超时
      }

      // 使用UploadApi上传文件
      const response = await UploadApi.upload(formData, config)

      // 清空取消令牌
      this.cancelTokenSource = null

      // 处理响应（适配新的状态码）
      if (response && response.code === 200) {
        return {
          success: true,
          data: response.data
        }
      } else {
        return {
          success: false,
          message: response?.message || '上传失败'
        }
      }
    } catch (error: any) {
      // 如果是取消请求导致的错误，返回取消信息
      if (axios.isCancel(error)) {
        return {
          success: false,
          message: '上传已取消'
        }
      }

      // 其他错误
      return {
        success: false,
        message: error.message || '上传失败'
      }
    }
  }

  /**
   * 取消上传
   */
  public cancel(): void {
    if (this.cancelTokenSource) {
      this.cancelTokenSource.cancel('用户取消上传')
      this.cancelTokenSource = null
    }
  }

  /**
   * 销毁上传器
   */
  public destroy(): void {
    this.cancel()
    super.destroy()
  }
}

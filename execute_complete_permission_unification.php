<?php
/**
 * 执行完善权限统一SQL脚本
 */

require_once 'vendor/autoload.php';

echo "=== 执行完善权限统一 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 执行前状态:\n";
    
    // 检查当前格式分布
    $stmt = $pdo->prepare("
        SELECT 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END as format_type,
            COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END
    ");
    $stmt->execute();
    $beforeFormats = $stmt->fetchAll();
    
    $beforeTotal = 0;
    $beforeThreeParts = 0;
    
    echo "  执行前格式分布:\n";
    foreach ($beforeFormats as $format) {
        echo "    {$format['format_type']}: {$format['count']} 个\n";
        $beforeTotal += $format['count'];
        if ($format['format_type'] === '三段式') {
            $beforeThreeParts = $format['count'];
        }
    }
    
    $beforeRate = round($beforeThreeParts / $beforeTotal * 100, 1);
    echo "  执行前统一率: {$beforeRate}% ({$beforeThreeParts}/{$beforeTotal})\n";
    
    echo "\n2. 执行SQL脚本:\n";
    
    $sqlScript = file_get_contents('complete_permission_unification.sql');
    if (!$sqlScript) {
        throw new Exception("无法读取SQL脚本文件");
    }
    
    // 分割SQL语句
    $statements = explode(';', $sqlScript);
    $executedCount = 0;
    $errorCount = 0;
    
    $pdo->beginTransaction();
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // 跳过注释和空语句
        if (empty($statement) || strpos($statement, '--') === 0 || 
            strpos($statement, 'START TRANSACTION') !== false ||
            strpos($statement, 'COMMIT') !== false ||
            strpos($statement, 'ROLLBACK') !== false) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executedCount++;
            
            if ($executedCount % 10 == 0) {
                echo "    已执行 {$executedCount} 条语句...\n";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "    ❌ 执行失败: " . substr($statement, 0, 50) . "...\n";
            echo "       错误: " . $e->getMessage() . "\n";
        }
    }
    
    if ($errorCount > 0) {
        echo "\n  ⚠️ 发现 {$errorCount} 个错误，回滚事务\n";
        $pdo->rollBack();
        throw new Exception("SQL执行过程中发现错误，已回滚");
    } else {
        $pdo->commit();
        echo "\n  ✅ 所有SQL语句执行成功，已提交事务\n";
        echo "  总执行语句数: {$executedCount} 条\n";
    }
    
    echo "\n3. 执行后验证:\n";
    
    // 检查执行后格式分布
    $stmt = $pdo->prepare("
        SELECT 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END as format_type,
            COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END
    ");
    $stmt->execute();
    $afterFormats = $stmt->fetchAll();
    
    $afterTotal = 0;
    $afterThreeParts = 0;
    
    echo "  执行后格式分布:\n";
    foreach ($afterFormats as $format) {
        echo "    {$format['format_type']}: {$format['count']} 个\n";
        $afterTotal += $format['count'];
        if ($format['format_type'] === '三段式') {
            $afterThreeParts = $format['count'];
        }
    }
    
    $afterRate = round($afterThreeParts / $afterTotal * 100, 1);
    echo "  执行后统一率: {$afterRate}% ({$afterThreeParts}/{$afterTotal})\n";
    
    $improvement = $afterRate - $beforeRate;
    echo "  统一率提升: +{$improvement}%\n";
    
    if ($afterRate >= 95) {
        echo "  ✅ 权限格式统一完成！\n";
    } else {
        echo "  ⚠️ 权限格式统一基本完成，还有少量权限未统一\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 权限统一完成 ===\n";

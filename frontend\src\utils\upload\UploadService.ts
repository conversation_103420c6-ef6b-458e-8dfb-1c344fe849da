import { UploadConfig, UploadToken, UploadResult, UploadOptions } from './IUploader'
import { UploaderFactory } from './UploaderFactory'
import { ElMessage } from 'element-plus'
import { UploadApi } from '@/api/uploadApi'
import { useUploadStore } from '@/store/modules/upload'
import { useUserStore } from '@/store/modules/user'
import { ApiStatus } from '@/utils/http/status'
import { FileTypeUtil } from './FileTypeUtil'
import { computed } from 'vue'
// import { FileTypeUtil } from './FileTypeUtil'

/**
 * API响应接口
 */
/*interface ApiResponse<T = any> {
  code: number
  data?: T
  message?: string
}*/

/**
 * 上传服务类
 */
export class UploadService {
  /**
   * 配置是否已初始化
   */
  private static isInitialized = false

  /**
   * 页面加载时初始化上传配置
   * 每次刷新页面时调用
   */
  public static async initializeOnPageLoad(): Promise<void> {
    try {
      // 重置初始化状态
      this.isInitialized = false

      const isLogin = computed(() => useUserStore().isLogin)

      // 检查用户是否已登录
      if (isLogin.value && !this.isInitialized) {
        // 从store获取配置
        const uploadStore = useUploadStore()
        await uploadStore.fetchConfig()
        this.isInitialized = true
      } else {
        console.log('用户未登录，无法获取上传配置')
        // 用户未登录，清空上传配置
        const uploadStore = useUploadStore()
        uploadStore.resetConfig()
      }
    } catch (error) {
      console.error('初始化上传配置失败', error)
      // 出现错误时，清空配置
      const uploadStore = useUploadStore()
      uploadStore.resetConfig()
    }
  }

  /**
   * 获取上传配置
   * @param forceRefresh 是否强制刷新配置
   * @returns 上传配置
   */
  public static async getUploadConfig(forceRefresh = false): Promise<UploadConfig> {
    try {
      // 获取上传Store
      const uploadStore = useUploadStore()

      const isLogin = computed(() => useUserStore().isLogin)
      // 检查用户是否已登录
      if (!isLogin.value) {
        throw new Error('用户未登录，无法获取上传配置')
      }

      // 如果强制刷新或Store中没有配置，则从后端获取
      if (forceRefresh || !uploadStore.uploadConfig) {
        await uploadStore.fetchConfig()

        if (!uploadStore.uploadConfig) {
          throw new Error('获取上传配置失败')
        }

        return uploadStore.uploadConfig
      }

      // 如果Store中有配置，直接返回
      if (uploadStore.uploadConfig) {
        return uploadStore.uploadConfig
      }

      throw new Error('上传配置不存在')
    } catch (error: any) {
      console.error('获取上传配置失败', error)
      throw error
    }
  }

  /**
   * 获取上传Token
   * @param storage 存储类型
   * @param params 额外参数，用于传递给后端
   * @returns 上传Token
   */
  public static async getUploadToken(
    storage: string,
    params?: Record<string, any>
  ): Promise<UploadToken> {
    try {
      // 将前端存储类型映射回后端存储类型
      let backendStorage = storage
      switch (storage) {
        case 'qiniu':
          backendStorage = 'qnoss'
          break
        case 'tencent':
          backendStorage = 'txoss'
          break
        case 'aliyun':
          backendStorage = 'alioss'
          break
        default:
          // 其他情况保持不变
          break
      }

      // 获取上传Token
      const response = await UploadApi.getToken(backendStorage, params)

      // 处理响应
      if (response.code === ApiStatus.success) {
        // 添加日志输出，便于调试
        console.log('获取到上传Token:', response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取上传Token失败')
      }
    } catch (error: any) {
      console.error('获取上传Token失败', error)
      throw error
    }
  }

  /**
   * 检查文件是否符合要求
   * @param file 文件对象
   * @param fileType 文件类型
   * @returns 检查结果
   */
  public static async checkFile(file: File, fileType: string): Promise<UploadResult> {
    try {
      // 获取上传配置
      const config = await this.getUploadConfig()

      // 验证文件大小
      const fileSizeInMB = file.size / (1024 * 1024)
      if (
        typeof config.upload_allow_size !== 'undefined' &&
        fileSizeInMB > config.upload_allow_size
      ) {
        return {
          success: false,
          message: `文件大小不能超过${config.upload_allow_size}MB`
        }
      }

      // 获取文件扩展名
      const extension = FileTypeUtil.getExtension(file.name)

      // 验证文件扩展名
      if (
        !config.upload_allow_ext_array ||
        !Array.isArray(config.upload_allow_ext_array) ||
        config.upload_allow_ext_array.length === 0
      ) {
        return {
          success: false,
          message: '上传配置错误：未定义允许的文件扩展名'
        }
      }

      if (!config.upload_allow_ext_array.includes(extension)) {
        return {
          success: false,
          message: `不支持的文件类型：${extension}，允许的类型：${config.upload_allow_ext}`
        }
      }

      // 根据文件类型进行额外验证
      if (fileType === 'image' && !file.type.startsWith('image/')) {
        return {
          success: false,
          message: '请上传图片类型文件'
        }
      } else if (fileType === 'video' && !file.type.startsWith('video/')) {
        return {
          success: false,
          message: '请上传视频类型文件'
        }
      } else if (fileType === 'audio' && !file.type.startsWith('audio/')) {
        return {
          success: false,
          message: '请上传音频类型文件'
        }
      }

      return { success: true }
    } catch (error: any) {
      console.error('检查文件失败', error)
      return {
        success: false,
        message: error.message || '检查文件失败'
      }
    }
  }

  /**
   * 上传文件
   * @param file 文件对象
   * @param fileType 文件类型
   * @param options 上传选项
   * @param progressCallback 进度回调
   * @returns 上传结果
   */
  public static async uploadFile(
    file: File,
    fileType: string,
    options?: UploadOptions,
    progressCallback?: (event: ProgressEvent) => void
  ): Promise<UploadResult> {
    try {
      // 获取上传配置
      const config = await this.getUploadConfig()

      // 检查文件类型
      const result = await this.checkFile(file, fileType)
      if (!result.success) {
        return result
      }

      // 获取存储类型
      const storageType = config.storage_type || 'local'
      console.log('当前使用的存储类型:', storageType, '原始类型:', config.upload_type)

      // 如果是本地上传，使用传统方式（通过后端上传）
      if (storageType === 'local') {
        console.log('使用本地上传')
        return this.uploadToLocalServer(file, fileType, options, progressCallback)
      }

      // 云存储使用直传方式（不经过后端，直接上传到云存储）
      console.log('使用云存储直传:', storageType)
      return this.directUploadToCloud(file, fileType, storageType, options, progressCallback)
    } catch (error: any) {
      console.error('上传文件失败', error)
      ElMessage.error(error.message || '上传文件失败')
      return {
        success: false,
        message: error.message || '上传文件失败'
      }
    }
  }

  /**
   * 上传到本地服务器（使用新的AttachmentController）
   * @param file 文件对象
   * @param fileType 文件类型
   * @param options 上传选项
   * @param progressCallback 进度回调
   * @returns 上传结果
   */
  private static async uploadToLocalServer(
    file: File,
    fileType: string,
    options?: UploadOptions | Record<string, any>,
    progressCallback?: (event: ProgressEvent) => void
  ): Promise<UploadResult> {
    try {
      // 创建FormData
      const formData = new FormData()
      formData.append('file', file)

      // 添加额外参数
      if (options) {
        if (typeof options === 'object') {
          Object.keys(options).forEach(key => {
            if (options[key] !== undefined && options[key] !== null) {
              formData.append(key, String(options[key]))
            }
          })
        }
      }

      // 添加存储类型
      formData.append('storage', 'local')

      // 使用新的UploadApi上传
      const response = await UploadApi.upload(formData, {
        onUploadProgress: progressCallback
      })

      // 适配新的响应格式
      if (response.code === ApiStatus.success) {
        return {
          success: true,
          data: {
            ...response.data,
            // 确保兼容性
            url: response.data.url || response.data.path,
            name: response.data.display_name || response.data.name,
            real_name: response.data.original_name || response.data.display_name,
            path: response.data.path,
            size: response.data.size,
            mime_type: response.data.mime_type,
            is_duplicate: response.data.is_duplicate || false
          },
          message: '上传成功'
        }
      } else {
        return {
          success: false,
          message: response.message || '上传失败'
        }
      }
    } catch (error: any) {
      console.error('上传到本地服务器失败', error)
      return {
        success: false,
        message: error.message || '上传到本地服务器失败'
      }
    }
  }

  /**
   * 直接上传到云存储
   * @param file 文件对象
   * @param fileType 文件类型
   * @param storageType 存储类型
   * @param options 上传选项
   * @param progressCallback 进度回调
   * @returns 上传结果
   */
  private static async directUploadToCloud(
    file: File,
    fileType: string,
    storageType: string,
    options?: UploadOptions | Record<string, any>,
    progressCallback?: (event: ProgressEvent) => void
  ): Promise<UploadResult> {
    try {
      // 获取上传配置
      const config = await this.getUploadConfig()

      // 初始化上传器
      UploaderFactory.initUploader(storageType, config)
      console.log('已初始化上传器:', storageType)

      // 准备获取Token的参数
      const tokenParams = {
        filename: file.name,
        size: file.size,
        mime_type: file.type,
        media_type: fileType,
        ...(typeof options === 'object' ? options : {})
      }

      // 获取上传Token，传递额外参数给后端
      const token = await this.getUploadToken(storageType, tokenParams)
      console.log('获取到上传Token:', token)

      // 获取上传器
      const uploader = UploaderFactory.getUploader(storageType)

      // 设置上传Token
      uploader.setToken(token)

      // 上传文件
      return await uploader.upload(file, options || fileType, progressCallback)
    } catch (error: any) {
      console.error('直接上传到云存储失败', error)
      return {
        success: false,
        message: error.message || '直接上传到云存储失败'
      }
    }
  }

  /**
   * 取消上传
   * @param storage 存储类型
   */
  public static async cancelUpload(storage?: string): Promise<void> {
    try {
      if (storage) {
        // 取消指定存储类型的上传
        const uploader = UploaderFactory.getUploader(storage)
        uploader.cancel()
      } else {
        const config = await this.getUploadConfig()
        // 取消当前存储类型的上传
        const uploader = UploaderFactory.getUploader(config.storage_type || 'local')
        uploader.cancel()
      }
    } catch (error) {
      console.error('取消上传失败', error)
    }
  }

  /**
   * 重置上传服务
   */
  public static reset(): void {
    const uploadStore = useUploadStore()
    uploadStore.resetConfig()
    UploaderFactory.destroyAll()
    this.isInitialized = false
  }

  /**
   * 初始化上传配置
   * @param config 上传配置
   */
  public static initConfig(config: UploadConfig): void {
    const uploadStore = useUploadStore()
    uploadStore.setConfig(config)
    this.isInitialized = true
  }
}

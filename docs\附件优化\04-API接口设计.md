# API接口设计文档

**版本**: v1.0  
**更新日期**: 2025-01-31  
**文档状态**: 草稿

---

## 🎯 接口设计原则

### 设计理念
1. **向后兼容**：保持现有API接口不变，确保前端无感知升级
2. **RESTful规范**：遵循REST API设计规范
3. **统一响应格式**：使用统一的响应数据结构
4. **权限控制**：所有接口都进行严格的权限验证

### 响应格式规范
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据
    },
    "timestamp": 1706745600
}
```

---

## 📁 文件上传接口

### 1. 获取上传配置
**接口地址**：`GET /api/upload/config`  
**接口说明**：获取文件上传配置信息

#### 请求参数
无

#### 响应示例
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "upload_allow_type": "local",
        "upload_allow_ext": ".jpg,.png,.jpeg,.gif,.pdf,.doc,.docx",
        "upload_allow_mime": ["image/jpeg", "image/png", "application/pdf"],
        "upload_allow_size": 10,
        "max_file_count": 5,
        "domain": "https://your-domain.com",
        "storage_type": "local"
    }
}
```

### 2. 获取上传Token（云存储）
**接口地址**：`GET /api/upload/token`  
**接口说明**：获取云存储上传Token

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| storage | string | 是 | 存储类型：qnoss/alioss/txoss |
| filename | string | 是 | 文件名 |
| size | integer | 是 | 文件大小 |
| mime_type | string | 是 | 文件MIME类型 |
| cate_id | integer | 否 | 分类ID，默认0 |

#### 响应示例
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "token": "upload_token_string",
        "key": "upload_key",
        "domain": "https://cdn.example.com",
        "storage": "qnoss",
        "deadline": 3600,
        "pre_upload_id": "pre_upload_12345"
    }
}
```

### 3. 文件上传（本地存储）
**接口地址**：`POST /api/upload`  
**接口说明**：上传文件到本地存储

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | file | 是 | 上传的文件 |
| storage | string | 否 | 存储类型，默认local |
| cate_id | integer | 否 | 分类ID，默认0 |

#### 响应示例
```json
{
    "code": 200,
    "message": "上传成功",
    "data": {
        "id": 123,
        "attachment_id": 456,
        "name": "generated_filename.jpg",
        "display_name": "用户上传的文件名.jpg",
        "path": "/uploads/2025/01/31/generated_filename.jpg",
        "url": "https://your-domain.com/uploads/2025/01/31/generated_filename.jpg",
        "size": 1024000,
        "extension": "jpg",
        "mime_type": "image/jpeg",
        "is_duplicate": false,
        "upload_time": "2025-01-31 10:30:00"
    }
}
```

### 4. 云存储上传回调
**接口地址**：`POST /api/upload/callback/{storage}`  
**接口说明**：处理云存储上传回调

#### 请求参数
根据不同云存储平台的回调格式

#### 响应示例
```json
{
    "code": 200,
    "message": "回调处理成功",
    "data": {
        "id": 123,
        "attachment_id": 456,
        "name": "generated_filename.jpg",
        "display_name": "用户上传的文件名.jpg",
        "path": "tenant/0/20250131/etag123.jpg",
        "url": "https://cdn.example.com/tenant/0/20250131/etag123.jpg",
        "size": 1024000,
        "extension": "jpg",
        "mime_type": "image/jpeg",
        "is_duplicate": true,
        "upload_time": "2025-01-31 10:30:00"
    }
}
```

---

## 📋 文件管理接口

### 1. 获取文件列表
**接口地址**：`GET /api/attachment`  
**接口说明**：获取用户的文件列表

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认10 |
| cate_id | integer | 否 | 分类ID，-1表示全部 |
| name | string | 否 | 文件名搜索 |
| storage | string | 否 | 存储类型筛选 |
| extension | string | 否 | 文件扩展名筛选 |

#### 响应示例
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "list": [
            {
                "id": 123,
                "cate_id": 0,
                "display_name": "用户文件名.jpg",
                "original_name": "original_filename.jpg",
                "size": 1024000,
                "extension": "jpg",
                "mime_type": "image/jpeg",
                "upload_time": "2025-01-31 10:30:00",
                "upload_source": "web",
                "path": "/uploads/2025/01/31/generated_filename.jpg",
                "url": "https://your-domain.com/uploads/2025/01/31/generated_filename.jpg"
            }
        ],
        "total": 100,
        "page": 1,
        "limit": 10
    }
}
```

### 2. 获取文件详情
**接口地址**：`GET /api/attachment/{id}`  
**接口说明**：获取指定文件的详细信息

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 用户文件关联ID |

#### 响应示例
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 123,
        "attachment_id": 456,
        "cate_id": 0,
        "display_name": "用户文件名.jpg",
        "original_name": "original_filename.jpg",
        "size": 1024000,
        "extension": "jpg",
        "mime_type": "image/jpeg",
        "storage": "local",
        "upload_time": "2025-01-31 10:30:00",
        "upload_source": "web",
        "upload_ip": "*************",
        "path": "/uploads/2025/01/31/generated_filename.jpg",
        "url": "https://your-domain.com/uploads/2025/01/31/generated_filename.jpg",
        "ref_count": 3,
        "is_shared": false
    }
}
```

### 3. 移动文件分类
**接口地址**：`PUT /api/attachment/{id}/move`  
**接口说明**：移动文件到指定分类

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| cate_id | integer | 是 | 目标分类ID |

#### 响应示例
```json
{
    "code": 200,
    "message": "移动成功",
    "data": null
}
```

### 4. 重命名文件
**接口地址**：`PUT /api/attachment/{id}/rename`  
**接口说明**：重命名文件显示名称

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| display_name | string | 是 | 新的显示名称 |

#### 响应示例
```json
{
    "code": 200,
    "message": "重命名成功",
    "data": null
}
```

### 5. 删除文件
**接口地址**：`DELETE /api/attachment/{id}`  
**接口说明**：删除指定文件

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 用户文件关联ID |

#### 响应示例
```json
{
    "code": 200,
    "message": "删除成功",
    "data": null
}
```

### 6. 批量删除文件
**接口地址**：`DELETE /api/attachment/batch`  
**接口说明**：批量删除文件

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 用户文件关联ID数组 |

#### 请求示例
```json
{
    "ids": [123, 124, 125]
}
```

#### 响应示例
```json
{
    "code": 200,
    "message": "批量删除成功",
    "data": {
        "success_count": 3,
        "failed_count": 0,
        "failed_ids": []
    }
}
```

---

## 📊 统计接口

### 1. 获取用户文件统计
**接口地址**：`GET /api/attachment/stats`  
**接口说明**：获取用户文件使用统计

#### 请求参数
无

#### 响应示例
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "total_files": 150,
        "total_size": 1073741824,
        "total_size_formatted": "1.00 GB",
        "by_category": [
            {
                "cate_id": 0,
                "cate_name": "未分类",
                "file_count": 50,
                "total_size": 536870912
            }
        ],
        "by_extension": [
            {
                "extension": "jpg",
                "file_count": 80,
                "total_size": 429496729
            }
        ],
        "recent_uploads": 10
    }
}
```

### 2. 获取系统去重统计（管理员）
**接口地址**：`GET /api/attachment/dedup-stats`  
**接口说明**：获取系统文件去重统计（仅管理员）

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| start_date | string | 否 | 开始日期 |
| end_date | string | 否 | 结束日期 |

#### 响应示例
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "total_physical_files": 1000,
        "total_user_references": 2500,
        "dedup_ratio": 60.0,
        "space_saved": 5368709120,
        "space_saved_formatted": "5.00 GB",
        "by_storage": [
            {
                "storage": "local",
                "physical_files": 500,
                "user_references": 1200,
                "dedup_ratio": 58.3
            }
        ]
    }
}
```

---

## 🔒 权限验证

### 接口权限
所有接口都需要进行以下权限验证：

1. **身份认证**：验证用户登录状态
2. **租户隔离**：确保用户只能访问自己租户的数据
3. **数据权限**：确保用户只能操作自己的文件
4. **功能权限**：验证用户是否有对应的功能权限

### 权限错误响应
```json
{
    "code": 403,
    "message": "无权限访问",
    "data": null
}
```

---

## 🔄 兼容性说明

### 现有接口保持不变
为确保前端无感知升级，以下现有接口保持完全兼容：

1. `POST /api/upload` - 文件上传接口
2. `GET /api/attachment` - 文件列表接口
3. `DELETE /api/attachment/{id}` - 文件删除接口
4. `PUT /api/attachment/{id}/move` - 文件移动接口

### 响应数据兼容
- 保持现有响应字段不变
- 新增字段不影响现有逻辑
- 确保前端组件正常工作

### 渐进式升级
- 后端先实现新架构
- 前端逐步适配新功能
- 保证系统稳定运行

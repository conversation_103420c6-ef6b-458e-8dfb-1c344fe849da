# 附件系统技术方案设计

**版本**: v1.0  
**更新日期**: 2025-01-31  
**文档状态**: 草稿

---

## 🏗️ 整体架构设计

### 核心设计理念

#### 职责分离原则
- **物理文件层**：`system_attachment` - 纯文件存储管理
- **权限访问层**：`system_attachment_user` - 用户文件关联和权限控制

#### 设计优势
1. **存储效率最优**：相同文件只存储一份
2. **权限控制精细**：用户级别的文件隔离
3. **架构清晰**：职责分离，易于维护和扩展
4. **云存储友好**：充分利用云存储去重能力

### 架构图

```mermaid
graph TB
    subgraph "用户层"
        U1[用户A] 
        U2[用户B]
        U3[用户C]
    end
    
    subgraph "应用层"
        API[附件API]
        Service[附件服务]
        Upload[上传服务]
    end
    
    subgraph "数据层"
        AU[system_attachment_user<br/>用户文件关联表]
        A[system_attachment<br/>物理文件表]
    end
    
    subgraph "存储层"
        Local[本地存储]
        OSS[阿里云OSS]
        COS[腾讯云COS]
        Qiniu[七牛云]
    end
    
    U1 --> API
    U2 --> API
    U3 --> API
    
    API --> Service
    Service --> Upload
    Service --> AU
    AU --> A
    
    Upload --> Local
    Upload --> OSS
    Upload --> COS
    Upload --> Qiniu
```

---

## 📊 数据模型设计

### 核心表结构

#### 1. 物理文件表 (system_attachment)
```sql
CREATE TABLE `system_attachment` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `name` varchar(100) NOT NULL COMMENT '系统生成的文件名',
    `path` varchar(255) NOT NULL COMMENT '文件路径',
    `extension` varchar(10) DEFAULT NULL COMMENT '文件扩展名',
    `size` bigint(20) NOT NULL DEFAULT 0 COMMENT '文件大小(字节)',
    `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
    `storage` varchar(20) NOT NULL DEFAULT 'local' COMMENT '存储方式',
    `storage_id` varchar(100) DEFAULT NULL COMMENT '存储平台ID',
    `file_md5` varchar(32) NOT NULL COMMENT '文件MD5值',
    `file_hash` varchar(64) DEFAULT NULL COMMENT '文件SHA256值',
    `ref_count` int(11) NOT NULL DEFAULT 0 COMMENT '引用计数',
    `storage_meta` json DEFAULT NULL COMMENT '存储平台元数据',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_storage_identifier` (`storage`, `file_md5`, `storage_id`),
    KEY `idx_file_md5` (`file_md5`),
    KEY `idx_storage_id` (`storage_id`)
) ENGINE=InnoDB COMMENT='附件文件表';
```

#### 2. 用户文件关联表 (system_attachment_user)
```sql
CREATE TABLE `system_attachment_user` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `attachment_id` bigint(20) UNSIGNED NOT NULL COMMENT '附件ID',
    `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户分类ID',
    `display_name` varchar(255) NOT NULL COMMENT '用户自定义显示名称',
    `original_name` varchar(255) NOT NULL COMMENT '原始上传文件名',
    `upload_ip` varchar(45) DEFAULT NULL COMMENT '上传IP',
    `upload_source` varchar(50) DEFAULT 'web' COMMENT '上传来源',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_attachment_user_tenant` (`attachment_id`, `user_id`, `tenant_id`, `deleted_at`),
    KEY `idx_user_tenant` (`user_id`, `tenant_id`),
    KEY `idx_attachment_id` (`attachment_id`),
    KEY `idx_cate_user` (`cate_id`, `user_id`, `tenant_id`)
) ENGINE=InnoDB COMMENT='用户文件关联表';
```

### 数据关系说明

#### 关联关系
- `system_attachment_user.attachment_id` → `system_attachment.id`
- 一个物理文件可以被多个用户关联
- 一个用户可以关联多个物理文件

#### 约束规则
- 同一用户不能重复关联同一个附件
- 删除用户关联时，检查引用计数决定是否删除物理文件
- 支持软删除，保证数据可恢复

---

## 🔄 业务流程设计

### 文件上传流程

#### 本地存储上传
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant S as 服务层
    participant D as 数据库
    participant L as 本地存储
    
    U->>F: 选择文件上传
    F->>A: POST /upload (文件)
    A->>S: uploadFile()
    S->>S: 计算文件MD5
    S->>D: 查询是否存在相同文件
    
    alt 文件已存在
        S->>D: 创建用户关联记录
        S->>D: 增加引用计数
    else 文件不存在
        S->>L: 上传物理文件
        S->>D: 创建物理文件记录
        S->>D: 创建用户关联记录
    end
    
    S->>A: 返回结果
    A->>F: 返回文件信息
    F->>U: 显示上传成功
```

#### 云存储上传
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant S as 服务层
    participant C as 云存储
    participant D as 数据库
    
    U->>F: 选择文件上传
    F->>A: GET /upload/token
    A->>S: getUploadToken()
    S->>A: 返回上传Token
    A->>F: 返回Token和配置
    F->>C: 直接上传到云存储
    C->>A: 上传回调
    A->>S: handleCallback()
    S->>D: 查询是否存在相同文件
    
    alt 文件已存在
        S->>D: 创建用户关联记录
        S->>D: 增加引用计数
    else 文件不存在
        S->>D: 创建物理文件记录
        S->>D: 创建用户关联记录
    end
    
    S->>A: 返回结果
    A->>F: 返回文件信息
    F->>U: 显示上传成功
```

### 文件删除流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant S as 服务层
    participant D as 数据库
    participant ST as 存储
    
    U->>F: 删除文件
    F->>A: DELETE /attachment/{id}
    A->>S: deleteUserFile()
    S->>D: 查询用户文件关联
    S->>D: 删除用户关联记录
    S->>D: 减少引用计数
    S->>D: 检查引用计数
    
    alt 引用计数为0
        S->>ST: 删除物理文件
        S->>D: 删除物理文件记录
    end
    
    S->>A: 返回结果
    A->>F: 返回删除成功
    F->>U: 显示删除成功
```

---

## 🔧 核心算法设计

### 文件去重算法

#### 多策略去重
```php
/**
 * 多策略文件去重查找
 */
private function findExistingAttachment(string $storage, array $fileInfo): ?AttachmentModel
{
    // 策略1：通过storage_id查找（云存储etag，最准确）
    if (!empty($fileInfo['storage_id'])) {
        $attachment = AttachmentModel::where('storage', $storage)
            ->where('storage_id', $fileInfo['storage_id'])
            ->where('deleted_at', null)
            ->find();
        if ($attachment) return $attachment;
    }
    
    // 策略2：通过MD5查找（本地存储或有MD5的情况）
    if (!empty($fileInfo['file_md5'])) {
        $attachment = AttachmentModel::where('storage', $storage)
            ->where('file_md5', $fileInfo['file_md5'])
            ->where('deleted_at', null)
            ->find();
        if ($attachment) return $attachment;
    }
    
    // 策略3：通过文件大小+路径查找（兜底策略）
    if (!empty($fileInfo['size']) && !empty($fileInfo['path'])) {
        return AttachmentModel::where('storage', $storage)
            ->where('size', $fileInfo['size'])
            ->where('path', $fileInfo['path'])
            ->where('deleted_at', null)
            ->find();
    }
    
    return null;
}
```

### 并发控制算法

#### 分布式锁
```php
/**
 * 使用分布式锁处理并发上传
 */
public function uploadWithLock(array $fileInfo, string $storage): array
{
    $lockKey = "upload_lock:{$storage}:" . ($fileInfo['file_md5'] ?? $fileInfo['storage_id']);
    
    return Redis::lock($lockKey, 10)->block(5, function() use ($fileInfo, $storage) {
        return $this->doUploadWithDeduplication($fileInfo, $storage);
    });
}
```

### 缓存策略

#### 多级缓存
```php
/**
 * 文件信息缓存策略
 */
class AttachmentCache
{
    // L1缓存：内存缓存（进程级别）
    private static array $memoryCache = [];
    
    // L2缓存：Redis缓存（应用级别）
    public static function getAttachmentInfo(int $attachmentId): ?array
    {
        // 先查内存缓存
        if (isset(self::$memoryCache[$attachmentId])) {
            return self::$memoryCache[$attachmentId];
        }
        
        // 再查Redis缓存
        $key = "attachment_info:{$attachmentId}";
        $info = Cache::get($key);
        
        if ($info) {
            self::$memoryCache[$attachmentId] = $info;
            return $info;
        }
        
        // 最后查数据库
        $attachment = AttachmentModel::find($attachmentId);
        if ($attachment) {
            $info = $attachment->toArray();
            Cache::set($key, $info, 3600);
            self::$memoryCache[$attachmentId] = $info;
            return $info;
        }
        
        return null;
    }
}
```

---

## 🚀 性能优化策略

### 数据库优化

#### 索引策略
```sql
-- 复合唯一索引，支持多种查找方式
ALTER TABLE system_attachment 
ADD UNIQUE KEY `uk_storage_identifier` (`storage`, `file_md5`, `storage_id`);

-- 用户文件查询优化
ALTER TABLE system_attachment_user 
ADD KEY `idx_user_tenant_cate` (`user_id`, `tenant_id`, `cate_id`);

-- 引用计数查询优化
ALTER TABLE system_attachment_user 
ADD KEY `idx_attachment_deleted` (`attachment_id`, `deleted_at`);
```

#### 查询优化
```php
/**
 * 优化的文件列表查询
 */
public function getUserFileList(array $params): array
{
    $query = AttachmentUserModel::alias('au')
        ->join('system_attachment a', 'au.attachment_id = a.id')
        ->where('au.user_id', $this->userId)
        ->where('au.tenant_id', $this->tenantId)
        ->where('au.deleted_at', null)
        ->where('a.deleted_at', null);
    
    // 使用覆盖索引，减少回表查询
    return $query->field([
        'au.id',
        'au.cate_id', 
        'au.display_name',
        'au.created_at',
        'a.size',
        'a.extension',
        'a.mime_type'
    ])->order('au.created_at desc')
    ->paginate();
}
```

### 应用层优化

#### 批量操作
```php
/**
 * 批量文件操作优化
 */
public function batchDeleteFiles(array $fileIds): bool
{
    return Db::transaction(function() use ($fileIds) {
        // 批量查询用户文件
        $userFiles = AttachmentUserModel::whereIn('id', $fileIds)
            ->where('user_id', $this->userId)
            ->select();
        
        $attachmentIds = $userFiles->column('attachment_id');
        
        // 批量软删除用户关联
        AttachmentUserModel::whereIn('id', $fileIds)->delete();
        
        // 批量更新引用计数
        foreach ($attachmentIds as $attachmentId) {
            AttachmentModel::where('id', $attachmentId)->dec('ref_count');
        }
        
        // 批量检查和删除无引用的文件
        $this->cleanupUnreferencedFiles($attachmentIds);
        
        return true;
    });
}
```

---

## 🔒 安全性设计

### 权限控制

#### 数据权限
```php
/**
 * 用户文件访问权限验证
 */
public function checkFileAccess(int $fileId, int $userId, int $tenantId): bool
{
    return AttachmentUserModel::where('id', $fileId)
        ->where('user_id', $userId)
        ->where('tenant_id', $tenantId)
        ->where('deleted_at', null)
        ->exists();
}
```

#### 文件访问控制
```php
/**
 * 文件下载权限验证
 */
public function downloadFile(int $fileId): Response
{
    // 验证用户权限
    if (!$this->checkFileAccess($fileId, $this->userId, $this->tenantId)) {
        throw new UnauthorizedException('无权限访问该文件');
    }
    
    // 获取文件信息
    $userFile = AttachmentUserModel::find($fileId);
    $attachment = AttachmentModel::find($userFile->attachment_id);
    
    // 返回文件流
    return $this->streamFile($attachment);
}
```

### 数据完整性

#### 事务保证
```php
/**
 * 事务保证数据一致性
 */
public function uploadFileWithTransaction(array $file, string $storage): array
{
    return Db::transaction(function() use ($file, $storage) {
        try {
            // 上传文件
            $result = $this->doUpload($file, $storage);
            
            // 验证结果
            $this->validateUploadResult($result);
            
            return $result;
        } catch (\Exception $e) {
            // 清理已上传的文件
            $this->cleanupFailedUpload($file, $storage);
            throw $e;
        }
    });
}
```

---

## 📈 监控和运维

### 性能监控
- 文件上传成功率
- 去重命中率
- 存储空间使用情况
- API响应时间

### 业务监控
- 用户文件数量统计
- 存储成本分析
- 热点文件分析
- 异常操作监控

### 告警机制
- 存储空间告警
- 上传失败率告警
- 性能异常告警
- 安全事件告警

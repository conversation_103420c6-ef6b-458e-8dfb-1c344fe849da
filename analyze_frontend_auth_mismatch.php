<?php
/**
 * 分析前端权限按钮与数据库权限的匹配情况
 */

require_once 'vendor/autoload.php';

echo "=== 分析前端权限按钮与数据库权限匹配情况 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取数据库中的权限数据:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type, sort
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY sort
    ");
    $stmt->execute();
    $dbPermissions = $stmt->fetchAll();
    
    echo "  数据库权限总数: " . count($dbPermissions) . " 个\n\n";
    
    // 从前端代码中提取的权限按钮配置
    $frontendAuthButtons = [
        // Admin.vue
        'system:permission:admin:add' => [
            'file' => 'Admin.vue',
            'line' => 23,
            'button' => '新增管理员',
            'expected_new' => 'system:permission_admin:add'
        ],
        
        // Role.vue
        'system:permission:role:add' => [
            'file' => 'Role.vue', 
            'line' => 20,
            'button' => '新增角色',
            'expected_new' => 'system:permission_role:add'
        ],
        
        // Menu.vue
        'system:permission_menu:add' => [
            'file' => 'Menu.vue',
            'line' => 25,
            'button' => '新增菜单',
            'expected_new' => 'system:permission_menu:add'
        ],
        'system:permission_menu:edit' => [
            'file' => 'Menu.vue',
            'line' => 312,
            'button' => '编辑菜单',
            'expected_new' => 'system:permission_menu:edit'
        ],
        'system:permission_menu:delete' => [
            'file' => 'Menu.vue',
            'line' => 313,
            'button' => '删除菜单',
            'expected_new' => 'system:permission_menu:delete'
        ],
        
        // Dept.vue
        'system:permission:department:add' => [
            'file' => 'Dept.vue',
            'line' => 21,
            'button' => '新增部门',
            'expected_new' => 'system:permission_department:add'
        ],
    ];
    
    echo "2. 前端权限按钮配置分析:\n";
    echo "  前端发现的权限按钮: " . count($frontendAuthButtons) . " 个\n\n";
    
    // 创建数据库权限名称索引
    $dbPermissionNames = array_column($dbPermissions, 'name');
    $dbPermissionIndex = array_flip($dbPermissionNames);
    
    echo "3. 权限匹配情况分析:\n";
    
    $matchedCount = 0;
    $mismatchedCount = 0;
    $missingInDb = [];
    $needsUpdate = [];
    
    foreach ($frontendAuthButtons as $frontendAuth => $config) {
        echo "  前端权限: {$frontendAuth}\n";
        echo "    文件: {$config['file']}:{$config['line']}\n";
        echo "    按钮: {$config['button']}\n";
        echo "    期望新格式: {$config['expected_new']}\n";
        
        // 检查前端权限是否在数据库中存在
        if (isset($dbPermissionIndex[$frontendAuth])) {
            echo "    ✅ 在数据库中找到旧格式权限\n";
            $matchedCount++;
        } else {
            echo "    ❌ 数据库中未找到旧格式权限\n";
            $missingInDb[] = $frontendAuth;
        }
        
        // 检查新格式权限是否在数据库中存在
        if (isset($dbPermissionIndex[$config['expected_new']])) {
            echo "    ✅ 数据库中已有新格式权限\n";
            $needsUpdate[] = [
                'file' => $config['file'],
                'line' => $config['line'],
                'old_auth' => $frontendAuth,
                'new_auth' => $config['expected_new'],
                'button' => $config['button']
            ];
        } else {
            echo "    ⚠️ 数据库中未找到新格式权限\n";
        }
        
        echo "\n";
    }
    
    echo "4. 匹配统计:\n";
    echo "  匹配成功: {$matchedCount} 个\n";
    echo "  需要更新: " . count($needsUpdate) . " 个\n";
    echo "  数据库中缺失: " . count($missingInDb) . " 个\n\n";
    
    if (!empty($missingInDb)) {
        echo "5. 数据库中缺失的权限:\n";
        foreach ($missingInDb as $missing) {
            echo "  - {$missing}\n";
        }
        echo "\n";
    }
    
    echo "6. 需要更新的前端文件:\n";
    if (!empty($needsUpdate)) {
        $fileGroups = [];
        foreach ($needsUpdate as $update) {
            $fileGroups[$update['file']][] = $update;
        }
        
        foreach ($fileGroups as $file => $updates) {
            echo "  📁 {$file}: " . count($updates) . " 处需要更新\n";
            foreach ($updates as $update) {
                echo "    第{$update['line']}行: {$update['old_auth']} → {$update['new_auth']}\n";
                echo "      按钮: {$update['button']}\n";
            }
            echo "\n";
        }
    } else {
        echo "  ✅ 所有前端权限按钮都已匹配\n\n";
    }
    
    echo "7. 查找其他可能的权限按钮:\n";
    
    // 查找system模块相关的权限，看是否有其他需要更新的
    $systemPermissions = array_filter($dbPermissions, function($perm) {
        return strpos($perm['name'], 'system:') === 0;
    });
    
    echo "  数据库中system模块权限: " . count($systemPermissions) . " 个\n";
    echo "  常见的权限操作:\n";
    
    $commonActions = ['add', 'edit', 'delete', 'index', 'detail'];
    $potentialMissing = [];
    
    foreach ($systemPermissions as $perm) {
        foreach ($commonActions as $action) {
            if (strpos($perm['name'], ':' . $action) !== false) {
                // 检查是否有对应的旧格式权限可能在前端使用
                $oldFormat = str_replace('_', ':', $perm['name']);
                if ($oldFormat !== $perm['name']) {
                    $potentialMissing[] = [
                        'new_format' => $perm['name'],
                        'possible_old_format' => $oldFormat,
                        'title' => $perm['title']
                    ];
                }
            }
        }
    }
    
    if (!empty($potentialMissing)) {
        echo "\n  可能需要检查的权限格式转换:\n";
        foreach (array_slice($potentialMissing, 0, 10) as $potential) {
            echo "    {$potential['possible_old_format']} → {$potential['new_format']} ({$potential['title']})\n";
        }
        if (count($potentialMissing) > 10) {
            echo "    ... 还有 " . (count($potentialMissing) - 10) . " 个\n";
        }
    }
    
    echo "\n8. 工作量评估:\n";
    echo "  📁 需要修改的前端文件: " . count($fileGroups) . " 个\n";
    echo "  🔧 需要更新的权限按钮: " . count($needsUpdate) . " 个\n";
    echo "  ⏱️ 预估工作时间: " . (count($fileGroups) * 15 + count($needsUpdate) * 5) . " 分钟\n";
    echo "  🧪 测试验证时间: " . (count($fileGroups) * 10) . " 分钟\n";
    echo "  📝 总计时间: " . (count($fileGroups) * 25 + count($needsUpdate) * 5) . " 分钟\n";
    
    echo "\n9. 实施建议:\n";
    echo "  1. 先备份前端文件\n";
    echo "  2. 逐个文件更新权限按钮配置\n";
    echo "  3. 更新后测试对应页面的权限控制\n";
    echo "  4. 验证按钮显示/隐藏是否正确\n";
    echo "  5. 检查是否有遗漏的权限按钮\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 分析完成 ===\n";

import request from '@/utils/http'
import { BaseResult } from '@/types/axios'

/**
 * HR月度统计相关接口
 */

// ==================== 接口数据类型定义 ====================

/**
 * 请假统计详情
 */
export interface LeaveStats {
  total_hours: number
  by_type: Record<number, { name: string; hours: number }>
}

/**
 * 时长显示格式
 */
export interface DaysBreakdown {
  days: number
  hours: number
  display: string
}

/**
 * 员工月度统计数据
 */
export interface HrMonthlyStatistics {
  year_month: string
  employee_id: number
  leave_stats: LeaveStats
  outing_hours: number
  business_trip_hours: number
  total_hours: number
  daily_work_hours: number
  display_format: string
  days_breakdown: DaysBreakdown
}

/**
 * 部门月度统计数据
 */
export interface HrDepartmentStatistics {
  dept_id: number
  year_month: string
  employee_count: number
  total_hours: number
  display_format: string
  days_breakdown: DaysBreakdown
  employees: (HrMonthlyStatistics & { employee_name: string })[]
}

/**
 * 员工年度统计数据
 */
export interface HrYearlyStatistics {
  year: number
  employee_id: number
  monthly_stats: HrMonthlyStatistics[]
  total_hours: number
  display_format: string
  days_breakdown: DaysBreakdown
}

/**
 * 统计概览数据
 */
export interface HrStatsOverview {
  year_month: string
  scope: 'employee' | 'all'
  employee_id: number
  leave_hours: number
  outing_hours: number
  business_trip_hours: number
  total_hours: number
  daily_work_hours: number
  display_format: string
  days_breakdown: DaysBreakdown
}

/**
 * 统计配置信息
 */
export interface HrStatsConfig {
  work_time_config: {
    work_time: string
    daily_work_hours: number
    segments: string[]
    description: string
  }
  leave_types: Record<number, string>
  approval_status: Record<number, string>
  calculation_rules: {
    min_unit: string
    rule_description: string
    display_format: string
  }
}

/**
 * 导出数据结构
 */
export interface HrExportData {
  title: string
  data: HrMonthlyStatistics | HrDepartmentStatistics
  summary: Record<string, string>
}

/**
 * 所有员工统计数据（分页）
 */
export interface HrAllEmployeeStats {
  data: (HrMonthlyStatistics & { employee_name: string; department: string })[]
  total: number
  page: number
  limit: number
  pages: number
}

// ==================== API接口类 ====================

export class HrMonthlyStatsApi {
  /**
   * 获取员工月度统计
   */
  static getEmployeeStats(params: {
    admin_id?: number
    year_month?: string
  }) {
    return request.get<BaseResult<HrMonthlyStatistics>>({
      url: '/hr/monthly_stats/employee',
      params
    })
  }

  /**
   * 获取所有员工月度统计（分页）
   */
  static getAllEmployeeStats(params: {
    year_month?: string
    page?: number
    limit?: number
  }) {
    return request.get<BaseResult<HrAllEmployeeStats>>({
      url: '/hr/monthly_stats/all_employees',
      params
    })
  }

  /**
   * 获取部门月度统计
   */
  static getDepartmentStats(params: {
    dept_id: number
    year_month?: string
  }) {
    return request.get<BaseResult<HrDepartmentStatistics>>({
      url: '/hr/monthly_stats/department',
      params
    })
  }

  /**
   * 获取员工年度统计
   */
  static getEmployeeYearlyStats(params: {
    admin_id?: number
    year?: number
  }) {
    return request.get<BaseResult<HrYearlyStatistics>>({
      url: '/hr/monthly_stats/yearly',
      params
    })
  }

  /**
   * 获取统计数据概览
   */
  static getStatsOverview(params: {
    admin_id?: number
    year_month?: string
  }) {
    return request.get<BaseResult<HrStatsOverview>>({
      url: '/hr/monthly_stats/overview',
      params
    })
  }

  /**
   * 导出员工月度统计
   */
  static exportEmployeeStats(params: {
    admin_id?: number
    year_month?: string
  }) {
    return request.get<BaseResult<HrExportData>>({
      url: '/hr/monthly_stats/export/employee',
      params
    })
  }

  /**
   * 导出部门月度统计
   */
  static exportDepartmentStats(params: {
    dept_id: number
    year_month?: string
  }) {
    return request.get<BaseResult<HrExportData>>({
      url: '/hr/monthly_stats/export/department',
      params
    })
  }

  /**
   * 获取统计配置信息
   */
  static getStatsConfig() {
    return request.get<BaseResult<HrStatsConfig>>({
      url: '/hr/monthly_stats/config'
    })
  }
}

// ==================== 辅助方法 ====================

/**
 * 格式化时长显示
 */
export function formatDuration(hours: number, dailyWorkHours: number = 8): string {
  if (hours <= 0) return '0小时'
  
  if (hours <= dailyWorkHours) {
    return `${hours}小时`
  }
  
  const days = Math.floor(hours / dailyWorkHours)
  const remainingHours = hours - (days * dailyWorkHours)
  
  let display = `${days}天`
  if (remainingHours > 0) {
    display += `${remainingHours}小时`
  }
  
  return display
}

/**
 * 获取请假类型名称
 */
export function getLeaveTypeName(type: number): string {
  const leaveTypes: Record<number, string> = {
    1: '年假',
    2: '事假',
    3: '病假',
    4: '婚假',
    5: '产假',
    6: '丧假',
    7: '其他'
  }
  return leaveTypes[type] || '未知'
}

/**
 * 获取审批状态名称
 */
export function getApprovalStatusName(status: number): string {
  const statusMap: Record<number, string> = {
    0: '草稿',
    1: '审批中',
    2: '已通过',
    3: '已拒绝',
    4: '已撤回'
  }
  return statusMap[status] || '未知'
}

/**
 * 计算统计数据的百分比
 */
export function calculatePercentage(value: number, total: number): string {
  if (total === 0) return '0%'
  return ((value / total) * 100).toFixed(1) + '%'
}

<?php
/**
 * 测试MenuService的buildMenuTree方法
 * 分析为什么传入192个菜单，输出只有3个
 */

require_once __DIR__ . '/vendor/autoload.php';

// 模拟数据库连接和环境
use think\facade\Db;
use app\system\service\MenuService;

class MenuBuildTest
{
    private $menuService;
    
    public function __construct()
    {
        $this->menuService = new MenuService();
    }
    
    /**
     * 测试buildMenuTree方法
     */
    public function testBuildMenuTree()
    {
        echo "=== 开始测试buildMenuTree方法 ===\n";
        
        // 1. 获取管理员215的所有菜单权限（模拟传入的数据）
        $menuList = $this->getAdmin215Menus();
        echo "传入菜单总数: " . count($menuList) . "\n";
        
        // 2. 分析菜单类型分布
        $this->analyzeMenuTypes($menuList);
        
        // 3. 分析顶级菜单
        $this->analyzeTopLevelMenus($menuList);
        
        // 4. 测试buildMenuTree方法
        echo "\n=== 开始调用buildMenuTree ===\n";
        $result = $this->menuService->buildMenuTree($menuList);
        
        echo "输出菜单数量: " . count($result) . "\n";
        
        // 5. 分析输出结果
        $this->analyzeResult($result);
        
        return $result;
    }
    
    /**
     * 获取管理员215的菜单权限（模拟数据）
     */
    private function getAdmin215Menus()
    {
        // 模拟从数据库获取的菜单数据
        $sql = "
            SELECT DISTINCT
                m.id,
                m.parent_id,
                m.title,
                m.name,
                m.path,
                m.component,
                m.type,
                m.icon,
                m.sort,
                m.external,
                m.keep_alive,
                m.visible,
                m.hide_tab,
                m.status
            FROM system_admin_role ar
            JOIN system_role_menu rm ON ar.role_id = rm.role_id
            JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = 215 
              AND m.status = 1 
              AND m.deleted_at IS NULL
            ORDER BY m.sort DESC
        ";
        
        try {
            $result = Db::query($sql);
            echo "SQL查询成功，获取到 " . count($result) . " 条记录\n";
            return collect($result); // 转换为Collection对象
        } catch (Exception $e) {
            echo "SQL查询失败: " . $e->getMessage() . "\n";
            return collect([]);
        }
    }
    
    /**
     * 分析菜单类型分布
     */
    private function analyzeMenuTypes($menuList)
    {
        $types = [
            0 => '目录',
            1 => '菜单', 
            2 => '按钮',
            3 => '扩展'
        ];
        
        echo "\n=== 菜单类型分布 ===\n";
        $typeCount = [];
        foreach ($menuList as $menu) {
            $type = $menu['type'];
            $typeCount[$type] = ($typeCount[$type] ?? 0) + 1;
        }
        
        foreach ($typeCount as $type => $count) {
            $typeName = $types[$type] ?? '其他';
            echo "类型 {$type} ({$typeName}): {$count} 个\n";
        }
    }
    
    /**
     * 分析顶级菜单
     */
    private function analyzeTopLevelMenus($menuList)
    {
        echo "\n=== 顶级菜单分析 ===\n";
        $topMenus = $menuList->where('parent_id', 0)->where('type', '!=', 2);
        echo "顶级菜单数量: " . $topMenus->count() . "\n";
        
        foreach ($topMenus as $menu) {
            echo "- ID: {$menu['id']}, 标题: {$menu['title']}, 类型: {$menu['type']}, 可见: {$menu['visible']}\n";
        }
    }
    
    /**
     * 分析输出结果
     */
    private function analyzeResult($result)
    {
        echo "\n=== 输出结果分析 ===\n";
        foreach ($result as $index => $menu) {
            echo "菜单 {$index}: ID={$menu['id']}, 标题={$menu['title']}, 子菜单数=" . count($menu['children'] ?? []) . "\n";
            
            // 分析子菜单
            if (!empty($menu['children'])) {
                foreach ($menu['children'] as $child) {
                    echo "  - 子菜单: ID={$child['id']}, 标题={$child['title']}\n";
                }
            }
        }
    }
}

// 运行测试
try {
    $test = new MenuBuildTest();
    $result = $test->testBuildMenuTree();
    
    echo "\n=== 测试完成 ===\n";
    echo "最终结果: " . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

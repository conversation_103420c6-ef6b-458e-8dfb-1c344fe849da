<?php
/**
 * 最终系统统一验证
 * 验证路由、权限、解析逻辑的统一效果
 */

require_once 'vendor/autoload.php';

echo "=== 最终系统统一验证 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 路由文件统一验证:\n";
    
    $routeFiles = [
        'route/Auth.php' => '认证路由',
        'route/System.php' => '系统管理路由',
        'route/Workflow.php' => '工作流路由',
        'route/Notice.php' => '通知路由',
        'route/Router.php' => '主路由',
        'route/Crm.php' => 'CRM路由',
        'route/Project.php' => '项目路由',
        'route/Office.php' => '办公路由',
        'route/Daily.php' => '每日报价路由',
        'route/Ims.php' => '库存路由'
    ];
    
    $routeFileResults = [];
    foreach ($routeFiles as $file => $desc) {
        $exists = file_exists($file);
        $hasPermissionMiddleware = false;
        $hasTokenMiddleware = false;
        
        if ($exists) {
            $content = file_get_contents($file);
            $hasPermissionMiddleware = strpos($content, 'PermissionMiddleware') !== false;
            $hasTokenMiddleware = strpos($content, 'TokenAuthMiddleware') !== false;
        }
        
        $routeFileResults[$file] = [
            'exists' => $exists,
            'permission_middleware' => $hasPermissionMiddleware,
            'token_middleware' => $hasTokenMiddleware
        ];
        
        $status = $exists ? '✅' : '❌';
        echo "  {$status} {$desc}: ";
        if ($exists) {
            $middlewareStatus = '';
            if ($file !== 'route/Auth.php') { // Auth.php不需要权限中间件
                $middlewareStatus = ($hasPermissionMiddleware && $hasTokenMiddleware) ? '✅ 中间件完整' : '⚠️ 中间件不完整';
            } else {
                $middlewareStatus = '✅ 无需权限验证';
            }
            echo $middlewareStatus;
        } else {
            echo '文件不存在';
        }
        echo "\n";
    }
    
    echo "\n2. 权限数据统一验证:\n";
    
    // 检查权限格式分布
    $stmt = $pdo->prepare("
        SELECT 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END as format_type,
            COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END
        ORDER BY count DESC
    ");
    $stmt->execute();
    $formatDistribution = $stmt->fetchAll();
    
    $totalPermissions = 0;
    $threePartsCount = 0;
    
    echo "  权限格式分布:\n";
    foreach ($formatDistribution as $format) {
        echo "    {$format['format_type']}: {$format['count']} 个\n";
        $totalPermissions += $format['count'];
        if ($format['format_type'] === '三段式') {
            $threePartsCount = $format['count'];
        }
    }
    
    $unificationRate = round($threePartsCount / $totalPermissions * 100, 1);
    echo "  权限统一率: {$unificationRate}% ({$threePartsCount}/{$totalPermissions})\n";
    
    echo "\n3. 权限解析逻辑验证:\n";
    
    // 模拟统一后的权限解析逻辑
    function camelToSnake($input) {
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
    }
    
    function generateUnifiedPermissionName($module, $parts, $controllerName, $method) {
        $snakeName = camelToSnake($controllerName);
        
        // 处理子目录控制器
        if (count($parts) > 4) {
            $subPath = strtolower($parts[3]);
            return $subPath . '_' . $snakeName . ':' . $method;
        }
        
        // 基础控制器直接使用控制器名
        return $snakeName . ':' . $method;
    }
    
    function parseUnifiedPermissionInfo($ruleName) {
        [$classPath, $method] = explode('@', $ruleName);
        $parts = explode('\\', $classPath);
        $module = strtolower($parts[1]);
        $controllerClass = $parts[count($parts) - 1];
        $controllerName = str_replace('Controller', '', $controllerClass);
        
        $permissionPath = generateUnifiedPermissionName($module, $parts, $controllerName, $method);
        
        return [$module, $permissionPath, $method];
    }
    
    function buildUnifiedPermission($ruleName) {
        [$module, $permissionPath, $action] = parseUnifiedPermissionInfo($ruleName);
        return strtolower("{$module}:{$permissionPath}");
    }
    
    // 测试统一后的权限解析
    $testCases = [
        'app\\crm\\controller\\CrmCustomerMyController@index' => 'crm:crm_customer_my:index',
        'app\\system\\controller\\permission\\AdminController@index' => 'system:permission_admin:index',
        'app\\workflow\\controller\\TaskController@index' => 'workflow:task:index',
        'app\\project\\controller\\ProjectController@index' => 'project:project:index',
        'app\\notice\\controller\\MessageController@delete' => 'notice:message:delete',
    ];
    
    $passedTests = 0;
    $totalTests = count($testCases);
    
    foreach ($testCases as $input => $expected) {
        $result = buildUnifiedPermission($input);
        
        // 检查权限是否存在于数据库
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE name = ? AND status = 1 AND deleted_at IS NULL");
        $stmt->execute([$expected]);
        $exists = $stmt->fetch()['count'] > 0;
        
        $parseStatus = ($result === $expected) ? '✅' : '❌';
        $dbStatus = $exists ? '✅' : '❌';
        
        echo "  {$parseStatus}{$dbStatus} {$input}\n";
        echo "    解析: {$result}\n";
        echo "    期望: {$expected}\n";
        echo "    数据库: " . ($exists ? '存在' : '不存在') . "\n";
        
        if ($result === $expected && $exists) {
            $passedTests++;
        }
        echo "\n";
    }
    
    $parseSuccessRate = round($passedTests / $totalTests * 100, 1);
    echo "  权限解析测试通过率: {$parseSuccessRate}% ({$passedTests}/{$totalTests})\n";
    
    echo "\n4. 用户权限验证:\n";
    
    // 检查tenant_admin用户权限
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM system_admin_role ar
        LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
        LEFT JOIN system_menu m ON rm.menu_id = m.id
        WHERE ar.admin_id = 201 AND m.status = 1 AND m.deleted_at IS NULL
    ");
    $stmt->execute();
    $userPermissionCount = $stmt->fetch()['count'];
    
    $permissionCoverage = round($userPermissionCount / $totalPermissions * 100, 1);
    echo "  tenant_admin权限数: {$userPermissionCount} 个\n";
    echo "  权限覆盖率: {$permissionCoverage}%\n";
    
    echo "\n5. 系统统一完成度评估:\n";
    
    $routeFileScore = count(array_filter($routeFileResults, fn($r) => $r['exists'])) / count($routeFileResults) * 100;
    $middlewareScore = count(array_filter($routeFileResults, fn($r) => $r['permission_middleware'] || $r['exists'] && basename(array_search($r, $routeFileResults)) === 'Auth.php')) / count($routeFileResults) * 100;
    
    $scores = [
        '路由文件完整性' => $routeFileScore,
        '中间件配置' => $middlewareScore,
        '权限格式统一' => $unificationRate,
        '权限解析准确性' => $parseSuccessRate,
        '用户权限覆盖' => $permissionCoverage
    ];
    
    $totalScore = array_sum($scores) / count($scores);
    
    foreach ($scores as $item => $score) {
        $status = $score >= 80 ? '✅' : ($score >= 60 ? '⚠️' : '❌');
        echo "  {$status} {$item}: {$score}%\n";
    }
    
    echo "\n  总体完成度: " . round($totalScore, 1) . "%\n";
    
    if ($totalScore >= 80) {
        echo "  ✅ 系统统一规范化成功完成！\n";
        echo "  \n";
        echo "  主要成果:\n";
        echo "  - 路由文件已统一创建和配置\n";
        echo "  - 权限格式基本统一为三段式\n";
        echo "  - 权限解析逻辑已完全统一\n";
        echo "  - 用户权限覆盖率良好\n";
        echo "  \n";
        echo "  建议:\n";
        echo "  1. 重新登录系统测试功能\n";
        echo "  2. 监控系统运行状况\n";
        echo "  3. 根据使用情况进一步优化\n";
    } else {
        echo "  ⚠️ 系统统一规范化部分完成\n";
        echo "  需要进一步完善和优化\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 系统统一验证完成 ===\n";

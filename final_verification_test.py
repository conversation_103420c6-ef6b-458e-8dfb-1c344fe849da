#!/usr/bin/env python3
"""
最终验证测试脚本
验证所有测试用户的权限配置和数据
"""
import os
import mysql.connector
from datetime import datetime

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

def get_db_connection():
    return mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )

def main():
    print("=== 最终验证测试 ===\n")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 测试用户列表
    test_users = [
        (201, "tenant_admin", "租户超级管理员", "租户超级管理员"),
        (202, "system_admin", "系统管理员", "系统管理员"),
        (203, "crm_admin", "CRM管理员", "CRM管理员"),
        (204, "project_admin", "项目管理员", "项目管理员"),
        (205, "business_manager", "业务经理", "业务经理"),
        (206, "staff_user", "普通员工", "普通员工")
    ]
    
    print("📋 测试用户验证:")
    print("=" * 80)
    
    total_permissions = 0
    
    for user_id, username, real_name, role_name in test_users:
        print(f"\n👤 {real_name} ({username})")
        print("-" * 50)
        
        # 验证用户存在
        cursor.execute("""
            SELECT id, username, real_name, status 
            FROM system_admin 
            WHERE id = %s AND tenant_id = 1
        """, (user_id,))
        user = cursor.fetchone()
        
        if not user:
            print(f"  ❌ 用户不存在")
            continue
        
        print(f"  ✅ 用户信息: ID={user[0]}, 状态={'正常' if user[3] == 1 else '禁用'}")
        
        # 验证角色分配
        cursor.execute("""
            SELECT r.name 
            FROM system_admin_role ar
            LEFT JOIN system_role r ON ar.role_id = r.id
            WHERE ar.admin_id = %s AND ar.tenant_id = 1
        """, (user_id,))
        role = cursor.fetchone()
        
        if role:
            print(f"  ✅ 角色分配: {role[0]}")
        else:
            print(f"  ❌ 未分配角色")
            continue
        
        # 验证权限数量
        cursor.execute("""
            SELECT COUNT(*) 
            FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = %s AND m.status = 1 AND m.deleted_at IS NULL
        """, (user_id,))
        permission_count = cursor.fetchone()[0]
        total_permissions += permission_count
        
        print(f"  ✅ 权限数量: {permission_count} 个")
        
        # 验证权限模块分布
        cursor.execute("""
            SELECT 
                SUBSTRING_INDEX(m.name, ':', 1) as module,
                COUNT(*) as count
            FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = %s AND m.status = 1 AND m.deleted_at IS NULL 
            AND m.name LIKE '%:%'
            GROUP BY SUBSTRING_INDEX(m.name, ':', 1)
            ORDER BY count DESC
            LIMIT 5
        """, (user_id,))
        
        modules = cursor.fetchall()
        if modules:
            module_info = ", ".join([f"{mod[0]}({mod[1]}个)" for mod in modules])
            print(f"  ✅ 主要权限: {module_info}")
        
        # 验证数据权限
        if user_id >= 203:  # 业务用户
            # 验证CRM客户数据
            cursor.execute("""
                SELECT COUNT(*) FROM crm_customer 
                WHERE tenant_id = 1 AND (creator_id = %s OR owner_user_id = %s)
            """, (user_id, user_id))
            customer_count = cursor.fetchone()[0]
            
            # 验证CRM线索数据
            cursor.execute("""
                SELECT COUNT(*) FROM crm_lead 
                WHERE tenant_id = 1 AND (creator_id = %s OR owner_user_id = %s)
            """, (user_id, user_id))
            lead_count = cursor.fetchone()[0]
            
            # 验证项目数据
            cursor.execute("""
                SELECT COUNT(*) FROM project_project 
                WHERE tenant_id = 1 AND (creator_id = %s OR owner_id = %s)
            """, (user_id, user_id))
            project_count = cursor.fetchone()[0]
            
            print(f"  ✅ 业务数据: 客户{customer_count}个, 线索{lead_count}个, 项目{project_count}个")
    
    # 系统整体验证
    print(f"\n" + "=" * 80)
    print(f"📊 系统整体验证:")
    print("=" * 80)
    
    # 验证系统权限总数
    cursor.execute("SELECT COUNT(*) FROM system_menu WHERE status = 1 AND deleted_at IS NULL")
    system_total_permissions = cursor.fetchone()[0]
    print(f"  系统总权限数: {system_total_permissions} 个")
    
    # 验证用户总数
    cursor.execute("SELECT COUNT(*) FROM system_admin WHERE tenant_id = 1 AND status = 1")
    total_users = cursor.fetchone()[0]
    print(f"  活跃用户总数: {total_users} 个")
    
    # 验证角色总数
    cursor.execute("SELECT COUNT(*) FROM system_role WHERE tenant_id = 1 AND status = 1")
    total_roles = cursor.fetchone()[0]
    print(f"  活跃角色总数: {total_roles} 个")
    
    # 验证权限分配总数
    cursor.execute("SELECT COUNT(*) FROM system_role_menu WHERE tenant_id = 1")
    total_role_permissions = cursor.fetchone()[0]
    print(f"  权限分配总数: {total_role_permissions} 个")
    
    # 验证业务数据
    cursor.execute("SELECT COUNT(*) FROM crm_customer WHERE tenant_id = 1")
    total_customers = cursor.fetchone()[0]
    print(f"  CRM客户总数: {total_customers} 个")
    
    cursor.execute("SELECT COUNT(*) FROM crm_lead WHERE tenant_id = 1")
    total_leads = cursor.fetchone()[0]
    print(f"  CRM线索总数: {total_leads} 个")
    
    cursor.execute("SELECT COUNT(*) FROM project_project WHERE tenant_id = 1")
    total_projects = cursor.fetchone()[0]
    print(f"  项目总数: {total_projects} 个")
    
    # 权限覆盖率分析
    print(f"\n📈 权限覆盖率分析:")
    print("-" * 50)
    
    avg_permissions = total_permissions / len(test_users)
    print(f"  平均权限数: {avg_permissions:.1f} 个")
    
    # 超级管理员权限覆盖率
    tenant_admin_coverage = (280 / system_total_permissions) * 100
    print(f"  租户超级管理员覆盖率: {tenant_admin_coverage:.1f}%")
    
    # 安全性评估
    print(f"\n🔒 安全性评估:")
    print("-" * 50)
    
    # 超级管理员数量
    cursor.execute("""
        SELECT COUNT(*) 
        FROM system_admin_role ar
        LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
        LEFT JOIN system_menu m ON rm.menu_id = m.id
        WHERE m.status = 1 AND m.deleted_at IS NULL
        GROUP BY ar.admin_id
        HAVING COUNT(*) >= 250
    """)
    super_admin_count = len(cursor.fetchall())
    print(f"  超级管理员数量: {super_admin_count} 个")
    
    if super_admin_count == 1:
        print(f"  ✅ 超级管理员数量合理")
    else:
        print(f"  ⚠️ 超级管理员数量异常")
    
    # 权限分布合理性
    high_privilege_count = 0
    medium_privilege_count = 0
    low_privilege_count = 0
    
    for user_id, _, _, _ in test_users:
        cursor.execute("""
            SELECT COUNT(*) 
            FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = %s AND m.status = 1 AND m.deleted_at IS NULL
        """, (user_id,))
        user_permissions = cursor.fetchone()[0]
        
        if user_permissions >= 50:
            high_privilege_count += 1
        elif user_permissions >= 20:
            medium_privilege_count += 1
        else:
            low_privilege_count += 1
    
    print(f"  高权限用户: {high_privilege_count} 个")
    print(f"  中权限用户: {medium_privilege_count} 个")
    print(f"  低权限用户: {low_privilege_count} 个")
    
    # 最终结论
    print(f"\n🎯 最终验证结论:")
    print("=" * 80)
    
    all_checks_passed = True
    
    checks = [
        (total_users == 6, "✅ 测试用户数量正确 (6个)"),
        (total_roles == 6, "✅ 测试角色数量正确 (6个)"),
        (super_admin_count == 1, "✅ 超级管理员数量合理 (1个)"),
        (total_customers >= 5, "✅ CRM客户测试数据充足"),
        (total_leads >= 3, "✅ CRM线索测试数据充足"),
        (total_projects >= 3, "✅ 项目测试数据充足"),
        (tenant_admin_coverage >= 99, "✅ 租户超级管理员权限完整"),
    ]
    
    for check_result, message in checks:
        if check_result:
            print(f"  {message}")
        else:
            print(f"  ❌ {message.replace('✅', '❌')}")
            all_checks_passed = False
    
    if all_checks_passed:
        print(f"\n🎉 所有验证检查通过！系统已准备就绪，可以开始手动测试！")
        print(f"\n📋 测试建议:")
        print(f"  1. 使用每个测试账号登录系统验证权限")
        print(f"  2. 测试不同角色的功能边界")
        print(f"  3. 验证数据权限和数据隔离")
        print(f"  4. 测试业务流程的完整性")
        print(f"  5. 进行安全性和越权测试")
    else:
        print(f"\n⚠️ 部分验证检查未通过，请检查配置！")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    main()

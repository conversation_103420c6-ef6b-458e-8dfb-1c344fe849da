<?php

namespace app\system\controller;

use app\common\core\base\BaseController;
use app\system\service\AuthService;
use think\Response;

class AuthController extends BaseController
{
	/**
	 * @var AuthService
	 */
	private AuthService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取AuthService实例
		$this->service = AuthService::getInstance();
	}
	
	/**
	 * 用户登录
	 * 
	 * @return Response
	 */
	public function login(): Response
	{
		// 调用AuthService的login方法处理登录
		$result = $this->service->login(input());
		return $this->success('登录成功', $result);
	}
	
	/**
	 * 用户退出
	 * 
	 * @return Response
	 */
	public function logout(): Response
	{
		// 调用AuthService的logout方法处理退出
		$result = $this->service->logout($this->request->header('Authorization'));
		return $result 
			? $this->success('退出成功')
			: $this->error('退出失败');
	}
	
	/**
	 * 获取验证码
	 * 
	 * @return Response
	 */
	public function captcha(): Response
	{
		// 使用AuthService生成验证码
		$data = $this->service->generateCaptcha();
		return $this->success('获取成功', $data);
	}
}
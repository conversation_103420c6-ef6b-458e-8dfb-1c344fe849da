<?php
/**
 * 实施控制器文件重命名
 */

echo "=== 开始实施控制器文件重命名 ===\n\n";

// 需要重命名的控制器文件列表
$filesToRename = [
    // hr模块
    'app/hr/controller/HrMonthlyStats.php' => 'app/hr/controller/HrMonthlyStatsController.php',
    
    // system模块基础控制器
    'app/system/controller/Attachment.php' => 'app/system/controller/AttachmentController.php',
    'app/system/controller/AttachmentCat.php' => 'app/system/controller/AttachmentCatController.php',
    'app/system/controller/Auth.php' => 'app/system/controller/AuthController.php',
    'app/system/controller/Config.php' => 'app/system/controller/ConfigController.php',
    'app/system/controller/Upload.php' => 'app/system/controller/UploadController.php',
    
    // system模块日志控制器
    'app/system/controller/log/Login.php' => 'app/system/controller/log/LoginController.php',
    'app/system/controller/log/Operation.php' => 'app/system/controller/log/OperationController.php',
    
    // system模块权限控制器
    'app/system/controller/permission/Admin.php' => 'app/system/controller/permission/AdminController.php',
    'app/system/controller/permission/Department.php' => 'app/system/controller/permission/DepartmentController.php',
    'app/system/controller/permission/Menu.php' => 'app/system/controller/permission/MenuController.php',
    'app/system/controller/permission/Post.php' => 'app/system/controller/permission/PostController.php',
    'app/system/controller/permission/Role.php' => 'app/system/controller/permission/RoleController.php',
    
    // system模块租户控制器
    'app/system/controller/tenant/TenantConfig.php' => 'app/system/controller/tenant/TenantConfigController.php',
];

// 类名映射
$classNameMap = [
    'HrMonthlyStats' => 'HrMonthlyStatsController',
    'Attachment' => 'AttachmentController',
    'AttachmentCat' => 'AttachmentCatController',
    'Auth' => 'AuthController',
    'Config' => 'ConfigController',
    'Upload' => 'UploadController',
    'Login' => 'LoginController',
    'Operation' => 'OperationController',
    'Admin' => 'AdminController',
    'Department' => 'DepartmentController',
    'Menu' => 'MenuController',
    'Post' => 'PostController',
    'Role' => 'RoleController',
    'TenantConfig' => 'TenantConfigController',
];

$successCount = 0;
$errorCount = 0;

echo "1. 开始重命名控制器文件:\n";

foreach ($filesToRename as $oldFile => $newFile) {
    echo "  处理: {$oldFile}\n";
    
    if (!file_exists($oldFile)) {
        echo "    ❌ 源文件不存在: {$oldFile}\n";
        $errorCount++;
        continue;
    }
    
    if (file_exists($newFile)) {
        echo "    ⚠️ 目标文件已存在: {$newFile}\n";
        $errorCount++;
        continue;
    }
    
    // 读取文件内容
    $content = file_get_contents($oldFile);
    
    // 更新类名
    $oldClassName = basename($oldFile, '.php');
    $newClassName = $classNameMap[$oldClassName] ?? $oldClassName . 'Controller';
    
    // 替换类名
    $content = preg_replace(
        '/class\s+' . preg_quote($oldClassName, '/') . '\s+/',
        'class ' . $newClassName . ' ',
        $content
    );
    
    // 创建目标目录（如果不存在）
    $targetDir = dirname($newFile);
    if (!is_dir($targetDir)) {
        mkdir($targetDir, 0755, true);
    }
    
    // 写入新文件
    if (file_put_contents($newFile, $content) !== false) {
        // 删除旧文件
        if (unlink($oldFile)) {
            echo "    ✅ 成功重命名: {$oldFile} → {$newFile}\n";
            echo "    ✅ 类名更新: {$oldClassName} → {$newClassName}\n";
            $successCount++;
        } else {
            echo "    ❌ 删除旧文件失败: {$oldFile}\n";
            $errorCount++;
        }
    } else {
        echo "    ❌ 写入新文件失败: {$newFile}\n";
        $errorCount++;
    }
    
    echo "\n";
}

echo "2. 重命名结果统计:\n";
echo "  成功: {$successCount} 个文件\n";
echo "  失败: {$errorCount} 个文件\n\n";

if ($successCount > 0) {
    echo "✅ 控制器文件重命名完成！\n";
} else {
    echo "❌ 控制器文件重命名失败！\n";
}

echo "\n=== 控制器文件重命名完成 ===\n";

<?php
/**
 * 更新权限解析逻辑以适配规范化后的权限
 */

echo "=== 更新权限解析逻辑 ===\n\n";

echo "1. 备份当前PermissionService.php:\n";

$serviceFile = 'app/system/service/PermissionService.php';
$backupFile = 'app/system/service/PermissionService.php.backup.' . date('Ymd_His');

if (file_exists($serviceFile)) {
    copy($serviceFile, $backupFile);
    echo "  ✅ 已备份到: {$backupFile}\n";
} else {
    echo "  ❌ 源文件不存在: {$serviceFile}\n";
    exit;
}

echo "\n2. 生成新的权限解析逻辑:\n";

$newPermissionLogic = '
    /**
     * 生成权限名称（适配规范化后的权限）
     *
     * @param string $module 模块名
     * @param array $parts 路径部分
     * @param string $controllerName 控制器名（不含Controller后缀）
     * @param string $method 方法名
     * @return string 权限名称
     */
    private function generatePermissionName(string $module, array $parts, string $controllerName, string $method): string
    {
        // 处理不同模块的权限命名规则（规范化后）
        switch ($module) {
            case \'crm\':
                return $this->generateCrmPermissionName($controllerName, $method);
            case \'system\':
                return $this->generateSystemPermissionName($parts, $controllerName, $method);
            case \'project\':
                return $this->generateProjectPermissionName($controllerName, $method);
            case \'notice\':
                return $this->generateNoticePermissionName($controllerName, $method);
            case \'office\':
                return $this->generateOfficePermissionName($controllerName, $method);
            case \'workflow\':
                return $this->generateWorkflowPermissionName($controllerName, $method);
            default:
                // 其他模块使用默认规则
                return strtolower($module . \'_\' . $controllerName) . \':\' . $method;
        }
    }

    /**
     * 生成CRM模块权限名称（规范化后）
     */
    private function generateCrmPermissionName(string $controllerName, string $method): string
    {
        // CRM模块的权限格式: crm_模块名:方法
        $snakeName = $this->camelToSnake($controllerName);
        
        // 确保以crm_开头
        if (!str_starts_with($snakeName, \'crm_\')) {
            $snakeName = \'crm_\' . $snakeName;
        }
        
        return $snakeName . \':\' . $method;
    }

    /**
     * 生成System模块权限名称（规范化后）
     */
    private function generateSystemPermissionName(array $parts, string $controllerName, string $method): string
    {
        // System模块的权限格式根据子目录而定
        if (count($parts) > 4) {
            // 有子目录，如: system:permission:admin:index
            $subPath = strtolower($parts[3]);
            $controller = strtolower($controllerName);
            return $subPath . \':\' . $controller . \':\' . $method;
        } else {
            // 无子目录的基础控制器
            $controller = strtolower($controllerName);
            
            // 规范化后的基础控制器权限格式
            $baseControllers = [
                \'article\', \'attachment\', \'auth\', \'config\', \'dict\', \'log\', 
                \'system\', \'upload\', \'tenant\', \'attachmentcat\'
            ];
            
            if (in_array($controller, $baseControllers)) {
                // 基础控制器使用简化格式: system:controller:method
                return $controller . \':\' . $method;
            } else {
                // 其他控制器使用完整格式: system:system_controller:method
                return \'system_\' . $controller . \':\' . $method;
            }
        }
    }

    /**
     * 生成Project模块权限名称（规范化后）
     */
    private function generateProjectPermissionName(string $controllerName, string $method): string
    {
        // Project模块的权限格式
        $snakeName = $this->camelToSnake($controllerName);
        
        // 特殊处理：Project控制器映射为project:project
        if ($controllerName === \'Project\') {
            return \'project:\' . $method;
        }
        
        // 其他控制器使用下划线格式
        return $snakeName . \':\' . $method;
    }

    /**
     * 生成Notice模块权限名称（规范化后）
     */
    private function generateNoticePermissionName(string $controllerName, string $method): string
    {
        $snakeName = $this->camelToSnake($controllerName);
        
        // Notice模块的权限格式: notice_模块名:方法
        if (!str_starts_with($snakeName, \'notice_\')) {
            $snakeName = \'notice_\' . $snakeName;
        }
        
        return $snakeName . \':\' . $method;
    }

    /**
     * 生成Office模块权限名称（规范化后）
     */
    private function generateOfficePermissionName(string $controllerName, string $method): string
    {
        $snakeName = $this->camelToSnake($controllerName);
        
        // Office模块的权限格式: office_模块名:方法
        if (!str_starts_with($snakeName, \'office_\')) {
            $snakeName = \'office_\' . $snakeName;
        }
        
        return $snakeName . \':\' . $method;
    }

    /**
     * 生成Workflow模块权限名称（规范化后）
     */
    private function generateWorkflowPermissionName(string $controllerName, string $method): string
    {
        $snakeName = $this->camelToSnake($controllerName);
        
        // Workflow模块的权限格式: workflow_模块名:方法
        if (!str_starts_with($snakeName, \'workflow_\')) {
            $snakeName = \'workflow_\' . $snakeName;
        }
        
        return $snakeName . \':\' . $method;
    }
';

echo "  ✅ 新的权限解析逻辑已生成\n";

echo "\n3. 更新PermissionService.php文件:\n";

// 读取当前文件内容
$currentContent = file_get_contents($serviceFile);

// 查找并替换generatePermissionName方法及相关方法
$pattern = '/\/\*\*\s*\n\s*\* 生成权限名称.*?\n\s*\*\/\s*\n\s*private function generatePermissionName.*?\n\s*\}/s';

if (preg_match($pattern, $currentContent)) {
    echo "  找到现有的generatePermissionName方法，准备替换...\n";
    
    // 替换方法
    $updatedContent = preg_replace($pattern, $newPermissionLogic, $currentContent);
    
    // 写入文件
    file_put_contents($serviceFile, $updatedContent);
    echo "  ✅ PermissionService.php 已更新\n";
} else {
    echo "  ❌ 未找到generatePermissionName方法，需要手动添加\n";
}

echo "\n4. 验证更新结果:\n";

// 验证文件是否正确更新
if (strpos(file_get_contents($serviceFile), \'generateCrmPermissionName\') !== false) {
    echo "  ✅ 新的CRM权限生成方法已添加\n";
} else {
    echo "  ❌ CRM权限生成方法添加失败\n";
}

if (strpos(file_get_contents($serviceFile), \'generateNoticePermissionName\') !== false) {
    echo "  ✅ 新的Notice权限生成方法已添加\n";
} else {
    echo "  ❌ Notice权限生成方法添加失败\n";
}

echo "\n5. 清除权限缓存:\n";

// 清除可能的权限缓存
$cacheFiles = [
    \'runtime/cache\',
    \'cache/permission.php\',
    \'cache/route.php\'
];

$clearedFiles = 0;
foreach ($cacheFiles as $file) {
    if (file_exists($file)) {
        if (is_dir($file)) {
            // 清除目录内容
            $files = glob($file . \'/*\');
            foreach ($files as $f) {
                if (is_file($f)) {
                    unlink($f);
                    $clearedFiles++;
                }
            }
        } else {
            unlink($file);
            $clearedFiles++;
        }
    }
}

echo "  ✅ 清除了 {$clearedFiles} 个缓存文件\n";

echo "\n6. 权限解析逻辑更新完成!\n";
echo "  ✅ PermissionService.php 已更新\n";
echo "  ✅ 权限缓存已清除\n";
echo "  ✅ 可以继续下一步：验证权限功能\n";

echo "\n=== 权限解析逻辑更新完成 ===\n";

<template>
  <!-- 详情抽屉 -->
  <el-drawer v-model="visible" title="📄 汇报详情" size="60%" destroy-on-close>
    <div class="detail-content" v-loading="loading">
      <!-- 头部信息 -->
      <div class="detail-header">
        <div class="report-info">
          <h2>{{ detailData.title || '未知标题' }}</h2>
          <div class="meta-info">
            <el-tag :type="getTypeColor(detailData.type)">{{ getTypeText(detailData.type) }}</el-tag>
            <span>📅 {{ detailData.report_date }}</span>
            <span>👤 {{ detailData.creator_name || '未知' }}</span>
            <span>⏰ {{ formatTime(detailData.created_at) }}</span>
          </div>
        </div>
        <div class="actions" v-if="showActions">
          <el-button type="primary" plain @click="handleEdit">✏️ 编辑</el-button>
          <el-button type="danger" plain @click="handleDelete">🗑️ 删除</el-button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="detail-body">
        <div class="content-section" v-if="detailData.content">
          <h3>📝 工作内容</h3>
          <div class="content-text">{{ detailData.content }}</div>
        </div>

        <div class="content-section" v-if="detailData.summary">
          <h3>📊 工作总结</h3>
          <div class="content-text">{{ detailData.summary }}</div>
        </div>

        <div class="content-section" v-if="detailData.plan">
          <h3>📅 下期计划</h3>
          <div class="content-text">{{ detailData.plan }}</div>
        </div>

        <div class="content-section" v-if="detailData.attachments">
          <h3>📎 附件列表</h3>
          <div class="attachment-list">
            <div class="attachment-item">
              <span>📄 {{ detailData.attachments }}</span>
              <el-button type="primary" link size="small">📥 下载</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { CrmWorkReportApi } from '@/api/crm/crmWorkReport'
  import { ApiStatus } from '@/utils/http/status'

  interface Props {
    showActions?: boolean // 是否显示操作按钮
  }

  const props = withDefaults(defineProps<Props>(), {
    showActions: true
  })

  const emit = defineEmits<{
    edit: [id: number]
    delete: [id: number]
    success: []
  }>()

  const visible = ref(false)
  const loading = ref(false)
  const detailData = ref<any>({})

  // 显示详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      visible.value = true
      const res = await CrmWorkReportApi.detail(id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data
      } else {
        ElMessage.error(res.message || '获取详情失败')
        visible.value = false
      }
    } finally {
      loading.value = false
    }
  }

  // 获取类型颜色
  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      daily: 'primary',
      weekly: 'success',
      monthly: 'warning'
    }
    return colors[type] || 'info'
  }

  // 获取类型文本
  const getTypeText = (type: string) => {
    const texts: Record<string, string> = {
      daily: '日报',
      weekly: '周报',
      monthly: '月报'
    }
    return texts[type] || type
  }

  // 格式化时间
  const formatTime = (time: string) => {
    if (!time) return ''
    return new Date(time).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 处理编辑
  const handleEdit = () => {
    emit('edit', detailData.value.id)
  }

  // 处理删除
  const handleDelete = async () => {
    try {
      await ElMessageBox.confirm('确定要删除这条汇报吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await CrmWorkReportApi.delete(detailData.value.id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        visible.value = false
        emit('delete', detailData.value.id)
        emit('success')
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch {
      // 用户取消删除
    } finally {
      loading.value = false
    }
  }

  // 暴露方法
  defineExpose({
    showDetail
  })
</script>

<style scoped lang="scss">
  // 详情页面样式
  .detail-content {
    padding: 20px;
  }

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .report-info h2 {
    margin: 0 0 8px 0;
    color: #1f2329;
    font-size: 20px;
    font-weight: 600;
  }

  .meta-info {
    display: flex;
    gap: 12px;
    align-items: center;
    color: #646a73;
    font-size: 14px;
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .content-section {
    margin-bottom: 24px;
  }

  .content-section h3 {
    margin: 0 0 12px 0;
    color: #1f2329;
    font-size: 16px;
    font-weight: 600;
  }

  .content-text {
    background: #f7f8fa;
    padding: 16px;
    border-radius: 8px;
    line-height: 1.6;
    color: #1f2329;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .attachment-list {
    background: #f7f8fa;
    padding: 16px;
    border-radius: 8px;
  }

  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e4e7ed;

    &:last-child {
      border-bottom: none;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .detail-header {
      flex-direction: column;
      gap: 16px;
    }

    .meta-info {
      flex-wrap: wrap;
    }

    .actions {
      flex-wrap: wrap;
      gap: 4px;
    }
  }
</style>

<?php
declare(strict_types=1);

namespace app\hr\service;

use app\common\core\base\BaseService;
use app\common\utils\DateCalculator;
use app\system\service\TenantConfigService;

/**
 * HR工作时间配置服务类
 * 负责处理工作时间相关的配置和计算
 */
class HrWorkTimeService
{
    /**
     * 获取每日工作小时数
     * 从租户配置中读取工作时间配置并解析
     * @return float 每日工作小时数
     */
    public function getDailyWorkHours(): float
    {
        $tenantConfigService = TenantConfigService::getInstance();
        $enterpriseConfig = $tenantConfigService->getInfo('enterprise');
        
        // 从配置中获取工作时间，默认为 '08:00-12:00,14:00-18:00'
        $workTime = $enterpriseConfig['work_time'] ?? '08:00-12:00,14:00-18:00';
        
        return DateCalculator::parseWorkTimeConfig($workTime);
    }
    
    /**
     * 获取工作时间配置详情
     * 返回完整的工作时间配置信息
     * @return array 工作时间配置详情
     */
    public function getWorkTimeConfig(): array
    {
        $tenantConfigService = TenantConfigService::getInstance();
        $enterpriseConfig = $tenantConfigService->getInfo('enterprise');
        
        // 从配置中获取工作时间
        $workTime = $enterpriseConfig['work_time'] ?? '08:00-12:00,14:00-18:00';
        $dailyHours = $this->getDailyWorkHours();
        
        return [
            'work_time' => $workTime,
            'daily_work_hours' => $dailyHours,
            'segments' => explode(',', $workTime),
            'description' => $this->formatWorkTimeDescription($workTime, $dailyHours)
        ];
    }
    
    /**
     * 格式化工作时间描述
     * 将工作时间配置转换为易读的描述文本
     * 
     * @param string $workTime 工作时间配置
     * @param float $dailyHours 每日工作小时数
     * @return string 格式化后的描述
     */
    private function formatWorkTimeDescription(string $workTime, float $dailyHours): string
    {
        $segments = explode(',', $workTime);
        $descriptions = [];
        
        foreach ($segments as $segment) {
            $segment = trim($segment);
            if (strpos($segment, '-') !== false) {
                [$start, $end] = explode('-', $segment);
                $descriptions[] = trim($start) . '-' . trim($end);
            }
        }
        
        $timeDesc = implode('，', $descriptions);
        return "工作时间：{$timeDesc}，每日工作{$dailyHours}小时";
    }
    
    /**
     * 验证工作时间配置格式
     * 检查工作时间配置字符串是否符合预期格式
     * 
     * @param string $workTime 工作时间配置
     * @return array 验证结果 ['valid' => bool, 'message' => string, 'parsed_hours' => float]
     */
    public function validateWorkTimeConfig(string $workTime): array
    {
        if (empty($workTime)) {
            return [
                'valid' => false,
                'message' => '工作时间配置不能为空',
                'parsed_hours' => 0
            ];
        }
        
        try {
            $parsedHours = DateCalculator::parseWorkTimeConfig($workTime);
            
            if ($parsedHours <= 0) {
                return [
                    'valid' => false,
                    'message' => '工作时间配置解析失败，请检查格式',
                    'parsed_hours' => 0
                ];
            }
            
            if ($parsedHours > 24) {
                return [
                    'valid' => false,
                    'message' => '每日工作时间不能超过24小时',
                    'parsed_hours' => $parsedHours
                ];
            }
            
            return [
                'valid' => true,
                'message' => '工作时间配置格式正确',
                'parsed_hours' => $parsedHours
            ];
            
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'message' => '工作时间配置解析异常：' . $e->getMessage(),
                'parsed_hours' => 0
            ];
        }
    }
}

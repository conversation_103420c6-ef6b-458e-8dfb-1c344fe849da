<?php
/**
 * 详细分析前端权限按钮与数据库权限的匹配情况
 */

require_once 'vendor/autoload.php';

echo "=== 详细分析前端权限按钮匹配情况 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取数据库权限数据:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type, sort
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY sort
    ");
    $stmt->execute();
    $dbPermissions = $stmt->fetchAll();
    
    $dbPermissionNames = array_column($dbPermissions, 'name');
    
    echo "  数据库权限总数: " . count($dbPermissions) . " 个\n\n";
    
    echo "2. 手动分析关键前端文件:\n";
    
    // 手动提取的前端权限按钮（基于实际查看的代码）
    $frontendAuthButtons = [
        // Admin.vue - 管理员管理
        [
            'file' => 'Admin.vue',
            'line' => 23,
            'old_auth' => 'system:permission:admin:add',
            'new_auth' => 'system:permission_admin:add',
            'button' => '新增管理员',
            'context' => 'v-auth="\'system:permission:admin:add\'"'
        ],
        
        // Role.vue - 角色管理  
        [
            'file' => 'Role.vue',
            'line' => 20,
            'old_auth' => 'system:permission:role:add',
            'new_auth' => 'system:permission_role:add',
            'button' => '新增角色',
            'context' => 'v-auth="\'system:permission:role:add\'"'
        ],
        
        // Menu.vue - 菜单权限管理
        [
            'file' => 'Menu.vue',
            'line' => 25,
            'old_auth' => 'system:permission_menu:add',
            'new_auth' => 'system:permission_menu:add',
            'button' => '新增菜单',
            'context' => 'v-auth="\'system:permission_menu:add\'"'
        ],
        [
            'file' => 'Menu.vue',
            'line' => 312,
            'old_auth' => 'system:permission_menu:edit',
            'new_auth' => 'system:permission_menu:edit',
            'button' => '编辑菜单',
            'context' => 'v-auth="\'system:permission_menu:edit\'"'
        ],
        [
            'file' => 'Menu.vue',
            'line' => 313,
            'old_auth' => 'system:permission_menu:delete',
            'new_auth' => 'system:permission_menu:delete',
            'button' => '删除菜单',
            'context' => 'v-auth="\'system:permission_menu:delete\'"'
        ],
        
        // Dept.vue - 部门管理
        [
            'file' => 'Dept.vue',
            'line' => 21,
            'old_auth' => 'system:permission:department:add',
            'new_auth' => 'system:permission_department:add',
            'button' => '新增部门',
            'context' => 'v-auth="\'system:permission:department:add\'"'
        ],
    ];
    
    echo "  手动识别的权限按钮: " . count($frontendAuthButtons) . " 个\n\n";
    
    echo "3. 权限匹配分析:\n";
    
    $matchResults = [];
    $needsUpdate = [];
    $missingInDb = [];
    
    foreach ($frontendAuthButtons as $button) {
        $oldAuth = $button['old_auth'];
        $newAuth = $button['new_auth'];
        
        echo "  📁 {$button['file']}:{$button['line']}\n";
        echo "    按钮: {$button['button']}\n";
        echo "    前端权限: {$oldAuth}\n";
        echo "    期望权限: {$newAuth}\n";
        
        // 检查旧格式权限是否在数据库中
        $oldExists = in_array($oldAuth, $dbPermissionNames);
        echo "    旧格式在数据库: " . ($oldExists ? '✅ 存在' : '❌ 不存在') . "\n";
        
        // 检查新格式权限是否在数据库中
        $newExists = in_array($newAuth, $dbPermissionNames);
        echo "    新格式在数据库: " . ($newExists ? '✅ 存在' : '❌ 不存在') . "\n";
        
        if ($oldAuth !== $newAuth) {
            if ($newExists) {
                $needsUpdate[] = $button;
                echo "    状态: 🔧 需要更新前端代码\n";
            } else {
                $missingInDb[] = $button;
                echo "    状态: ⚠️ 数据库缺少新格式权限\n";
            }
        } else {
            echo "    状态: ✅ 格式已正确\n";
        }
        
        echo "\n";
    }
    
    echo "4. 统计结果:\n";
    echo "  需要更新前端代码: " . count($needsUpdate) . " 个\n";
    echo "  数据库缺少权限: " . count($missingInDb) . " 个\n";
    echo "  格式已正确: " . (count($frontendAuthButtons) - count($needsUpdate) - count($missingInDb)) . " 个\n\n";
    
    if (!empty($needsUpdate)) {
        echo "5. 需要更新的前端代码:\n";
        
        $fileGroups = [];
        foreach ($needsUpdate as $update) {
            $fileGroups[$update['file']][] = $update;
        }
        
        foreach ($fileGroups as $file => $updates) {
            echo "  📁 {$file}: " . count($updates) . " 处需要更新\n";
            foreach ($updates as $update) {
                echo "    第{$update['line']}行: {$update['old_auth']} → {$update['new_auth']}\n";
                echo "      替换: v-auth=\"'{$update['old_auth']}'\" → v-auth=\"'{$update['new_auth']}'\"\n";
            }
            echo "\n";
        }
    }
    
    if (!empty($missingInDb)) {
        echo "6. 数据库中缺少的权限:\n";
        foreach ($missingInDb as $missing) {
            echo "  - {$missing['new_auth']} ({$missing['button']})\n";
        }
        echo "\n";
    }
    
    echo "7. 工作量评估:\n";
    
    $totalFiles = count($fileGroups ?? []);
    $totalUpdates = count($needsUpdate);
    $totalMissing = count($missingInDb);
    
    echo "  📁 需要修改的前端文件: {$totalFiles} 个\n";
    echo "  🔧 需要更新的权限按钮: {$totalUpdates} 个\n";
    echo "  ➕ 需要添加的数据库权限: {$totalMissing} 个\n";
    echo "  \n";
    echo "  ⏱️ 工作时间估算:\n";
    echo "    - 添加缺失权限: " . ($totalMissing * 2) . " 分钟\n";
    echo "    - 修改前端代码: " . ($totalFiles * 10 + $totalUpdates * 3) . " 分钟\n";
    echo "    - 功能测试验证: " . ($totalFiles * 15) . " 分钟\n";
    echo "    - 总计时间: " . ($totalMissing * 2 + $totalFiles * 25 + $totalUpdates * 3) . " 分钟\n";
    
    echo "\n8. 实施步骤:\n";
    echo "  步骤1: 在数据库中添加缺失的 {$totalMissing} 个权限\n";
    echo "  步骤2: 为相关角色分配新权限\n";
    echo "  步骤3: 备份需要修改的 {$totalFiles} 个前端文件\n";
    echo "  步骤4: 批量更新 {$totalUpdates} 个权限按钮配置\n";
    echo "  步骤5: 逐页面测试权限控制功能\n";
    echo "  步骤6: 验证按钮显示/隐藏逻辑\n";
    
    echo "\n9. 风险评估:\n";
    echo "  🔴 高风险: 权限配置错误可能导致功能无法访问\n";
    echo "  🟡 中风险: 前端代码修改可能引入语法错误\n";
    echo "  🟢 低风险: 数据库权限添加相对安全\n";
    
    echo "\n10. 建议:\n";
    echo "  1. 先在测试环境完整验证\n";
    echo "  2. 准备回滚方案（备份文件和数据库）\n";
    echo "  3. 分批次实施，每次修改1-2个文件\n";
    echo "  4. 每次修改后立即测试对应功能\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 分析完成 ===\n";

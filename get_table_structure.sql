-- =====================================================
-- 数据库表结构查询脚本
-- 用于获取准确的表结构信息，生成精确的权限测试SQL
-- =====================================================

-- =====================================================
-- 第一步：查看所有表
-- =====================================================
SELECT '=== 数据库中的所有表 ===' as message;
SHOW TABLES;

-- =====================================================
-- 第二步：查看核心权限相关表结构
-- =====================================================

-- 2.1 system_admin 表结构
SELECT '=== system_admin 表结构 ===' as message;
DESCRIBE system_admin;

-- 2.2 system_role 表结构  
SELECT '=== system_role 表结构 ===' as message;
DESCRIBE system_role;

-- 2.3 system_dept 表结构
SELECT '=== system_dept 表结构 ===' as message;
DESCRIBE system_dept;

-- 2.4 system_menu 表结构
SELECT '=== system_menu 表结构 ===' as message;
DESCRIBE system_menu;

-- 2.5 system_admin_role 表结构
SELECT '=== system_admin_role 表结构 ===' as message;
DESCRIBE system_admin_role;

-- 2.6 system_role_menu 表结构
SELECT '=== system_role_menu 表结构 ===' as message;
DESCRIBE system_role_menu;

-- =====================================================
-- 第三步：查看现有数据情况
-- =====================================================

-- 3.1 查看现有租户数据
SELECT '=== 现有租户数据 ===' as message;
SELECT DISTINCT tenant_id, COUNT(*) as count 
FROM system_admin 
GROUP BY tenant_id 
ORDER BY tenant_id;

-- 3.2 查看现有部门数据
SELECT '=== 现有部门数据 ===' as message;
SELECT id, name, parent_id, tenant_id 
FROM system_dept 
ORDER BY tenant_id, parent_id, id 
LIMIT 20;

-- 3.3 查看现有角色数据
SELECT '=== 现有角色数据 ===' as message;
SELECT id, name, data_scope, tenant_id 
FROM system_role 
ORDER BY tenant_id, id 
LIMIT 20;

-- 3.4 查看现有用户数据
SELECT '=== 现有用户数据 ===' as message;
SELECT id, username, real_name, dept_id, tenant_id, status 
FROM system_admin 
ORDER BY tenant_id, id 
LIMIT 20;

-- =====================================================
-- 第四步：查看菜单数据
-- =====================================================

-- 4.1 查看菜单总数
SELECT '=== 菜单数据统计 ===' as message;
SELECT 
    type,
    CASE type 
        WHEN 0 THEN '目录'
        WHEN 1 THEN '菜单' 
        WHEN 2 THEN '按钮'
        ELSE '其他'
    END as type_name,
    COUNT(*) as count
FROM system_menu 
WHERE status = 1 AND deleted_at IS NULL
GROUP BY type
ORDER BY type;

-- 4.2 查看主要菜单
SELECT '=== 主要菜单列表 ===' as message;
SELECT id, parent_id, title, name, type, sort
FROM system_menu 
WHERE status = 1 AND deleted_at IS NULL 
AND (parent_id = 0 OR name LIKE '%system%' OR name LIKE '%crm%')
ORDER BY parent_id, sort, id
LIMIT 30;

-- 4.3 查看权限管理相关菜单
SELECT '=== 权限管理菜单 ===' as message;
SELECT id, parent_id, title, name, type
FROM system_menu 
WHERE status = 1 AND deleted_at IS NULL 
AND (name LIKE '%admin%' OR name LIKE '%role%' OR name LIKE '%dept%' OR name LIKE '%menu%')
ORDER BY parent_id, sort, id;

-- 4.4 查看CRM相关菜单
SELECT '=== CRM相关菜单 ===' as message;
SELECT id, parent_id, title, name, type
FROM system_menu 
WHERE status = 1 AND deleted_at IS NULL 
AND (name LIKE '%crm%' OR name LIKE '%customer%' OR name LIKE '%lead%')
ORDER BY parent_id, sort, id;

-- =====================================================
-- 第五步：检查业务表是否存在
-- =====================================================

-- 5.1 检查CRM相关表
SELECT '=== 检查CRM相关表 ===' as message;
SELECT 
    table_name,
    CASE 
        WHEN table_name LIKE '%crm%' THEN '✅ CRM相关表'
        WHEN table_name LIKE '%customer%' THEN '✅ 客户相关表'
        WHEN table_name LIKE '%lead%' THEN '✅ 线索相关表'
        ELSE '其他表'
    END as table_type
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND (table_name LIKE '%crm%' OR table_name LIKE '%customer%' OR table_name LIKE '%lead%')
ORDER BY table_name;

-- 5.2 检查项目管理相关表
SELECT '=== 检查项目管理相关表 ===' as message;
SELECT 
    table_name,
    '✅ 项目相关表' as table_type
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND (table_name LIKE '%project%' OR table_name LIKE '%task%')
ORDER BY table_name;

-- 5.3 检查其他业务表
SELECT '=== 检查其他业务表 ===' as message;
SELECT 
    table_name,
    CASE 
        WHEN table_name LIKE '%daily%' THEN '✅ 每日报价相关'
        WHEN table_name LIKE '%workflow%' THEN '✅ 工作流相关'
        WHEN table_name LIKE '%tenant%' THEN '✅ 租户相关'
        ELSE '其他业务表'
    END as table_type
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND (table_name LIKE '%daily%' OR table_name LIKE '%workflow%' OR table_name LIKE '%tenant%')
ORDER BY table_name;

-- =====================================================
-- 第六步：获取字段详细信息
-- =====================================================

-- 6.1 检查关键字段是否存在
SELECT '=== 检查关键字段 ===' as message;

-- 检查 tenant_id 字段
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND column_name = 'tenant_id'
AND table_name IN ('system_admin', 'system_role', 'system_dept', 'system_admin_role', 'system_role_menu')
ORDER BY table_name;

-- 检查 data_scope 字段
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND column_name = 'data_scope'
ORDER BY table_name;

-- 检查 deleted_at 字段
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND column_name = 'deleted_at'
AND table_name IN ('system_admin', 'system_role', 'system_dept', 'system_menu')
ORDER BY table_name;

-- =====================================================
-- 第七步：生成建议的ID范围
-- =====================================================

-- 7.1 查看现有ID的最大值
SELECT '=== 现有ID范围 ===' as message;

SELECT 'system_dept' as table_name, COALESCE(MAX(id), 0) as max_id FROM system_dept
UNION ALL
SELECT 'system_role', COALESCE(MAX(id), 0) FROM system_role  
UNION ALL
SELECT 'system_admin', COALESCE(MAX(id), 0) FROM system_admin
UNION ALL
SELECT 'system_menu', COALESCE(MAX(id), 0) FROM system_menu;

-- 7.2 建议的测试ID范围
SELECT '=== 建议的测试ID范围 ===' as message;
SELECT 
    'system_dept' as table_name,
    '建议使用 1001-1010' as suggested_id_range,
    '避免与现有数据冲突' as note
UNION ALL
SELECT 
    'system_role',
    '建议使用 1001-1010',
    '避免与现有数据冲突'
UNION ALL
SELECT 
    'system_admin', 
    '建议使用 1001-1020',
    '避免与现有数据冲突';

SELECT '=== 表结构查询完成 ===' as final_message;
SELECT '=== 请将查询结果提供给我，我将生成准确的测试SQL ===' as instruction;

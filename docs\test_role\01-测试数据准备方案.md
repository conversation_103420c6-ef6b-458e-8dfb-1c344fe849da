# 按钮权限与数据权限测试 - 测试数据准备方案

## 📋 概述

为租户ID=1创建完整的测试环境，包括测试用户、角色、部门数据，用于验证按钮权限和数据权限的正确性。

## 🏢 测试组织架构设计

### 部门结构
```
总公司 (tenant_id=1)
├── 销售部 (dept_id=101)
│   ├── 销售一组 (dept_id=102)
│   └── 销售二组 (dept_id=103)
├── 技术部 (dept_id=104)
│   ├── 前端组 (dept_id=105)
│   └── 后端组 (dept_id=106)
└── 财务部 (dept_id=107)
```

### 角色权限设计
1. **超级管理员角色** (role_id=101) - 数据权限：全部数据(1)
2. **部门经理角色** (role_id=102) - 数据权限：本部门及以下(3)
3. **组长角色** (role_id=103) - 数据权限：本部门(2)
4. **普通员工角色** (role_id=104) - 数据权限：仅本人(4)
5. **自定义权限角色** (role_id=105) - 数据权限：自定义(5)

### 测试用户设计
1. **tenant_admin** - 租户超级管理员 (销售部)
2. **sales_manager** - 销售部经理 (销售部)
3. **sales_leader1** - 销售一组组长 (销售一组)
4. **sales_staff1** - 销售一组员工 (销售一组)
5. **sales_staff2** - 销售二组员工 (销售二组)
6. **tech_manager** - 技术部经理 (技术部)
7. **tech_staff** - 技术部员工 (前端组)
8. **finance_staff** - 财务部员工 (财务部)
9. **custom_user** - 自定义权限用户 (技术部)

## 📝 SQL脚本

### 1. 创建测试部门
```sql
-- 清理租户ID=1的测试数据（如果存在）
DELETE FROM system_dept WHERE tenant_id = 1;
DELETE FROM system_admin WHERE tenant_id = 1;
DELETE FROM system_role WHERE tenant_id = 1;
DELETE FROM system_admin_role WHERE tenant_id = 1;
DELETE FROM system_role_menu WHERE tenant_id = 1;

-- 创建部门结构
INSERT INTO `system_dept` (`id`, `parent_id`, `name`, `code`, `leader_name`, `phone`, `email`, `sort`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `tenant_id`) VALUES
(101, 0, '总公司', 'HQ', '张总', '13800000001', '<EMAIL>', 1, 1, '租户1总公司', 1, NOW(), NOW(), 1),
(102, 101, '销售部', 'SALES', '李经理', '13800000002', '<EMAIL>', 1, 1, '销售部门', 1, NOW(), NOW(), 1),
(103, 102, '销售一组', 'SALES1', '王组长', '13800000003', '<EMAIL>', 1, 1, '销售一组', 1, NOW(), NOW(), 1),
(104, 102, '销售二组', 'SALES2', '赵组长', '13800000004', '<EMAIL>', 2, 1, '销售二组', 1, NOW(), NOW(), 1),
(105, 101, '技术部', 'TECH', '刘经理', '13800000005', '<EMAIL>', 2, 1, '技术部门', 1, NOW(), NOW(), 1),
(106, 105, '前端组', 'FRONTEND', '陈组长', '13800000006', '<EMAIL>', 1, 1, '前端开发组', 1, NOW(), NOW(), 1),
(107, 105, '后端组', 'BACKEND', '周组长', '13800000007', '<EMAIL>', 2, 1, '后端开发组', 1, NOW(), NOW(), 1),
(108, 101, '财务部', 'FINANCE', '孙经理', '13800000008', '<EMAIL>', 3, 1, '财务部门', 1, NOW(), NOW(), 1);
```

### 2. 创建测试角色
```sql
INSERT INTO `system_role` (`id`, `name`, `sort`, `data_scope`, `data_scope_dept_ids`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `tenant_id`) VALUES
(101, '租户超级管理员', 1, 1, '[]', 1, '租户内全部数据权限', 1, NOW(), NOW(), 1),
(102, '部门经理', 2, 3, '[]', 1, '本部门及以下数据权限', 1, NOW(), NOW(), 1),
(103, '组长', 3, 2, '[]', 1, '本部门数据权限', 1, NOW(), NOW(), 1),
(104, '普通员工', 4, 4, '[]', 1, '仅本人数据权限', 1, NOW(), NOW(), 1),
(105, '自定义权限', 5, 5, '[102,105]', 1, '自定义部门权限：销售部+技术部', 1, NOW(), NOW(), 1);
```

### 3. 创建测试用户
```sql
INSERT INTO `system_admin` (`id`, `username`, `password`, `salt`, `real_name`, `avatar`, `gender`, `email`, `mobile`, `is_super_admin`, `dept_id`, `post_ids`, `status`, `remark`, `creator_id`, `created_at`, `updated_at`, `tenant_id`) VALUES
(201, 'tenant_admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '租户管理员', '', 1, '<EMAIL>', '13900000001', 0, 102, '', 1, '租户1超级管理员', 1, NOW(), NOW(), 1),
(202, 'sales_manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '李经理', '', 1, '<EMAIL>', '13900000002', 0, 102, '', 1, '销售部经理', 1, NOW(), NOW(), 1),
(203, 'sales_leader1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '王组长', '', 1, '<EMAIL>', '13900000003', 0, 103, '', 1, '销售一组组长', 1, NOW(), NOW(), 1),
(204, 'sales_staff1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '张员工', '', 2, '<EMAIL>', '13900000004', 0, 103, '', 1, '销售一组员工', 1, NOW(), NOW(), 1),
(205, 'sales_staff2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '赵员工', '', 2, '<EMAIL>', '13900000005', 0, 104, '', 1, '销售二组员工', 1, NOW(), NOW(), 1),
(206, 'tech_manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '刘经理', '', 1, '<EMAIL>', '13900000006', 0, 105, '', 1, '技术部经理', 1, NOW(), NOW(), 1),
(207, 'tech_staff', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '陈开发', '', 1, '<EMAIL>', '13900000007', 0, 106, '', 1, '前端开发', 1, NOW(), NOW(), 1),
(208, 'finance_staff', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '孙会计', '', 2, '<EMAIL>', '13900000008', 0, 108, '', 1, '财务人员', 1, NOW(), NOW(), 1),
(209, 'custom_user', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'salt123', '周测试', '', 1, '<EMAIL>', '13900000009', 0, 105, '', 1, '自定义权限测试用户', 1, NOW(), NOW(), 1);
```

### 4. 分配用户角色
```sql
INSERT INTO `system_admin_role` (`admin_id`, `role_id`, `creator_id`, `created_at`, `updated_at`, `tenant_id`) VALUES
(201, 101, 1, NOW(), NOW(), 1), -- 租户管理员 -> 超级管理员
(202, 102, 1, NOW(), NOW(), 1), -- 销售经理 -> 部门经理
(203, 103, 1, NOW(), NOW(), 1), -- 销售组长 -> 组长
(204, 104, 1, NOW(), NOW(), 1), -- 销售员工1 -> 普通员工
(205, 104, 1, NOW(), NOW(), 1), -- 销售员工2 -> 普通员工
(206, 102, 1, NOW(), NOW(), 1), -- 技术经理 -> 部门经理
(207, 104, 1, NOW(), NOW(), 1), -- 技术员工 -> 普通员工
(208, 104, 1, NOW(), NOW(), 1), -- 财务员工 -> 普通员工
(209, 105, 1, NOW(), NOW(), 1); -- 自定义用户 -> 自定义权限
```

## 🔧 执行步骤

1. **备份现有数据**（可选）
2. **执行SQL脚本**创建测试数据
3. **验证数据创建**是否正确
4. **配置角色菜单权限**（下一步详细说明）

## ✅ 验证检查

执行以下查询验证数据创建是否正确：

```sql
-- 检查部门创建
SELECT id, name, parent_id, tenant_id FROM system_dept WHERE tenant_id = 1 ORDER BY id;

-- 检查角色创建
SELECT id, name, data_scope, tenant_id FROM system_role WHERE tenant_id = 1 ORDER BY id;

-- 检查用户创建
SELECT id, username, real_name, dept_id, tenant_id FROM system_admin WHERE tenant_id = 1 ORDER BY id;

-- 检查用户角色关联
SELECT ar.admin_id, a.username, ar.role_id, r.name as role_name 
FROM system_admin_role ar 
LEFT JOIN system_admin a ON ar.admin_id = a.id 
LEFT JOIN system_role r ON ar.role_id = r.id 
WHERE ar.tenant_id = 1 ORDER BY ar.admin_id;
```

## 📌 注意事项

1. **密码说明**：所有测试用户密码为 `password`（已加密）
2. **租户隔离**：所有数据都设置了 `tenant_id = 1`
3. **数据权限**：每个角色都配置了不同的数据权限范围
4. **部门层级**：设计了3级部门结构用于测试层级权限

下一步将配置角色的菜单权限，为按钮权限测试做准备。

# 数据权限测试计划

## 📋 概述

本文档详细说明数据权限测试计划，验证系统的5种数据权限范围（全部数据、本部门、本部门及以下、仅本人、自定义）在实际业务场景中的正确性。

## 🎯 数据权限类型说明

| 权限类型 | data_scope值 | 说明 | 测试角色 |
|----------|--------------|------|----------|
| 全部数据 | 1 | 可以查看租户内所有数据 | 租户超级管理员 |
| 本部门 | 2 | 只能查看本部门的数据 | 组长 |
| 本部门及以下 | 3 | 可以查看本部门及下级部门数据 | 部门经理 |
| 仅本人 | 4 | 只能查看自己创建的数据 | 普通员工 |
| 自定义 | 5 | 可以查看指定部门的数据 | 自定义权限用户 |

## 🏢 测试数据结构

### 部门层级关系
```
总公司(101) - tenant_admin
├── 销售部(102) - sales_manager
│   ├── 销售一组(103) - sales_leader1, sales_staff1
│   └── 销售二组(104) - sales_staff2
├── 技术部(105) - tech_manager, custom_user
│   ├── 前端组(106) - tech_staff
│   └── 后端组(107)
└── 财务部(108) - finance_staff
```

### 测试数据创建脚本

#### 1. CRM客户测试数据
```sql
-- 创建不同部门用户的客户数据
INSERT INTO `crm_customer` (`id`, `name`, `phone`, `email`, `source`, `industry`, `level`, `status`, `remark`, `creator_id`, `dept_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- 销售部经理创建的客户
(1001, '客户A-销售部经理', '13800001001', '<EMAIL>', 1, '互联网', 1, 1, '销售部经理创建', 202, 102, 1, NOW(), NOW()),
(1002, '客户B-销售部经理', '13800001002', '<EMAIL>', 2, '制造业', 2, 1, '销售部经理创建', 202, 102, 1, NOW(), NOW()),

-- 销售一组组长创建的客户
(1003, '客户C-销售一组长', '13800001003', '<EMAIL>', 1, '金融', 1, 1, '销售一组长创建', 203, 103, 1, NOW(), NOW()),
(1004, '客户D-销售一组长', '13800001004', '<EMAIL>', 3, '教育', 2, 1, '销售一组长创建', 203, 103, 1, NOW(), NOW()),

-- 销售一组员工创建的客户
(1005, '客户E-销售一组员工', '13800001005', '<EMAIL>', 1, '医疗', 1, 1, '销售一组员工创建', 204, 103, 1, NOW(), NOW()),
(1006, '客户F-销售一组员工', '13800001006', '<EMAIL>', 2, '零售', 3, 1, '销售一组员工创建', 204, 103, 1, NOW(), NOW()),

-- 销售二组员工创建的客户
(1007, '客户G-销售二组员工', '13800001007', '<EMAIL>', 1, '物流', 1, 1, '销售二组员工创建', 205, 104, 1, NOW(), NOW()),
(1008, '客户H-销售二组员工', '13800001008', '<EMAIL>', 3, '房地产', 2, 1, '销售二组员工创建', 205, 104, 1, NOW(), NOW()),

-- 技术部经理创建的客户
(1009, '客户I-技术部经理', '13800001009', '<EMAIL>', 2, '软件', 1, 1, '技术部经理创建', 206, 105, 1, NOW(), NOW()),

-- 技术部员工创建的客户
(1010, '客户J-技术部员工', '13800001010', '<EMAIL>', 1, '硬件', 2, 1, '技术部员工创建', 207, 106, 1, NOW(), NOW()),

-- 财务部员工创建的客户
(1011, '客户K-财务部员工', '13800001011', '<EMAIL>', 2, '咨询', 1, 1, '财务部员工创建', 208, 108, 1, NOW(), NOW()),

-- 自定义权限用户创建的客户
(1012, '客户L-自定义权限', '13800001012', '<EMAIL>', 1, '广告', 3, 1, '自定义权限用户创建', 209, 105, 1, NOW(), NOW());
```

#### 2. CRM线索测试数据
```sql
INSERT INTO `crm_lead` (`id`, `name`, `phone`, `email`, `source`, `industry`, `status`, `remark`, `creator_id`, `dept_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- 不同用户创建的线索数据
(2001, '线索A-销售经理', '13900001001', '<EMAIL>', 1, '互联网', 1, '销售经理创建', 202, 102, 1, NOW(), NOW()),
(2002, '线索B-销售组长', '13900001002', '<EMAIL>', 2, '制造业', 1, '销售组长创建', 203, 103, 1, NOW(), NOW()),
(2003, '线索C-销售员工1', '13900001003', '<EMAIL>', 1, '金融', 1, '销售员工1创建', 204, 103, 1, NOW(), NOW()),
(2004, '线索D-销售员工2', '13900001004', '<EMAIL>', 3, '教育', 1, '销售员工2创建', 205, 104, 1, NOW(), NOW()),
(2005, '线索E-技术经理', '13900001005', '<EMAIL>', 2, '软件', 1, '技术经理创建', 206, 105, 1, NOW(), NOW()),
(2006, '线索F-技术员工', '13900001006', '<EMAIL>', 1, '硬件', 1, '技术员工创建', 207, 106, 1, NOW(), NOW()),
(2007, '线索G-财务员工', '13900001007', '<EMAIL>', 2, '咨询', 1, '财务员工创建', 208, 108, 1, NOW(), NOW()),
(2008, '线索H-自定义权限', '13900001008', '<EMAIL>', 1, '广告', 1, '自定义权限创建', 209, 105, 1, NOW(), NOW());
```

## 🧪 数据权限测试用例

### 测试用例1：全部数据权限（data_scope=1）
**测试角色**：租户超级管理员 (tenant_admin)
**预期结果**：能查看租户内所有数据

**验证SQL**：
```sql
-- 模拟租户超级管理员查询客户数据
SELECT c.id, c.name, c.creator_id, a.real_name as creator_name, d.name as dept_name
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE c.tenant_id = 1
ORDER BY c.id;
```

**预期结果**：应该返回所有12条客户记录（1001-1012）

### 测试用例2：本部门及以下权限（data_scope=3）
**测试角色**：部门经理 (sales_manager, tech_manager)

#### 2.1 销售部经理测试
**预期结果**：能查看销售部(102)、销售一组(103)、销售二组(104)的数据

**验证SQL**：
```sql
-- 模拟销售部经理(202)查询客户数据
-- 应该能看到：销售部(102) + 销售一组(103) + 销售二组(104)的数据
SELECT c.id, c.name, c.creator_id, a.real_name as creator_name, d.name as dept_name
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE c.tenant_id = 1 
  AND a.dept_id IN (102, 103, 104)  -- 本部门及以下
ORDER BY c.id;
```

**预期结果**：应该返回客户1001-1008（8条记录）

#### 2.2 技术部经理测试
**预期结果**：能查看技术部(105)、前端组(106)、后端组(107)的数据

**验证SQL**：
```sql
-- 模拟技术部经理(206)查询客户数据
SELECT c.id, c.name, c.creator_id, a.real_name as creator_name, d.name as dept_name
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE c.tenant_id = 1 
  AND a.dept_id IN (105, 106, 107)  -- 本部门及以下
ORDER BY c.id;
```

**预期结果**：应该返回客户1009-1010, 1012（3条记录）

### 测试用例3：本部门权限（data_scope=2）
**测试角色**：组长 (sales_leader1)
**预期结果**：只能查看销售一组(103)的数据

**验证SQL**：
```sql
-- 模拟销售一组长(203)查询客户数据
SELECT c.id, c.name, c.creator_id, a.real_name as creator_name, d.name as dept_name
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE c.tenant_id = 1 
  AND a.dept_id = 103  -- 仅本部门
ORDER BY c.id;
```

**预期结果**：应该返回客户1003-1006（4条记录）

### 测试用例4：仅本人权限（data_scope=4）
**测试角色**：普通员工 (sales_staff1, sales_staff2, tech_staff, finance_staff)

#### 4.1 销售一组员工测试
**预期结果**：只能查看自己创建的数据

**验证SQL**：
```sql
-- 模拟销售一组员工(204)查询客户数据
SELECT c.id, c.name, c.creator_id, a.real_name as creator_name, d.name as dept_name
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE c.tenant_id = 1 
  AND c.creator_id = 204  -- 仅本人创建
ORDER BY c.id;
```

**预期结果**：应该返回客户1005-1006（2条记录）

### 测试用例5：自定义权限（data_scope=5）
**测试角色**：自定义权限用户 (custom_user)
**权限配置**：销售部(102) + 技术部(105)
**预期结果**：能查看指定部门的数据

**验证SQL**：
```sql
-- 模拟自定义权限用户(209)查询客户数据
-- 自定义部门：[102,105]
SELECT c.id, c.name, c.creator_id, a.real_name as creator_name, d.name as dept_name
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE c.tenant_id = 1 
  AND a.dept_id IN (102, 105)  -- 自定义部门权限
ORDER BY c.id;
```

**预期结果**：应该返回客户1001-1002, 1009, 1012（4条记录）

## 📊 测试结果记录表

| 测试用例 | 测试账号 | 预期记录数 | 实际记录数 | 测试结果 | 备注 |
|----------|----------|------------|------------|----------|------|
| 全部数据权限 | tenant_admin | 12 |  | ⭕ |  |
| 销售部经理权限 | sales_manager | 8 |  | ⭕ |  |
| 技术部经理权限 | tech_manager | 3 |  | ⭕ |  |
| 销售组长权限 | sales_leader1 | 4 |  | ⭕ |  |
| 销售员工权限 | sales_staff1 | 2 |  | ⭕ |  |
| 技术员工权限 | tech_staff | 1 |  | ⭕ |  |
| 财务员工权限 | finance_staff | 1 |  | ⭕ |  |
| 自定义权限 | custom_user | 4 |  | ⭕ |  |

## 🔍 测试执行步骤

1. **创建测试数据**：执行上述客户和线索数据创建脚本
2. **登录测试账号**：使用不同角色的测试账号登录系统
3. **查询数据**：在客户管理、线索管理等模块查看数据列表
4. **验证结果**：对比实际显示的数据与预期结果
5. **记录测试结果**：填写测试结果记录表
6. **问题跟踪**：记录发现的数据权限问题

## ✅ 验证检查点

- [ ] 全部数据权限：能看到租户内所有数据
- [ ] 本部门及以下权限：能看到本部门及下级部门数据
- [ ] 本部门权限：只能看到本部门数据
- [ ] 仅本人权限：只能看到自己创建的数据
- [ ] 自定义权限：只能看到指定部门数据
- [ ] 跨租户隔离：不能看到其他租户数据
- [ ] 数据权限与按钮权限联动正确

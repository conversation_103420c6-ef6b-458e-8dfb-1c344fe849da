<template>
  <div class="media-selector-test">
    <el-card>
      <template #header>
        <span>MediaSelector 组件测试</span>
      </template>
      
      <div class="test-controls">
        <el-button type="primary" @click="openMediaSelector">
          打开媒体选择器
        </el-button>
        
        <el-button type="info" @click="checkComponentStatus">
          检查组件状态
        </el-button>
        
        <el-button type="success" @click="testAPI">
          测试API
        </el-button>
      </div>
      
      <div class="status-info">
        <h4>组件状态:</h4>
        <p>对话框可见: {{ mediaSelectorVisible }}</p>
        <p>已选择媒体: {{ selectedMedia.length }} 个</p>
        <p>API状态: {{ apiStatus }}</p>
      </div>
      
      <div class="selected-media" v-if="selectedMedia.length > 0">
        <h4>已选择的媒体:</h4>
        <el-tag
          v-for="media in selectedMedia"
          :key="media.id"
          type="success"
          style="margin-right: 8px; margin-bottom: 4px;"
        >
          {{ media.name }}
        </el-tag>
      </div>
    </el-card>

    <!-- MediaSelector 组件 -->
    <MediaSelector
      v-model="mediaSelectorVisible"
      :multiple="true"
      @confirm="handleMediaSelect"
      @close="handleMediaClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import MediaSelector from '@/components/custom/MediaSelector/index.vue'
import { AttachmentApi } from '@/api/attachmentApi'
import { ApiStatus } from '@/utils/http/status'

// 响应式数据
const mediaSelectorVisible = ref(false)
const selectedMedia = ref<any[]>([])
const apiStatus = ref('未测试')

// 打开媒体选择器
const openMediaSelector = () => {
  console.log('打开媒体选择器...')
  mediaSelectorVisible.value = true
  ElMessage.info('正在打开媒体选择器')
}

// 媒体选择回调
const handleMediaSelect = (selected: any[]) => {
  console.log('媒体选择回调:', selected)
  selectedMedia.value = selected
  ElMessage.success(`选择了 ${selected.length} 个媒体文件`)
}

// 媒体选择器关闭回调
const handleMediaClose = () => {
  console.log('媒体选择器关闭')
  ElMessage.info('媒体选择器已关闭')
}

// 检查组件状态
const checkComponentStatus = () => {
  console.log('组件状态检查:')
  console.log('- mediaSelectorVisible:', mediaSelectorVisible.value)
  console.log('- selectedMedia:', selectedMedia.value)
  
  ElMessage.info('组件状态已输出到控制台')
}

// 测试API
const testAPI = async () => {
  try {
    apiStatus.value = '测试中...'
    
    // 测试文件列表API
    const listRes = await AttachmentApi.list({ page: 1, limit: 5 })
    if (listRes.code === ApiStatus.success) {
      apiStatus.value = `API正常 - 找到 ${listRes.data.total} 个文件`
      ElMessage.success('API测试成功')
    } else {
      apiStatus.value = `API错误 - ${listRes.message}`
      ElMessage.error('API测试失败')
    }
  } catch (error: any) {
    apiStatus.value = `API异常 - ${error.message}`
    ElMessage.error('API测试异常')
    console.error('API测试错误:', error)
  }
}
</script>

<style scoped>
.media-selector-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-controls {
  margin-bottom: 20px;
}

.test-controls .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.status-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.status-info h4 {
  margin-top: 0;
  color: #303133;
}

.status-info p {
  margin: 5px 0;
  color: #606266;
}

.selected-media {
  background: #f0f9ff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.selected-media h4 {
  margin-top: 0;
  color: #409eff;
}
</style>

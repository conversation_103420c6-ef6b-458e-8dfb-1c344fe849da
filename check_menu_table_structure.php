<?php
/**
 * 检查菜单表结构和当前ID分布
 */

require_once 'vendor/autoload.php';

echo "=== 检查菜单表结构 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. system_menu表结构:\n";
    
    $stmt = $pdo->prepare("DESCRIBE system_menu");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $column) {
        echo "  {$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Key']} - {$column['Default']}\n";
    }
    
    echo "\n2. 当前ID分布:\n";
    
    $stmt = $pdo->prepare("
        SELECT MIN(id) as min_id, MAX(id) as max_id, COUNT(*) as total_count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
    ");
    $stmt->execute();
    $idStats = $stmt->fetch();
    
    echo "  最小ID: {$idStats['min_id']}\n";
    echo "  最大ID: {$idStats['max_id']}\n";
    echo "  总数量: {$idStats['total_count']}\n";
    
    echo "\n3. ID分布详情:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $allMenus = $stmt->fetchAll();
    
    $idRanges = [
        '1-100' => 0,
        '101-1000' => 0,
        '1001-3000' => 0,
        '3000+' => 0
    ];
    
    foreach ($allMenus as $menu) {
        $id = $menu['id'];
        if ($id <= 100) {
            $idRanges['1-100']++;
        } elseif ($id <= 1000) {
            $idRanges['101-1000']++;
        } elseif ($id <= 3000) {
            $idRanges['1001-3000']++;
        } else {
            $idRanges['3000+']++;
        }
    }
    
    foreach ($idRanges as $range => $count) {
        echo "  {$range}: {$count} 个\n";
    }
    
    echo "\n4. 按模块分组的ID分布:\n";
    
    $moduleGroups = [];
    foreach ($allMenus as $menu) {
        $parts = explode(':', $menu['name']);
        $module = $parts[0] ?? 'unknown';
        
        if (!isset($moduleGroups[$module])) {
            $moduleGroups[$module] = [];
        }
        $moduleGroups[$module][] = $menu;
    }
    
    foreach ($moduleGroups as $module => $menus) {
        $ids = array_column($menus, 'id');
        $minId = min($ids);
        $maxId = max($ids);
        $count = count($menus);
        
        echo "  {$module}: {$count}个 (ID: {$minId}-{$maxId})\n";
    }
    
    echo "\n5. 建议的ID重新分配方案:\n";
    echo "  考虑到当前最大ID是 {$idStats['max_id']}，建议使用更大的ID范围:\n";
    echo "  - 系统管理: 1000-1099\n";
    echo "  - CRM模块: 1100-1199\n";
    echo "  - 项目管理: 1200-1249\n";
    echo "  - 工作流: 1250-1299\n";
    echo "  - 通知: 1300-1319\n";
    echo "  - 办公: 1320-1339\n";
    echo "  - 每日报价: 1340-1359\n";
    echo "  - 库存管理: 1360-1379\n";
    echo "  - 其他: 1380+\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";

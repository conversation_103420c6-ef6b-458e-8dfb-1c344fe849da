<?php

namespace app\system\controller;

use app\common\core\base\BaseAdminController;
use app\common\lib\upload\UploadService;
use app\system\service\ConfigService;
use app\system\service\AttachmentService;
use think\response\Json;

class UploadController extends BaseAdminController
{
	
	/**
	 * 上传文件（兼容性方法，推荐使用AttachmentController）
	 * @deprecated 建议使用 AttachmentController::upload 方法
	 */
	public function index(): Json
	{
		$file     = $this->request->file('file');
		$storage  = $this->request->param('storage', 'local');
		$cateId   = $this->request->param('cate_id', 0, 'intval');

		if (empty($file)) {
			return $this->error('请选择文件');
		}

		try {
			// 使用新的AttachmentService
			$attachmentService = AttachmentService::getInstance();

			$fileInfo = [
				'name'     => $file->getOriginalName(),
				'tmp_name' => $file->getPathname(),
				'type'     => $file->getMime(),
				'size'     => $file->getSize(),
			];

			$result = $attachmentService->uploadFile($fileInfo, $storage, $cateId);

			// 保持向后兼容的响应格式
			if ($result) {
				$compatibleResult = [
					'id' => $result['id'],
					'name' => $result['name'],
					'path' => $result['path'],
					'url' => $result['url'],
					'size' => $result['size'],
					'extension' => $result['extension'],
					'mime_type' => $result['mime_type'],
					// 兼容旧字段名
					'real_name' => $result['display_name'],
					'storage' => $storage,
					'cate_id' => $cateId,
					'is_duplicate' => $result['is_duplicate'] ?? false
				];

				return $this->success('上传成功', $compatibleResult);
			}

			return $this->error('上传失败');
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}

	/**
	 * API迁移提示
	 */
	public function migrationNotice(): Json
	{
		return $this->success('API迁移提示', [
			'message' => '上传相关API已迁移到AttachmentController',
			'new_endpoints' => [
				'upload' => '/system/attachment/upload',
				'list' => '/system/attachment/index',
				'delete' => '/system/attachment/delete',
				'move' => '/system/attachment/move/{id}',
				'rename' => '/system/attachment/rename/{id}',
				'copy' => '/system/attachment/copy/{id}',
				'stats' => '/system/attachment/stats',
				'permissions' => '/system/attachment/permissions',
				'download' => '/system/attachment/download/{id}'
			],
			'deprecated_endpoints' => [
				'/system/upload' => '建议使用 /system/attachment/upload'
			],
			'migration_guide' => '请更新前端代码以使用新的API接口，旧接口将在未来版本中移除'
		]);
	}

	public function getUploadConfig(): Json
	{
		$uploadInfo = ConfigService::getInstance()
		                           ->getInfo('upload');
		$type       = $uploadInfo['upload_allow_type'] ?? 'local';
		$data       = [
			'upload_allow_type' => $type,
			'upload_allow_ext'  => $uploadInfo['upload_allow_ext'] ?? '',
			'upload_allow_mime' => generateCategoryMap($uploadInfo['upload_allow_ext']),
			'upload_allow_size' => intval($uploadInfo['upload_allow_size'] ?? 2),
			'max_file_count'    => intval($uploadInfo['max_file_count'] ?? 5),
			'domain'            => '',
		];
		
		$typeArr = [
			'qnoss',
			'alioss',
			'txoss'
		];
		if (in_array($type, $typeArr)) {
			$data['domain'] = $uploadInfo[$type . '_domain'] ?? '';
		}
		
		return $this->success('获取成功', $data);
	}
	
	/**
	 * 获取上传Token
	 */
	public function getUploadToken(): Json
	{
		$storage    = $this->request->param('storage/s');  // todo 后续可支持租户独立配置；
		$storageArr = [
			'alioss',
			'txoss',
			'qnoss'
		];
		if (!in_array($storage, $storageArr)) {
			return $this->error('暂不支持该存储方式');
		}
		$cateId   = $this->request->param('cate_id', 0, 'intval');
		$tenantId = $this->tenantId;
		
		$service = new UploadService();
		$result  = $service->getUploadToken($storage, $tenantId, ['cate_id' => $cateId]);
		
		return $this->success('获取成功', $result);
	}
	
	
	/**
	 * todo 需放在不需要权限的接口中
	 * 七牛云回调
	 */
	public function qiniuCallback()
	{
		try {
			$service = new UploadService();
			$result  = $service->handleCallback('qiniu', $this->request->post(), 0);
			
			return json([
				'ret'  => 'success',
				'data' => $result
			]);
		}
		catch (\Exception $e) {
			return json([
				'ret'   => 'fail',
				'error' => $e->getMessage()
			]);
		}
	}
	
	/**
	 * 阿里云回调
	 */
	public function aliyunCallback()
	{
		try {
			$service = new UploadService();
			$result  = $service->handleCallback('aliyun', $this->request->post(), 0);
			
			return json([
				'Status' => 'Ok',
				'data'   => $result
			]);
		}
		catch (\Exception $e) {
			return json([
				'Status'  => 'Error',
				'message' => $e->getMessage()
			]);
		}
	}
	
}
# 测试方案文档

**版本**: v1.0  
**更新日期**: 2025-01-31  
**文档状态**: 草稿

---

## 🎯 测试目标

### 测试范围
1. **功能测试**：验证所有功能需求正确实现
2. **性能测试**：验证系统性能满足要求
3. **安全测试**：验证权限控制和数据安全
4. **兼容性测试**：验证与现有系统的兼容性
5. **压力测试**：验证系统在高负载下的稳定性

### 测试环境
- **开发环境**：用于开发阶段的功能验证
- **测试环境**：用于完整的系统测试
- **预生产环境**：用于上线前的最终验证

---

## 🧪 单元测试

### 测试覆盖率要求
- **整体覆盖率**：>80%
- **核心业务逻辑**：>95%
- **关键算法**：100%

### 模型层测试

#### AttachmentModel 测试
```php
class AttachmentModelTest extends TestCase
{
    public function testCreateAttachment()
    {
        $data = [
            'name' => 'test_file.jpg',
            'path' => '/uploads/test/test_file.jpg',
            'extension' => 'jpg',
            'size' => 1024,
            'mime_type' => 'image/jpeg',
            'storage' => 'local',
            'file_md5' => 'abc123def456',
            'ref_count' => 1
        ];
        
        $attachment = AttachmentModel::create($data);
        
        $this->assertInstanceOf(AttachmentModel::class, $attachment);
        $this->assertEquals('test_file.jpg', $attachment->name);
        $this->assertEquals(1, $attachment->ref_count);
    }
    
    public function testFindByMd5()
    {
        // 测试通过MD5查找文件
    }
    
    public function testRefCountIncrement()
    {
        // 测试引用计数增加
    }
}
```

#### AttachmentUserModel 测试
```php
class AttachmentUserModelTest extends TestCase
{
    public function testCreateUserFileAssociation()
    {
        // 测试创建用户文件关联
    }
    
    public function testUserFilePermission()
    {
        // 测试用户文件权限
    }
    
    public function testSoftDelete()
    {
        // 测试软删除功能
    }
}
```

### 服务层测试

#### AttachmentService 测试
```php
class AttachmentServiceTest extends TestCase
{
    public function testFileDeduplication()
    {
        // 测试文件去重功能
        $file1 = $this->createMockFile('test.jpg', 'abc123');
        $file2 = $this->createMockFile('test_copy.jpg', 'abc123');
        
        $result1 = $this->service->uploadFile($file1, 'local', 0, 0);
        $result2 = $this->service->uploadFile($file2, 'local', 0, 0);
        
        // 验证物理文件只有一个
        $this->assertEquals($result1['attachment_id'], $result2['attachment_id']);
    }
    
    public function testUserFileIsolation()
    {
        // 测试用户文件隔离
    }
    
    public function testConcurrentUpload()
    {
        // 测试并发上传
    }
}
```

#### UploadService 测试
```php
class UploadServiceTest extends TestCase
{
    public function testLocalUpload()
    {
        // 测试本地上传
    }
    
    public function testCloudUpload()
    {
        // 测试云存储上传
    }
    
    public function testUploadCallback()
    {
        // 测试上传回调处理
    }
}
```

---

## 🔧 功能测试

### 文件上传功能测试

#### 测试用例1：本地文件上传
**测试目标**：验证本地文件上传功能正常
**测试步骤**：
1. 准备测试文件（不同格式、大小）
2. 调用上传接口
3. 验证文件保存成功
4. 验证数据库记录正确

**预期结果**：
- 文件成功上传到指定目录
- 数据库中创建正确的记录
- 返回正确的文件信息

#### 测试用例2：云存储文件上传
**测试目标**：验证云存储上传功能正常
**测试步骤**：
1. 获取上传Token
2. 模拟前端直传到云存储
3. 触发上传回调
4. 验证文件记录创建

**预期结果**：
- Token获取成功
- 回调处理正确
- 文件记录创建成功

#### 测试用例3：文件去重测试
**测试目标**：验证文件去重功能正常
**测试步骤**：
1. 上传相同内容的文件（不同用户）
2. 验证物理文件只有一份
3. 验证用户关联记录正确
4. 验证引用计数正确

**预期结果**：
- 物理文件只存储一份
- 每个用户都有独立的关联记录
- 引用计数正确

### 文件管理功能测试

#### 测试用例4：文件列表查询
**测试目标**：验证文件列表查询功能
**测试步骤**：
1. 创建测试数据（多用户、多租户）
2. 以不同用户身份查询文件列表
3. 验证权限隔离正确
4. 验证分页、搜索、筛选功能

**预期结果**：
- 用户只能看到自己的文件
- 分页功能正常
- 搜索筛选准确

#### 测试用例5：文件删除功能
**测试目标**：验证文件删除功能正常
**测试步骤**：
1. 创建被多用户引用的文件
2. 删除其中一个用户的关联
3. 验证引用计数减少
4. 删除最后一个关联
5. 验证物理文件被删除

**预期结果**：
- 用户关联正确删除
- 引用计数正确维护
- 无引用时物理文件被删除

---

## ⚡ 性能测试

### 测试指标
- **响应时间**：API接口响应时间
- **吞吐量**：每秒处理请求数
- **并发性能**：并发用户数支持
- **资源使用**：CPU、内存、磁盘使用率

### 性能测试用例

#### 测试用例6：文件上传性能
**测试目标**：验证文件上传性能满足要求
**测试条件**：
- 并发用户：50个
- 文件大小：1MB-10MB
- 测试时长：10分钟

**性能要求**：
- 平均响应时间：<2秒
- 95%响应时间：<5秒
- 错误率：<1%

#### 测试用例7：文件列表查询性能
**测试目标**：验证文件列表查询性能
**测试条件**：
- 数据量：10万条文件记录
- 并发用户：100个
- 查询类型：分页、搜索、筛选

**性能要求**：
- 平均响应时间：<500ms
- 95%响应时间：<1秒
- 数据库连接池：正常

#### 测试用例8：文件去重性能
**测试目标**：验证文件去重算法性能
**测试条件**：
- 文件数量：1万个文件
- 重复率：50%
- 并发上传：20个

**性能要求**：
- 去重命中率：>95%
- 去重查询时间：<100ms
- 无死锁和竞态条件

---

## 🔒 安全测试

### 权限控制测试

#### 测试用例9：用户文件隔离
**测试目标**：验证用户只能访问自己的文件
**测试步骤**：
1. 创建多个用户和文件
2. 尝试跨用户访问文件
3. 验证权限拒绝

**预期结果**：
- 跨用户访问被拒绝
- 返回403权限错误
- 日志记录异常访问

#### 测试用例10：租户数据隔离
**测试目标**：验证租户间数据完全隔离
**测试步骤**：
1. 创建多租户测试数据
2. 切换租户身份访问
3. 验证数据隔离

**预期结果**：
- 租户间数据完全隔离
- 无法访问其他租户数据

#### 测试用例11：文件访问控制
**测试目标**：验证文件URL访问控制
**测试步骤**：
1. 获取文件访问URL
2. 尝试未授权访问
3. 验证访问控制

**预期结果**：
- 未授权访问被拒绝
- 文件不会泄露

### 安全漏洞测试

#### 测试用例12：文件上传安全
**测试目标**：验证文件上传安全性
**测试内容**：
- 恶意文件上传测试
- 文件类型验证测试
- 文件大小限制测试
- 路径遍历攻击测试

#### 测试用例13：SQL注入测试
**测试目标**：验证接口防SQL注入
**测试内容**：
- 参数注入测试
- 文件名注入测试
- 搜索参数注入测试

---

## 🔄 兼容性测试

### API兼容性测试

#### 测试用例14：现有接口兼容性
**测试目标**：验证现有API接口完全兼容
**测试步骤**：
1. 使用现有前端代码调用API
2. 验证响应格式一致
3. 验证功能正常

**预期结果**：
- 现有功能完全正常
- 响应格式保持一致
- 前端无需修改

#### 测试用例15：数据格式兼容性
**测试目标**：验证数据格式向后兼容
**测试步骤**：
1. 对比新旧数据格式
2. 验证字段映射正确
3. 验证数据完整性

**预期结果**：
- 数据格式兼容
- 字段映射正确
- 无数据丢失

---

## 📊 测试执行计划

### 测试阶段安排

#### 第一阶段：单元测试（开发期间）
- **时间**：开发过程中持续进行
- **负责人**：开发工程师
- **覆盖率要求**：>80%

#### 第二阶段：功能测试（开发完成后）
- **时间**：2天
- **负责人**：测试工程师
- **测试内容**：所有功能测试用例

#### 第三阶段：性能测试（功能测试通过后）
- **时间**：1天
- **负责人**：测试工程师 + 运维工程师
- **测试内容**：性能和压力测试

#### 第四阶段：安全测试（性能测试通过后）
- **时间**：1天
- **负责人**：安全工程师
- **测试内容**：安全漏洞和权限测试

#### 第五阶段：兼容性测试（安全测试通过后）
- **时间**：0.5天
- **负责人**：测试工程师
- **测试内容**：API和数据兼容性测试

### 测试环境要求

#### 硬件环境
- **CPU**：4核心以上
- **内存**：8GB以上
- **存储**：SSD 100GB以上
- **网络**：千兆网络

#### 软件环境
- **操作系统**：Linux/Windows
- **数据库**：MySQL 8.0+
- **PHP**：8.1+
- **Redis**：6.0+
- **Web服务器**：Nginx/Apache

### 测试数据准备

#### 基础测试数据
```sql
-- 创建测试用户
INSERT INTO system_admin (username, real_name, tenant_id) VALUES
('test_user_1', '测试用户1', 0),
('test_user_2', '测试用户2', 0),
('test_user_3', '测试用户3', 1);

-- 创建测试文件分类
INSERT INTO system_attachment_category (name, parent_id, tenant_id) VALUES
('图片', 0, 0),
('文档', 0, 0),
('视频', 0, 0);
```

#### 性能测试数据
- **用户数量**：1000个
- **文件数量**：10万个
- **文件大小**：1KB-100MB
- **重复文件比例**：30%

---

## 📋 测试报告模板

### 测试执行报告
```
测试项目：附件系统优化
测试版本：v1.0
测试时间：2025-01-31
测试环境：测试环境

测试结果汇总：
- 测试用例总数：15个
- 通过用例数：14个
- 失败用例数：1个
- 通过率：93.3%

性能测试结果：
- 文件上传平均响应时间：1.2秒
- 文件列表查询平均响应时间：350ms
- 并发支持：100用户
- 去重命中率：96%

安全测试结果：
- 权限控制：通过
- 数据隔离：通过
- 文件安全：通过
- 无严重安全漏洞

兼容性测试结果：
- API兼容性：100%
- 数据兼容性：100%
- 功能兼容性：100%

问题和建议：
1. 大文件上传性能需要优化
2. 建议增加文件访问日志
3. 考虑添加文件预览功能
```

---

## ✅ 验收标准

### 功能验收标准
- [ ] 所有功能测试用例通过
- [ ] 文件去重率>90%
- [ ] 用户文件完全隔离
- [ ] API接口完全兼容

### 性能验收标准
- [ ] 文件上传响应时间<2秒
- [ ] 文件列表查询<500ms
- [ ] 支持100并发用户
- [ ] 系统资源使用正常

### 安全验收标准
- [ ] 无严重安全漏洞
- [ ] 权限控制正确
- [ ] 数据隔离完整
- [ ] 文件访问安全

### 质量验收标准
- [ ] 代码覆盖率>80%
- [ ] 无阻塞性缺陷
- [ ] 文档完整准确
- [ ] 部署脚本可用

<?php

namespace app\system\controller\permission;

use app\common\core\base\BaseAdminController;
use app\system\service\PostService;
use think\response\Json;

/**
 * 岗位控制器
 */
class PostController extends BaseAdminController
{
	/**
	 * @var PostService
	 */
	private PostService $service;
	
	public function initialize(): void
	{
		parent::initialize();
		
		$this->service = PostService::getInstance();
	}
	
    /**
     * 获取岗位列表
     */
    public function index(): <PERSON><PERSON>
    {
        return $this->success('获取成功', $this->service->getList(input()));
    }
    
    /**
     * 获取岗位详情
     */
    public function detail($id): J<PERSON>
    {
        return $this->success('获取成功', $this->service->getDetail((int)$id));
    }
    
    /**
     * 创建岗位
     */
    public function add(): J<PERSON>
    {
        return $this->service->create($this->request->post(),$this->adminId) ? $this->success('创建成功') : $this->error('创建失败');
    }
    
    /**
     * 更新岗位
     */
    public function edit($id): Json
    {
        return $this->service->update((int)$id, input()) ? $this->success('更新成功') : $this->error('更新失败');
    }
    
    /**
     * 删除岗位
     */
    public function delete(): Json
    {
	    return $this->service->delete(input('id')) ? $this->success('删除成功') : $this->error('删除失败');
    }
    
    /**
     * 获取所有岗位（用于下拉选择）
     */
    public function options(): Json
    {
        return $this->success('获取成功', $this->service->getOptions([
	        [
		        'tenant_id',
		        '=',
		        $this->tenantId,
	        ],
	        [
		        'status',
		        '=',
		        1
	        ]
        ]));
    }
	
}

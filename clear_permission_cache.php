<?php
/**
 * 清除权限相关缓存
 */

echo "=== 清除权限相关缓存 ===\n\n";

// 清除runtime缓存目录
$runtimeDir = 'runtime';
if (is_dir($runtimeDir)) {
    echo "1. 清除runtime缓存:\n";
    
    // 递归删除目录内容
    function deleteDir($dir) {
        if (!is_dir($dir)) return false;
        
        $files = array_diff(scandir($dir), array('.', '..'));
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                deleteDir($path);
            } else {
                unlink($path);
            }
        }
        return rmdir($dir);
    }
    
    $cacheFiles = 0;
    if (is_dir($runtimeDir)) {
        $items = scandir($runtimeDir);
        foreach ($items as $item) {
            if ($item == '.' || $item == '..') continue;
            
            $itemPath = $runtimeDir . DIRECTORY_SEPARATOR . $item;
            if (is_file($itemPath)) {
                unlink($itemPath);
                $cacheFiles++;
            } elseif (is_dir($itemPath)) {
                deleteDir($itemPath);
                $cacheFiles++;
            }
        }
    }
    
    echo "  ✅ 清除了 {$cacheFiles} 个缓存文件/目录\n";
} else {
    echo "1. runtime目录不存在，无需清除缓存\n";
}

// 清除可能的权限缓存文件
$cacheFiles = [
    'cache/permission.php',
    'cache/route.php',
    'cache/config.php',
    'storage/cache/permission.cache',
    'storage/cache/route.cache'
];

echo "\n2. 清除特定缓存文件:\n";
$clearedFiles = 0;
foreach ($cacheFiles as $file) {
    if (file_exists($file)) {
        unlink($file);
        echo "  ✅ 删除: {$file}\n";
        $clearedFiles++;
    }
}

if ($clearedFiles == 0) {
    echo "  ℹ️ 没有找到需要清除的缓存文件\n";
}

// 检查权限服务是否正常
echo "\n3. 测试权限服务:\n";

try {
    // 模拟权限验证
    $testRoute = 'app\\system\\controller\\permission\\AdminController@index';
    echo "  测试路由: {$testRoute}\n";
    
    // 解析权限
    [$classPath, $method] = explode('@', $testRoute);
    $parts = explode('\\', $classPath);
    $module = strtolower($parts[1]);
    $controllerClass = $parts[count($parts) - 1];
    $controllerName = str_replace('Controller', '', $controllerClass);
    
    // 生成权限名称
    if (count($parts) > 4) {
        $subPath = strtolower($parts[3]);
        $controller = strtolower($controllerName);
        $permissionName = $subPath . ':' . $controller . ':' . $method;
    } else {
        $controller = strtolower($controllerName);
        $permissionName = $controller . ':' . $method;
    }
    
    $fullPermission = "{$module}:{$permissionName}";
    echo "  解析权限: {$fullPermission}\n";
    echo "  ✅ 权限解析服务正常\n";
    
} catch (Exception $e) {
    echo "  ❌ 权限解析服务异常: " . $e->getMessage() . "\n";
}

echo "\n4. 建议的下一步操作:\n";
echo "  1. 重启Web服务器 (nginx/apache)\n";
echo "  2. 重启PHP-FPM服务\n";
echo "  3. 清除浏览器缓存\n";
echo "  4. 重新登录系统测试权限\n";

echo "\n✅ 缓存清除完成！\n";
echo "\n=== 缓存清除完成 ===\n";

# 权限测试执行方案

## 📋 概述

本文档提供完整的权限测试执行方案，包括环境准备、测试执行步骤、结果验证和问题跟踪等详细操作指南。

## 🚀 测试环境准备

### 1. 数据库备份（可选）
```sql
-- 备份现有数据（可选）
CREATE TABLE system_admin_backup AS SELECT * FROM system_admin;
CREATE TABLE system_role_backup AS SELECT * FROM system_role;
CREATE TABLE system_dept_backup AS SELECT * FROM system_dept;
CREATE TABLE system_admin_role_backup AS SELECT * FROM system_admin_role;
CREATE TABLE system_role_menu_backup AS SELECT * FROM system_role_menu;
```

### 2. 执行测试数据创建
按顺序执行以下脚本：

#### 步骤1：创建测试组织架构
```bash
# 执行部门、角色、用户创建脚本
mysql -u用户名 -p数据库名 < docs/test_role/sql/01-create-test-data.sql
```

#### 步骤2：配置角色菜单权限
```bash
# 执行角色菜单权限配置脚本
mysql -u用户名 -p数据库名 < docs/test_role/sql/02-role-menu-permissions.sql
```

#### 步骤3：创建业务测试数据
```bash
# 执行CRM测试数据创建脚本
mysql -u用户名 -p数据库名 < docs/test_role/sql/03-business-test-data.sql
```

### 3. 验证环境准备
```sql
-- 验证测试数据创建是否成功
SELECT '部门数据' as type, COUNT(*) as count FROM system_dept WHERE tenant_id = 1
UNION ALL
SELECT '角色数据', COUNT(*) FROM system_role WHERE tenant_id = 1
UNION ALL
SELECT '用户数据', COUNT(*) FROM system_admin WHERE tenant_id = 1
UNION ALL
SELECT '用户角色关联', COUNT(*) FROM system_admin_role WHERE tenant_id = 1
UNION ALL
SELECT '角色菜单权限', COUNT(*) FROM system_role_menu WHERE tenant_id = 1;
```

## 🧪 按钮权限测试执行

### 测试流程
1. **登录测试账号**
2. **访问功能模块**
3. **检查按钮显示**
4. **验证功能访问**
5. **记录测试结果**

### 测试账号信息
| 账号 | 密码 | 角色 | 测试重点 |
|------|------|------|----------|
| tenant_admin | password | 租户超级管理员 | 全部按钮权限 |
| sales_manager | password | 部门经理 | 管理类按钮权限 |
| sales_leader1 | password | 组长 | 基础操作按钮权限 |
| sales_staff1 | password | 普通员工 | 只读类按钮权限 |
| custom_user | password | 自定义权限 | 特定按钮组合 |

### 按钮权限测试检查表

#### 1. 权限管理模块测试

**管理员管理页面** (`/system/admin`)

| 按钮功能 | tenant_admin | sales_manager | sales_leader1 | sales_staff1 | custom_user |
|----------|--------------|---------------|---------------|--------------|-------------|
| 新增管理员 | ✅ | ❌ | ❌ | ❌ | ❌ |
| 编辑管理员 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 删除管理员 | ✅ | ❌ | ❌ | ❌ | ❌ |
| 查看详情 | ✅ | ✅ | ✅ | ❌ | ❌ |
| 重置密码 | ✅ | ❌ | ❌ | ❌ | ❌ |

**角色管理页面** (`/system/role`)

| 按钮功能 | tenant_admin | sales_manager | sales_leader1 | sales_staff1 | custom_user |
|----------|--------------|---------------|---------------|--------------|-------------|
| 新增角色 | ✅ | ❌ | ❌ | ❌ | ❌ |
| 编辑角色 | ✅ | ❌ | ❌ | ❌ | ❌ |
| 删除角色 | ✅ | ❌ | ❌ | ❌ | ❌ |
| 查看详情 | ✅ | ❌ | ❌ | ❌ | ❌ |

#### 2. CRM客户管理模块测试

**我的客户页面** (`/crm/customer`)

| 按钮功能 | tenant_admin | sales_manager | sales_leader1 | sales_staff1 | custom_user |
|----------|--------------|---------------|---------------|--------------|-------------|
| 新增客户 | ✅ | ✅ | ✅ | ✅ | ❌ |
| 编辑客户 | ✅ | ✅ | ✅ | ✅ | ❌ |
| 删除客户 | ✅ | ❌ | ❌ | ❌ | ❌ |
| 查看详情 | ✅ | ✅ | ✅ | ✅ | ❌ |
| 导入客户 | ✅ | ❌ | ❌ | ❌ | ❌ |
| 导出客户 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 转移客户 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 共享客户 | ✅ | ✅ | ❌ | ❌ | ❌ |

## 📊 数据权限测试执行

### 测试执行步骤

#### 1. 租户超级管理员测试
```bash
# 登录账号：tenant_admin
# 预期：能看到所有租户内数据

# 测试客户管理
访问：/crm/customer
预期结果：显示所有12条客户记录

# 测试线索管理  
访问：/crm/lead
预期结果：显示所有8条线索记录
```

#### 2. 部门经理权限测试
```bash
# 登录账号：sales_manager
# 预期：能看到销售部及下级部门数据

# 测试客户管理
访问：/crm/customer
预期结果：显示客户1001-1008（8条记录）

# 登录账号：tech_manager  
# 预期：能看到技术部及下级部门数据

# 测试客户管理
访问：/crm/customer
预期结果：显示客户1009-1010, 1012（3条记录）
```

#### 3. 组长权限测试
```bash
# 登录账号：sales_leader1
# 预期：只能看到销售一组数据

# 测试客户管理
访问：/crm/customer
预期结果：显示客户1003-1006（4条记录）
```

#### 4. 普通员工权限测试
```bash
# 登录账号：sales_staff1
# 预期：只能看到自己创建的数据

# 测试客户管理
访问：/crm/customer
预期结果：显示客户1005-1006（2条记录）
```

#### 5. 自定义权限测试
```bash
# 登录账号：custom_user
# 预期：能看到销售部+技术部数据

# 测试客户管理
访问：/crm/customer
预期结果：显示客户1001-1002, 1009, 1012（4条记录）
```

## 📝 测试结果记录

### 按钮权限测试结果表
```
测试日期：____年____月____日
测试人员：________________

| 功能模块 | 测试账号 | 预期按钮 | 实际按钮 | 测试结果 | 问题描述 |
|----------|----------|----------|----------|----------|----------|
| 管理员管理 | tenant_admin | 新增/编辑/删除/详情/重置 |  | ⭕ |  |
| 管理员管理 | sales_manager | 编辑/详情 |  | ⭕ |  |
| 客户管理 | sales_staff1 | 新增/编辑/详情 |  | ⭕ |  |
| ... | ... | ... | ... | ... | ... |
```

### 数据权限测试结果表
```
| 测试账号 | 功能模块 | 预期记录数 | 实际记录数 | 测试结果 | 问题描述 |
|----------|----------|------------|------------|----------|----------|
| tenant_admin | 客户管理 | 12 |  | ⭕ |  |
| sales_manager | 客户管理 | 8 |  | ⭕ |  |
| sales_leader1 | 客户管理 | 4 |  | ⭕ |  |
| sales_staff1 | 客户管理 | 2 |  | ⭕ |  |
| custom_user | 客户管理 | 4 |  | ⭕ |  |
```

## 🔍 问题跟踪和解决

### 常见问题及解决方案

#### 1. 按钮权限问题
**问题**：用户能看到不应该看到的按钮
**排查步骤**：
1. 检查角色菜单权限配置
2. 检查前端权限指令实现
3. 检查权限中间件配置

**解决方案**：
```sql
-- 检查角色菜单权限
SELECT rm.role_id, r.name as role_name, rm.menu_id, m.title as menu_title
FROM system_role_menu rm
LEFT JOIN system_role r ON rm.role_id = r.id
LEFT JOIN system_menu m ON rm.menu_id = m.id
WHERE rm.tenant_id = 1 AND rm.role_id = ? ORDER BY rm.menu_id;
```

#### 2. 数据权限问题
**问题**：用户能看到不应该看到的数据
**排查步骤**：
1. 检查用户角色配置
2. 检查角色数据权限设置
3. 检查数据权限实现逻辑

**解决方案**：
```sql
-- 检查用户数据权限配置
SELECT a.id, a.username, ar.role_id, r.name as role_name, r.data_scope, r.data_scope_dept_ids
FROM system_admin a
LEFT JOIN system_admin_role ar ON a.id = ar.admin_id
LEFT JOIN system_role r ON ar.role_id = r.id
WHERE a.tenant_id = 1 AND a.id = ?;
```

## ✅ 测试完成检查清单

- [ ] 测试环境准备完成
- [ ] 测试数据创建成功
- [ ] 按钮权限测试完成
- [ ] 数据权限测试完成
- [ ] 测试结果记录完整
- [ ] 发现问题已跟踪
- [ ] 测试报告已生成

## 📋 测试报告模板

### 权限测试报告

**测试概要**
- 测试时间：
- 测试人员：
- 测试环境：
- 测试范围：

**测试结果统计**
- 按钮权限测试用例：通过 __ / 总计 __
- 数据权限测试用例：通过 __ / 总计 __
- 总体通过率：__%

**发现问题**
1. 问题描述：
   - 严重程度：
   - 影响范围：
   - 解决方案：

**测试结论**
- [ ] 权限系统功能正常，可以上线
- [ ] 存在问题，需要修复后重新测试

**建议和改进**
1. 建议内容...
2. 改进方向...

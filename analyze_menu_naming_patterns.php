<?php
/**
 * 深入分析system_menu表中的权限命名规范
 */

require_once 'vendor/autoload.php';

echo "=== 深入分析system_menu表权限命名规范 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 统计所有权限命名模式:\n";
    
    $stmt = $pdo->prepare("
        SELECT name, title, type, COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $allPermissions = $stmt->fetchAll();
    
    // 分析命名模式
    $patterns = [
        'module:controller:action' => [],
        'module:controller_name:action' => [],
        'module:controller' => [],
        'single_word' => [],
        'other' => []
    ];
    
    foreach ($allPermissions as $perm) {
        $name = $perm['name'];
        $parts = explode(':', $name);
        
        if (count($parts) == 3) {
            // 三段式：module:controller:action
            if (strpos($parts[1], '_') !== false) {
                $patterns['module:controller_name:action'][] = $perm;
            } else {
                $patterns['module:controller:action'][] = $perm;
            }
        } elseif (count($parts) == 2) {
            // 二段式：module:controller
            $patterns['module:controller'][] = $perm;
        } elseif (count($parts) == 1) {
            // 单词
            $patterns['single_word'][] = $perm;
        } else {
            // 其他格式
            $patterns['other'][] = $perm;
        }
    }
    
    echo "  权限命名模式统计:\n";
    foreach ($patterns as $pattern => $perms) {
        echo "    {$pattern}: " . count($perms) . " 个\n";
    }
    
    echo "\n2. 详细分析各种命名模式:\n";
    
    // 分析三段式权限（带下划线）
    echo "\n  2.1 三段式权限（controller_name格式）:\n";
    $controllerNamePatterns = [];
    foreach ($patterns['module:controller_name:action'] as $perm) {
        $parts = explode(':', $perm['name']);
        $module = $parts[0];
        $controller = $parts[1];
        
        if (!isset($controllerNamePatterns[$module])) {
            $controllerNamePatterns[$module] = [];
        }
        if (!isset($controllerNamePatterns[$module][$controller])) {
            $controllerNamePatterns[$module][$controller] = [];
        }
        $controllerNamePatterns[$module][$controller][] = $perm;
    }
    
    foreach ($controllerNamePatterns as $module => $controllers) {
        echo "    {$module}模块:\n";
        foreach ($controllers as $controller => $perms) {
            echo "      {$controller}: " . count($perms) . " 个权限\n";
            // 显示前3个权限示例
            for ($i = 0; $i < min(3, count($perms)); $i++) {
                echo "        - {$perms[$i]['name']} ({$perms[$i]['title']})\n";
            }
        }
    }
    
    // 分析三段式权限（不带下划线）
    echo "\n  2.2 三段式权限（controller格式）:\n";
    $simpleControllerPatterns = [];
    foreach ($patterns['module:controller:action'] as $perm) {
        $parts = explode(':', $perm['name']);
        $module = $parts[0];
        $controller = $parts[1];
        
        if (!isset($simpleControllerPatterns[$module])) {
            $simpleControllerPatterns[$module] = [];
        }
        if (!isset($simpleControllerPatterns[$module][$controller])) {
            $simpleControllerPatterns[$module][$controller] = [];
        }
        $simpleControllerPatterns[$module][$controller][] = $perm;
    }
    
    foreach ($simpleControllerPatterns as $module => $controllers) {
        echo "    {$module}模块:\n";
        foreach ($controllers as $controller => $perms) {
            echo "      {$controller}: " . count($perms) . " 个权限\n";
            // 显示前3个权限示例
            for ($i = 0; $i < min(3, count($perms)); $i++) {
                echo "        - {$perms[$i]['name']} ({$perms[$i]['title']})\n";
            }
        }
    }
    
    // 分析二段式权限
    echo "\n  2.3 二段式权限（module:controller格式）:\n";
    $twoPartPatterns = [];
    foreach ($patterns['module:controller'] as $perm) {
        $parts = explode(':', $perm['name']);
        $module = $parts[0];
        $controller = $parts[1];
        
        if (!isset($twoPartPatterns[$module])) {
            $twoPartPatterns[$module] = [];
        }
        $twoPartPatterns[$module][] = $perm;
    }
    
    foreach ($twoPartPatterns as $module => $perms) {
        echo "    {$module}模块: " . count($perms) . " 个权限\n";
        foreach ($perms as $perm) {
            echo "      - {$perm['name']} ({$perm['title']})\n";
        }
    }
    
    echo "\n3. 命名不一致性分析:\n";
    
    // 查找可能的命名不一致
    echo "  3.1 同一模块内的命名不一致:\n";
    
    $moduleInconsistencies = [];
    
    // 检查每个模块是否同时存在两种命名方式
    $allModules = array_unique(array_merge(
        array_keys($controllerNamePatterns),
        array_keys($simpleControllerPatterns)
    ));
    
    foreach ($allModules as $module) {
        $hasUnderscoreFormat = isset($controllerNamePatterns[$module]) && !empty($controllerNamePatterns[$module]);
        $hasSimpleFormat = isset($simpleControllerPatterns[$module]) && !empty($simpleControllerPatterns[$module]);
        
        if ($hasUnderscoreFormat && $hasSimpleFormat) {
            $moduleInconsistencies[$module] = [
                'underscore_count' => array_sum(array_map('count', $controllerNamePatterns[$module])),
                'simple_count' => array_sum(array_map('count', $simpleControllerPatterns[$module]))
            ];
        }
    }
    
    if (!empty($moduleInconsistencies)) {
        foreach ($moduleInconsistencies as $module => $counts) {
            echo "    {$module}模块存在命名不一致:\n";
            echo "      - controller_name格式: {$counts['underscore_count']} 个\n";
            echo "      - controller格式: {$counts['simple_count']} 个\n";
        }
    } else {
        echo "    ✅ 各模块内部命名相对一致\n";
    }
    
    echo "\n4. 与控制器文件名对比分析:\n";
    
    // 检查实际控制器文件
    $controllerFiles = [];
    $directories = ['app/crm/controller', 'app/system/controller', 'app/project/controller'];
    
    foreach ($directories as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '/*Controller.php');
            foreach ($files as $file) {
                $filename = basename($file, '.php');
                $module = basename(dirname($dir));
                $controllerFiles[$module][] = $filename;
            }
            
            // 检查子目录
            $subdirs = glob($dir . '/*', GLOB_ONLYDIR);
            foreach ($subdirs as $subdir) {
                $subfiles = glob($subdir . '/*Controller.php');
                foreach ($subfiles as $file) {
                    $filename = basename($file, '.php');
                    $subdirName = basename($subdir);
                    $module = basename(dirname($dir));
                    $controllerFiles[$module][] = $subdirName . '/' . $filename;
                }
            }
        }
    }
    
    echo "  实际控制器文件统计:\n";
    foreach ($controllerFiles as $module => $files) {
        echo "    {$module}模块: " . count($files) . " 个控制器\n";
        foreach ($files as $file) {
            echo "      - {$file}\n";
        }
    }
    
    echo "\n5. 权限命名规范化建议:\n";
    echo "  基于分析结果，提供以下规范化方案...\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 分析完成 ===\n";

<?php
declare(strict_types=1);

namespace app\system\controller\tenant;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\system\service\TenantService;

/**
 * 租户表控制器
 */
class TenantController extends BaseController
{
	use CrudControllerTrait;
	
    /**
     * 服务实例
     *
     * @var TenantService
     */
    protected TenantService $service;
    
    /**
     * 初始化
     */
    protected function initialize(): void
    {
        parent::initialize();
        $this->service = TenantService::getInstance();
    }
} 
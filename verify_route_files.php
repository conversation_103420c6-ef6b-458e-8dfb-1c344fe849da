<?php
/**
 * 验证路由文件创建结果
 */

echo "=== 验证路由文件创建结果 ===\n\n";

$routeFiles = [
    'route/Auth.php',
    'route/System.php',
    'route/Workflow.php', 
    'route/Notice.php',
    'route/Router.php',
    'route/Crm.php',
    'route/Project.php',
    'route/Office.php',
    'route/Daily.php',
    'route/Ims.php'
];

echo "1. 检查所有路由文件状态:\n";

$results = [];
foreach ($routeFiles as $file) {
    $result = [
        'file' => $file,
        'exists' => file_exists($file),
        'size' => file_exists($file) ? filesize($file) : 0,
        'lines' => 0,
        'has_permission_middleware' => false,
        'has_token_middleware' => false
    ];
    
    if ($result['exists']) {
        $content = file_get_contents($file);
        $result['lines'] = count(explode("\n", $content));
        $result['has_permission_middleware'] = strpos($content, 'PermissionMiddleware') !== false;
        $result['has_token_middleware'] = strpos($content, 'TokenAuthMiddleware') !== false;
    }
    
    $results[] = $result;
    
    $status = $result['exists'] ? '✅' : '❌';
    echo "  {$status} {$file}\n";
    if ($result['exists']) {
        echo "    大小: {$result['size']} bytes, 行数: {$result['lines']}\n";
        echo "    权限中间件: " . ($result['has_permission_middleware'] ? '✅' : '❌') . "\n";
        echo "    Token中间件: " . ($result['has_token_middleware'] ? '✅' : '❌') . "\n";
    }
}

echo "\n2. 统计结果:\n";

$totalFiles = count($routeFiles);
$existingFiles = count(array_filter($results, fn($r) => $r['exists']));
$withPermission = count(array_filter($results, fn($r) => $r['has_permission_middleware']));
$withToken = count(array_filter($results, fn($r) => $r['has_token_middleware']));

echo "  总文件数: {$totalFiles}\n";
echo "  存在文件: {$existingFiles}\n";
echo "  有权限中间件: {$withPermission}\n";
echo "  有Token中间件: {$withToken}\n";
echo "  完成率: " . round($existingFiles / $totalFiles * 100, 1) . "%\n";

echo "\n3. 检查Router.php的daily路由迁移:\n";

if (file_exists('route/Router.php')) {
    $routerContent = file_get_contents('route/Router.php');
    $hasDailyRoutes = strpos($routerContent, 'daily_price_order') !== false;
    
    echo "  Router.php中是否还有daily路由: " . ($hasDailyRoutes ? '❌ 是' : '✅ 否') . "\n";
}

if (file_exists('route/Daily.php')) {
    $dailyContent = file_get_contents('route/Daily.php');
    $hasDailyRoutes = strpos($dailyContent, 'daily_price_order') !== false;
    
    echo "  Daily.php中是否有daily路由: " . ($hasDailyRoutes ? '✅ 是' : '❌ 否') . "\n";
}

echo "\n4. 阶段1完成状态:\n";

$stage1Complete = $existingFiles === $totalFiles && $withPermission >= 8; // Auth.php不需要权限中间件

if ($stage1Complete) {
    echo "  ✅ 阶段1：路由文件统一 - 完成\n";
    echo "  - 所有路由文件已创建\n";
    echo "  - 中间件配置已统一\n";
    echo "  - daily路由已迁移\n";
    echo "  \n";
    echo "  可以开始阶段2：权限数据统一\n";
} else {
    echo "  ❌ 阶段1：路由文件统一 - 未完成\n";
    echo "  需要检查和修复问题\n";
}

echo "\n=== 验证完成 ===\n";

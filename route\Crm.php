<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/crm', function () {
	
	
	$nameSpace = '\app\crm\controller';
	
	// CRM模块路由将在此处添加
	// 基于现有控制器文件结构，需要添加相应的路由定义
	
	// 预留：客户管理相关路由
	// Route::get('customer/index', $nameSpace . '\CrmCustomerController@index');
	// Route::post('customer/add', $nameSpace . '\CrmCustomerController@add');
	// Route::post('customer/edit/:id', $nameSpace . '\CrmCustomerController@edit');
	// Route::post('customer/delete', $nameSpace . '\CrmCustomerController@delete');
	// Route::get('customer/detail/:id', $nameSpace . '\CrmCustomerController@detail');
	
	// 预留：我的客户相关路由
	// Route::get('customer_my/index', $nameSpace . '\CrmCustomerMyController@index');
	
	// 预留：线索管理相关路由
	// Route::get('lead/index', $nameSpace . '\CrmLeadController@index');
	// Route::post('lead/add', $nameSpace . '\CrmLeadController@add');
	// Route::post('lead/edit/:id', $nameSpace . '\CrmLeadController@edit');
	// Route::post('lead/delete', $nameSpace . '\CrmLeadController@delete');
	
	// 预留：商机管理相关路由
	// Route::get('business/index', $nameSpace . '\CrmBusinessController@index');
	// Route::post('business/add', $nameSpace . '\CrmBusinessController@add');
	// Route::post('business/edit/:id', $nameSpace . '\CrmBusinessController@edit');
	// Route::post('business/delete', $nameSpace . '\CrmBusinessController@delete');
	
	// 预留：合同管理相关路由
	// Route::get('contract/index', $nameSpace . '\CrmContractController@index');
	// Route::post('contract/add', $nameSpace . '\CrmContractController@add');
	// Route::post('contract/edit/:id', $nameSpace . '\CrmContractController@edit');
	// Route::post('contract/delete', $nameSpace . '\CrmContractController@delete');
	
	// 预留：产品管理相关路由
	// Route::get('product/index', $nameSpace . '\CrmProductController@index');
	// Route::post('product/add', $nameSpace . '\CrmProductController@add');
	// Route::post('product/edit/:id', $nameSpace . '\CrmProductController@edit');
	// Route::post('product/delete', $nameSpace . '\CrmProductController@delete');
	
	// 预留：其他CRM功能路由
	// 根据实际控制器文件添加相应路由
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
//	     OperationLogMiddleware::class
     ]);


# 权限测试快速执行指南

## 🚀 5分钟快速测试

### 步骤1：执行SQL脚本（2分钟）

```bash
# 进入项目根目录
cd /path/to/your/project

# 执行测试数据创建（按顺序执行）
mysql -u用户名 -p数据库名 < docs/test_role/sql/01-create-test-data.sql
mysql -u用户名 -p数据库名 < docs/test_role/sql/02-role-menu-permissions.sql
```

### 步骤2：验证数据创建（30秒）

```sql
-- 快速验证测试数据
SELECT 
    '部门' as type, COUNT(*) as count FROM system_dept WHERE tenant_id = 1
UNION ALL SELECT 
    '角色', COUNT(*) FROM system_role WHERE tenant_id = 1
UNION ALL SELECT 
    '用户', COUNT(*) FROM system_admin WHERE tenant_id = 1;

-- 预期结果：部门8个，角色5个，用户9个
```

### 步骤3：登录测试（2分钟）

使用以下账号登录系统测试：

| 账号 | 密码 | 测试重点 |
|------|------|----------|
| tenant_admin | password | 全部功能可见 |
| sales_manager | password | 管理功能可见，无删除权限 |
| sales_staff1 | password | 基础功能可见，无管理权限 |

### 步骤4：权限验证（30秒）

1. **访问管理员管理页面**：检查不同角色看到的按钮差异
2. **访问客户管理页面**：检查数据显示数量差异
3. **尝试无权限操作**：确认被正确拦截

## 📋 核心测试检查点

### ✅ 必须验证的功能

1. **按钮权限**
   - [ ] 超级管理员能看到所有按钮
   - [ ] 普通员工看不到删除、导入等高级按钮
   - [ ] 无权限按钮不显示或置灰

2. **数据权限**
   - [ ] 超级管理员能看到所有数据
   - [ ] 部门经理只能看到本部门及下级数据
   - [ ] 普通员工只能看到自己创建的数据

3. **租户隔离**
   - [ ] 只能看到租户ID=1的数据
   - [ ] 无法访问其他租户数据

## 🔧 常用测试SQL

### 检查用户权限配置
```sql
SELECT 
    a.username,
    a.real_name,
    d.name as dept_name,
    r.name as role_name,
    CASE r.data_scope
        WHEN 1 THEN '全部数据'
        WHEN 2 THEN '本部门'
        WHEN 3 THEN '本部门及以下'
        WHEN 4 THEN '仅本人'
        WHEN 5 THEN '自定义'
    END as data_scope_text
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
LEFT JOIN system_admin_role ar ON a.id = ar.admin_id
LEFT JOIN system_role r ON ar.role_id = r.id
WHERE a.tenant_id = 1
ORDER BY a.id;
```

### 检查角色菜单权限
```sql
SELECT 
    r.name as role_name,
    COUNT(rm.menu_id) as menu_count
FROM system_role r
LEFT JOIN system_role_menu rm ON r.id = rm.role_id
WHERE r.tenant_id = 1
GROUP BY r.id, r.name
ORDER BY r.id;
```

### 模拟数据权限查询（如果有CRM表）
```sql
-- 创建测试客户数据
INSERT INTO crm_customer (name, creator_id, tenant_id, created_at) VALUES
('测试客户A', 202, 1, NOW()),  -- 销售经理创建
('测试客户B', 204, 1, NOW()),  -- 销售员工创建
('测试客户C', 206, 1, NOW());  -- 技术经理创建

-- 验证数据权限
-- 销售经理应该能看到：测试客户A, B（本部门及以下）
-- 销售员工应该能看到：测试客户B（仅本人）
-- 技术经理应该能看到：测试客户C（本部门及以下）
```

## ⚠️ 注意事项

1. **数据库备份**：测试前建议备份现有数据
2. **环境隔离**：在测试环境执行，避免影响生产
3. **密码安全**：测试账号密码为password，仅用于测试
4. **数据清理**：测试完成后可选择清理测试数据

## 🐛 常见问题

### 问题1：登录失败
**原因**：用户状态为禁用或密码错误  
**解决**：检查用户状态，确认密码为password

### 问题2：看不到菜单
**原因**：角色菜单权限未配置  
**解决**：执行02-role-menu-permissions.sql脚本

### 问题3：数据权限不生效
**原因**：数据权限实现可能有问题  
**解决**：检查后端数据权限过滤逻辑

## 📞 快速支持

如果遇到问题：
1. 检查SQL脚本执行是否成功
2. 查看系统错误日志
3. 验证数据库数据是否正确创建
4. 参考详细测试文档

## 🎯 测试成功标准

- [ ] 所有测试账号能正常登录
- [ ] 不同角色看到的按钮明显不同
- [ ] 数据权限过滤正确生效
- [ ] 无越权访问情况
- [ ] 租户数据完全隔离

完成以上检查点即表示权限系统基本功能正常！

<?php
/**
 * 简单的菜单重新排序
 * 只是按模块顺序重新排列，不改变ID
 */

require_once 'vendor/autoload.php';

echo "=== 简单的菜单重新排序 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取当前权限菜单并按模块分组:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type, status
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $allMenus = $stmt->fetchAll();
    
    echo "  当前权限总数: " . count($allMenus) . " 个\n\n";
    
    // 按模块分组
    $moduleGroups = [];
    foreach ($allMenus as $menu) {
        $parts = explode(':', $menu['name']);
        $module = $parts[0] ?? 'unknown';
        
        if (!isset($moduleGroups[$module])) {
            $moduleGroups[$module] = [];
        }
        $moduleGroups[$module][] = $menu;
    }
    
    // 模块处理顺序
    $moduleOrder = ['system', 'crm', 'project', 'workflow', 'notice', 'office', 'daily', 'ims'];
    
    echo "2. 按模块显示权限分布:\n";
    
    foreach ($moduleOrder as $module) {
        if (!isset($moduleGroups[$module])) {
            echo "  {$module}模块: 0个权限\n";
            continue;
        }
        
        $menus = $moduleGroups[$module];
        
        // 按权限名称排序
        usort($menus, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        $ids = array_column($menus, 'id');
        $minId = min($ids);
        $maxId = max($ids);
        
        echo "  {$module}模块: " . count($menus) . "个权限，当前ID范围 {$minId}-{$maxId}\n";
        
        // 显示前5个权限
        echo "    权限示例:\n";
        for ($i = 0; $i < min(5, count($menus)); $i++) {
            $menu = $menus[$i];
            echo "      ID:{$menu['id']} {$menu['name']} ({$menu['title']})\n";
        }
        if (count($menus) > 5) {
            echo "      ... 还有 " . (count($menus) - 5) . " 个权限\n";
        }
        echo "\n";
    }
    
    echo "3. 添加sort字段来实现逻辑排序:\n";
    
    // 使用sort字段来实现逻辑排序，而不是改变ID
    $pdo->beginTransaction();
    
    try {
        $sortCounter = 1;
        
        foreach ($moduleOrder as $module) {
            if (!isset($moduleGroups[$module])) {
                continue;
            }
            
            $menus = $moduleGroups[$module];
            
            // 按权限名称排序
            usort($menus, function($a, $b) {
                return strcmp($a['name'], $b['name']);
            });
            
            foreach ($menus as $menu) {
                $stmt = $pdo->prepare("UPDATE system_menu SET sort = ? WHERE id = ?");
                $stmt->execute([$sortCounter, $menu['id']]);
                $sortCounter++;
            }
            
            echo "  ✅ {$module}模块: " . count($menus) . "个权限已设置sort值\n";
        }
        
        $pdo->commit();
        
        echo "\n  ✅ 所有权限的sort字段已更新\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
    echo "\n4. 验证排序结果:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, sort
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY sort
        LIMIT 20
    ");
    $stmt->execute();
    $sortedMenus = $stmt->fetchAll();
    
    echo "  按sort字段排序的前20个权限:\n";
    foreach ($sortedMenus as $menu) {
        echo "    Sort:{$menu['sort']} ID:{$menu['id']} {$menu['name']} ({$menu['title']})\n";
    }
    
    echo "\n5. 建议的查询方式:\n";
    echo "  现在可以使用以下SQL按逻辑顺序查询权限:\n";
    echo "  SELECT * FROM system_menu WHERE status = 1 AND deleted_at IS NULL ORDER BY sort;\n";
    
    echo "\n🎉 菜单逻辑排序完成！\n";
    echo "  - 保持原有ID不变，避免外键约束问题\n";
    echo "  - 使用sort字段实现逻辑排序\n";
    echo "  - 按模块顺序：system → crm → project → workflow → notice → office → daily → ims\n";
    echo "  - 每个模块内按权限名称字母顺序排列\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 菜单排序完成 ===\n";

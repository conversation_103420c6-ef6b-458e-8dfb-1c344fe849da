<?php
/**
 * 分析当前路由文件的准确结构
 * 为路由统一提供精确的数据基础
 */

echo "=== 分析当前路由文件结构 ===\n\n";

echo "1. 检查现有路由文件:\n";

$routeFiles = [
    'route/Auth.php',
    'route/System.php', 
    'route/Workflow.php',
    'route/Notice.php',
    'route/Router.php',
    'route/Crm.php',
    'route/Project.php',
    'route/Office.php',
    'route/Daily.php',
    'route/Ims.php'
];

$existingFiles = [];
$missingFiles = [];

foreach ($routeFiles as $file) {
    if (file_exists($file)) {
        $existingFiles[] = $file;
        echo "  ✅ {$file} - 存在\n";
    } else {
        $missingFiles[] = $file;
        echo "  ❌ {$file} - 不存在\n";
    }
}

echo "\n2. 分析现有路由文件内容:\n";

foreach ($existingFiles as $file) {
    echo "\n  分析 {$file}:\n";
    
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    
    echo "    总行数: " . count($lines) . "\n";
    
    // 分析路由定义
    $routeCount = 0;
    $middlewareUsage = [];
    $routePatterns = [];
    
    foreach ($lines as $lineNum => $line) {
        $line = trim($line);
        
        // 统计路由定义
        if (preg_match('/Route::(get|post|put|delete|patch|any|match|group)/', $line, $matches)) {
            $routeCount++;
            $method = $matches[1];
            $routePatterns[] = [
                'line' => $lineNum + 1,
                'method' => $method,
                'content' => $line
            ];
        }
        
        // 分析中间件
        if (strpos($line, 'middleware') !== false) {
            if (strpos($line, 'PermissionMiddleware') !== false) {
                $middlewareUsage['permission'] = ($middlewareUsage['permission'] ?? 0) + 1;
            }
            if (strpos($line, 'TokenAuthMiddleware') !== false) {
                $middlewareUsage['token'] = ($middlewareUsage['token'] ?? 0) + 1;
            }
        }
    }
    
    echo "    路由数量: {$routeCount}\n";
    echo "    权限中间件: " . ($middlewareUsage['permission'] ?? 0) . " 处\n";
    echo "    Token中间件: " . ($middlewareUsage['token'] ?? 0) . " 处\n";
    
    // 显示前5个路由示例
    if (!empty($routePatterns)) {
        echo "    路由示例:\n";
        for ($i = 0; $i < min(5, count($routePatterns)); $i++) {
            $route = $routePatterns[$i];
            echo "      第{$route['line']}行: {$route['content']}\n";
        }
    }
}

echo "\n3. 分析Router.php中的模块路由:\n";

if (file_exists('route/Router.php')) {
    $routerContent = file_get_contents('route/Router.php');
    $lines = explode("\n", $routerContent);
    
    $moduleRoutes = [
        'crm' => [],
        'project' => [],
        'office' => [],
        'daily' => [],
        'ims' => []
    ];
    
    foreach ($lines as $lineNum => $line) {
        $line = trim($line);
        
        // 查找各模块的路由
        foreach ($moduleRoutes as $module => $routes) {
            if (stripos($line, $module) !== false && 
                preg_match('/Route::(get|post|put|delete|patch|any|match|group)/', $line)) {
                $moduleRoutes[$module][] = [
                    'line' => $lineNum + 1,
                    'content' => $line
                ];
            }
        }
    }
    
    foreach ($moduleRoutes as $module => $routes) {
        if (!empty($routes)) {
            echo "  {$module}模块路由 (" . count($routes) . "个):\n";
            foreach (array_slice($routes, 0, 3) as $route) {
                echo "    第{$route['line']}行: {$route['content']}\n";
            }
            if (count($routes) > 3) {
                echo "    ... 还有 " . (count($routes) - 3) . " 个路由\n";
            }
        } else {
            echo "  {$module}模块: 未找到相关路由\n";
        }
    }
}

echo "\n4. 生成路由迁移计划:\n";

$migrationPlan = [];

foreach ($missingFiles as $file) {
    $module = basename($file, '.php');
    $moduleLower = strtolower($module);
    
    $migrationPlan[] = [
        'file' => $file,
        'module' => $module,
        'action' => 'create_new',
        'source' => 'route/Router.php',
        'estimated_routes' => 0 // 需要从Router.php中统计
    ];
}

foreach ($migrationPlan as $plan) {
    echo "  创建 {$plan['file']}:\n";
    echo "    模块: {$plan['module']}\n";
    echo "    操作: {$plan['action']}\n";
    echo "    来源: {$plan['source']}\n";
}

echo "\n5. 下一步行动建议:\n";
echo "  1. 创建缺失的路由文件\n";
echo "  2. 从Router.php迁移相关路由\n";
echo "  3. 统一中间件配置\n";
echo "  4. 测试路由访问\n";

echo "\n=== 路由分析完成 ===\n";

// 保存分析结果
$analysisResult = [
    'existing_files' => $existingFiles,
    'missing_files' => $missingFiles,
    'migration_plan' => $migrationPlan,
    'analysis_time' => date('Y-m-d H:i:s')
];

file_put_contents('route_analysis_result.json', json_encode($analysisResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\n✅ 分析结果已保存到: route_analysis_result.json\n";

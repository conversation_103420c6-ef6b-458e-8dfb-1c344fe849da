import request from '@/utils/http'
import { BaseResult } from '@/types/axios'

/**
 * 附件管理API接口 - 重构版本
 * 支持文件去重、权限控制、多存储方式
 */
export interface AttachmentData {
  id: number
  attachment_id: number
  name: string
  display_name: string
  original_name: string
  path: string
  url: string
  size: number
  extension: string
  mime_type: string
  storage: string
  cate_id: number
  upload_source: string
  is_duplicate?: boolean
  ref_count?: number
  created_at: string
  updated_at: string
  upload_time?: string
}

export interface AttachmentListParams {
  page?: number
  limit?: number
  cate_id?: number
  is_uncategorized?: number
  keyword?: string
  media_type?: string
  storage?: string
  extension?: string
}

export interface AttachmentListResult {
  data: AttachmentData[]
  total: number
  current_page: number
  per_page: number
  last_page: number
}

export interface AttachmentStatsResult {
  total_files: number
  total_size: number
  total_size_formatted: string
  by_category: Array<{
    cate_id: number
    count: number
    size: number
  }>
  by_extension: Array<{
    extension: string
    count: number
    size: number
  }>
  by_storage: Array<{
    storage: string
    count: number
    size: number
  }>
}

export interface AttachmentPermissionResult {
  can_upload: boolean
  can_download: boolean
  can_delete: boolean
  can_share: boolean
  is_admin: boolean
  storage_quota: {
    used: number
    total: number
    percentage: number
  }
}

export interface BatchOperationResult {
  success_count: number
  failed_count: number
  failed_ids: number[]
}

export class AttachmentApi {
  /**
   * 获取文件列表（管理页面使用，根据权限返回不同数据）
   */
  static list(params: AttachmentListParams) {
    return request.get<BaseResult<AttachmentListResult>>({
      url: '/system/attachment/index',
      params
    })
  }

  /**
   * 获取用户文件列表（媒体选择器专用）
   */
  static userFiles(params: AttachmentListParams) {
    return request.get<BaseResult<AttachmentListResult>>({
      url: '/system/attachment/userFiles',
      params
    })
  }

  /**
   * 获取文件详情
   */
  static read(id: number) {
    return request.get<BaseResult<AttachmentData>>({
      url: `/system/attachment/read/${id}`
    })
  }

  /**
   * 文件上传
   */
  static upload(formData: FormData) {
    return request.post<BaseResult<AttachmentData>>({
      url: '/system/attachment/upload',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 重命名文件
   */
  static rename(id: number, name: string) {
    return request.post<BaseResult>({
      url: `/system/attachment/rename/${id}`,
      data: { name }
    })
  }

  /**
   * 移动文件
   */
  static move(id: number, cate_id: number) {
    return request.post<BaseResult>({
      url: `/system/attachment/move/${id}`,
      data: { cate_id }
    })
  }

  /**
   * 复制文件
   */
  static copy(id: number, target_cate_id: number, new_name?: string) {
    return request.post<BaseResult<AttachmentData>>({
      url: `/system/attachment/copy/${id}`,
      data: { target_cate_id, new_name }
    })
  }

  /**
   * 删除文件
   */
  static delete(ids: number[]) {
    return request.post<BaseResult<BatchOperationResult>>({
      url: '/system/attachment/delete',
      data: { ids }
    })
  }

  /**
   * 批量移动文件
   */
  static batchMove(file_ids: number[], cate_id: number) {
    return request.post<BaseResult<BatchOperationResult>>({
      url: '/system/attachment/batchMove',
      data: { file_ids, cate_id }
    })
  }

  /**
   * 获取用户文件统计
   */
  static stats() {
    return request.get<BaseResult<AttachmentStatsResult>>({
      url: '/system/attachment/stats'
    })
  }

  /**
   * 获取上传Token（云存储）
   */
  static getUploadToken(storage: string, params?: Record<string, any>) {
    return request.get<BaseResult>({
      url: '/system/attachment/getUploadToken',
      params: { storage, ...params }
    })
  }

  /**
   * 处理云存储上传回调
   */
  static uploadCallback(storage: string, params: Record<string, any>) {
    return request.post<BaseResult<AttachmentData>>({
      url: `/system/attachment/uploadCallback/${storage}`,
      data: params
    })
  }

  /**
   * 文件下载
   */
  static download(id: number) {
    return request.get({
      url: `/system/attachment/download/${id}`,
      responseType: 'blob'
    })
  }
}

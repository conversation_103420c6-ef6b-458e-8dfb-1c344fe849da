-- =====================================================
-- 权限测试 - 角色菜单权限配置脚本
-- 创建日期：2025-01-31
-- 说明：为测试角色配置不同的菜单按钮权限
-- =====================================================

-- 清理现有的角色菜单权限（租户ID=1）
DELETE FROM system_role_menu WHERE tenant_id = 1;

-- =====================================================
-- 1. 租户超级管理员权限配置（role_id=101）
-- 拥有所有功能的完整权限
-- =====================================================

-- 1.1 权限管理模块
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- 权限管理主菜单
(101, 1, 1, NOW(), NOW()),   -- 权限管理

-- 管理员管理
(101, 3, 1, NOW(), NOW()),   -- 管理员管理菜单
(101, 37, 1, NOW(), NOW()),  -- 列表
(101, 16, 1, NOW(), NOW()),  -- 新增
(101, 17, 1, NOW(), NOW()),  -- 编辑
(101, 18, 1, NOW(), NOW()),  -- 删除
(101, 38, 1, NOW(), NOW()),  -- 详情
(101, 31, 1, NOW(), NOW()),  -- 重置密码

-- 角色管理
(101, 6, 1, NOW(), NOW()),   -- 角色管理菜单
(101, 19, 1, NOW(), NOW()),  -- 新增
(101, 20, 1, NOW(), NOW()),  -- 编辑
(101, 21, 1, NOW(), NOW()),  -- 删除
(101, 36, 1, NOW(), NOW()),  -- 详情

-- 部门管理
(101, 7, 1, NOW(), NOW()),   -- 部门管理菜单
(101, 39, 1, NOW(), NOW()),  -- 列表
(101, 22, 1, NOW(), NOW()),  -- 新增
(101, 23, 1, NOW(), NOW()),  -- 编辑
(101, 24, 1, NOW(), NOW()),  -- 删除
(101, 40, 1, NOW(), NOW()),  -- 详情

-- 菜单管理
(101, 2, 1, NOW(), NOW()),   -- 菜单管理菜单
(101, 32, 1, NOW(), NOW()),  -- 列表
(101, 13, 1, NOW(), NOW()),  -- 新增
(101, 14, 1, NOW(), NOW()),  -- 编辑
(101, 15, 1, NOW(), NOW()),  -- 删除
(101, 33, 1, NOW(), NOW()),  -- 详情

-- 岗位管理
(101, 8, 1, NOW(), NOW()),   -- 岗位管理菜单
(101, 41, 1, NOW(), NOW()),  -- 列表
(101, 25, 1, NOW(), NOW()),  -- 新增
(101, 26, 1, NOW(), NOW()),  -- 编辑
(101, 27, 1, NOW(), NOW()),  -- 删除
(101, 42, 1, NOW(), NOW());  -- 详情

-- 1.2 CRM客户管理模块
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- CRM主菜单
(101, 189, 1, NOW(), NOW()),  -- 客户管理

-- 我的客户
(101, 192, 1, NOW(), NOW()),  -- 我的客户菜单
(101, 2420, 1, NOW(), NOW()), -- 新增
(101, 2421, 1, NOW(), NOW()), -- 编辑
(101, 2422, 1, NOW(), NOW()), -- 删除
(101, 2423, 1, NOW(), NOW()), -- 详情
(101, 2424, 1, NOW(), NOW()), -- 导入
(101, 2425, 1, NOW(), NOW()), -- 导出
(101, 2426, 1, NOW(), NOW()), -- 转移
(101, 2430, 1, NOW(), NOW()), -- 回收
(101, 2432, 1, NOW(), NOW()), -- 共享

-- 我的线索
(101, 191, 1, NOW(), NOW()),  -- 我的线索菜单
(101, 1911, 1, NOW(), NOW()), -- 新增
(101, 1912, 1, NOW(), NOW()), -- 编辑
(101, 1913, 1, NOW(), NOW()), -- 删除
(101, 1914, 1, NOW(), NOW()), -- 详情
(101, 1915, 1, NOW(), NOW()), -- 导出
(101, 1916, 1, NOW(), NOW()), -- 导入
(101, 1917, 1, NOW(), NOW()), -- 转化
(101, 2439, 1, NOW(), NOW()), -- 分配

-- 联系人管理
(101, 193, 1, NOW(), NOW()),  -- 联系人管理菜单
(101, 1950, 1, NOW(), NOW()), -- 新增
(101, 1951, 1, NOW(), NOW()), -- 编辑
(101, 1952, 1, NOW(), NOW()), -- 删除
(101, 1953, 1, NOW(), NOW()), -- 详情
(101, 1954, 1, NOW(), NOW()), -- 导出
(101, 1955, 1, NOW(), NOW()); -- 导入

-- 1.3 项目管理模块
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(101, 2440, 1, NOW(), NOW()); -- 项目管理主菜单

-- 1.4 每日报价模块
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(101, 2600, 1, NOW(), NOW()); -- 每日报价菜单

-- =====================================================
-- 2. 部门经理权限配置（role_id=102）
-- 拥有管理类权限，但无删除等高危操作
-- =====================================================

-- 2.1 权限管理模块（有限权限）
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- 管理员管理（查看和编辑）
(102, 3, 1, NOW(), NOW()),   -- 管理员管理菜单
(102, 37, 1, NOW(), NOW()),  -- 列表
(102, 17, 1, NOW(), NOW()),  -- 编辑
(102, 38, 1, NOW(), NOW()),  -- 详情

-- 部门管理（查看）
(102, 7, 1, NOW(), NOW()),   -- 部门管理菜单
(102, 39, 1, NOW(), NOW()),  -- 列表
(102, 40, 1, NOW(), NOW());  -- 详情

-- 2.2 CRM客户管理模块（管理权限）
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- CRM主菜单
(102, 189, 1, NOW(), NOW()),  -- 客户管理

-- 我的客户（管理权限）
(102, 192, 1, NOW(), NOW()),  -- 我的客户菜单
(102, 2420, 1, NOW(), NOW()), -- 新增
(102, 2421, 1, NOW(), NOW()), -- 编辑
(102, 2423, 1, NOW(), NOW()), -- 详情
(102, 2425, 1, NOW(), NOW()), -- 导出
(102, 2426, 1, NOW(), NOW()), -- 转移
(102, 2432, 1, NOW(), NOW()), -- 共享

-- 我的线索（管理权限）
(102, 191, 1, NOW(), NOW()),  -- 我的线索菜单
(102, 1911, 1, NOW(), NOW()), -- 新增
(102, 1912, 1, NOW(), NOW()), -- 编辑
(102, 1914, 1, NOW(), NOW()), -- 详情
(102, 1917, 1, NOW(), NOW()), -- 转化
(102, 2439, 1, NOW(), NOW()), -- 分配

-- 联系人管理
(102, 193, 1, NOW(), NOW()),  -- 联系人管理菜单
(102, 1950, 1, NOW(), NOW()), -- 新增
(102, 1951, 1, NOW(), NOW()), -- 编辑
(102, 1953, 1, NOW(), NOW()), -- 详情
(102, 1954, 1, NOW(), NOW()); -- 导出

-- 2.3 项目管理和报价模块
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(102, 2440, 1, NOW(), NOW()), -- 项目管理主菜单
(102, 2600, 1, NOW(), NOW()); -- 每日报价菜单

-- =====================================================
-- 3. 组长权限配置（role_id=103）
-- 拥有基础操作权限，无高级管理功能
-- =====================================================

-- 3.1 权限管理模块（仅查看）
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- 管理员管理（仅查看）
(103, 3, 1, NOW(), NOW()),   -- 管理员管理菜单
(103, 37, 1, NOW(), NOW()),  -- 列表
(103, 38, 1, NOW(), NOW());  -- 详情

-- 3.2 CRM客户管理模块（基础操作）
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- CRM主菜单
(103, 189, 1, NOW(), NOW()),  -- 客户管理

-- 我的客户（基础操作）
(103, 192, 1, NOW(), NOW()),  -- 我的客户菜单
(103, 2420, 1, NOW(), NOW()), -- 新增
(103, 2421, 1, NOW(), NOW()), -- 编辑
(103, 2423, 1, NOW(), NOW()), -- 详情

-- 我的线索（基础操作）
(103, 191, 1, NOW(), NOW()),  -- 我的线索菜单
(103, 1911, 1, NOW(), NOW()), -- 新增
(103, 1912, 1, NOW(), NOW()), -- 编辑
(103, 1914, 1, NOW(), NOW()), -- 详情
(103, 1917, 1, NOW(), NOW()), -- 转化

-- 联系人管理
(103, 193, 1, NOW(), NOW()),  -- 联系人管理菜单
(103, 1950, 1, NOW(), NOW()), -- 新增
(103, 1951, 1, NOW(), NOW()), -- 编辑
(103, 1953, 1, NOW(), NOW()); -- 详情

-- 3.3 报价模块
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(103, 2600, 1, NOW(), NOW()); -- 每日报价菜单

-- =====================================================
-- 4. 普通员工权限配置（role_id=104）
-- 仅基础查看和操作权限
-- =====================================================

-- 4.1 CRM客户管理模块（基础权限）
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
-- CRM主菜单
(104, 189, 1, NOW(), NOW()),  -- 客户管理

-- 我的客户（基础权限）
(104, 192, 1, NOW(), NOW()),  -- 我的客户菜单
(104, 2420, 1, NOW(), NOW()), -- 新增
(104, 2421, 1, NOW(), NOW()), -- 编辑
(104, 2423, 1, NOW(), NOW()), -- 详情

-- 我的线索（基础权限）
(104, 191, 1, NOW(), NOW()),  -- 我的线索菜单
(104, 1911, 1, NOW(), NOW()), -- 新增
(104, 1912, 1, NOW(), NOW()), -- 编辑
(104, 1914, 1, NOW(), NOW()), -- 详情

-- 联系人管理
(104, 193, 1, NOW(), NOW()),  -- 联系人管理菜单
(104, 1950, 1, NOW(), NOW()), -- 新增
(104, 1951, 1, NOW(), NOW()), -- 编辑
(104, 1953, 1, NOW(), NOW()); -- 详情

-- 4.2 报价模块
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(104, 2600, 1, NOW(), NOW()); -- 每日报价菜单

-- =====================================================
-- 5. 自定义权限配置（role_id=105）
-- 特定功能组合权限
-- =====================================================

-- 5.1 项目管理模块（技术相关）
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`) VALUES
(105, 2440, 1, NOW(), NOW()); -- 项目管理主菜单

-- =====================================================
-- 验证权限配置结果
-- =====================================================
SELECT '=== 角色菜单权限配置完成 ===' as message;

SELECT
    r.id as role_id,
    r.name as role_name,
    COUNT(rm.menu_id) as menu_count
FROM system_role r
LEFT JOIN system_role_menu rm ON r.id = rm.role_id AND rm.tenant_id = 1
WHERE r.tenant_id = 1
GROUP BY r.id, r.name
ORDER BY r.id;

# Route目录优化方案

## 📊 现状分析

### 当前问题
1. **文件过多**：38个路由文件，管理复杂
2. **命名不规范**：CRM模块有22个独立文件
3. **中间件配置分散**：相同中间件配置重复定义
4. **维护困难**：修改中间件需要更新多个文件

### 文件分布统计
- **CRM模块**：22个文件 (58%)
- **项目模块**：5个文件 (13%)
- **系统模块**：1个文件 (3%)
- **其他模块**：10个文件 (26%)

## 🎯 优化方案

### 方案1：按模块合并（推荐）

#### 目标结构
```
route/
├── modules/
│   ├── crm.php          # 合并所有CRM路由
│   ├── project.php      # 合并所有项目路由
│   ├── system.php       # 系统管理路由
│   ├── workflow.php     # 工作流路由
│   ├── notice.php       # 通知路由
│   ├── hr.php           # 人事路由
│   ├── daily.php        # 每日报价路由
│   └── ims.php          # 库存管理路由
├── common.php           # 公共接口
├── auth.php             # 认证接口
└── public.php           # 公开接口
```

#### 优化效果
- **文件数量**：从38个减少到11个 (减少71%)
- **维护性**：按模块组织，逻辑清晰
- **可读性**：相关功能集中管理

### 方案2：中间件配置规范

#### 标准中间件组合
```php
// 1. 公开接口（无中间件）
Route::group('api/public', function () {
    // 公开API
});

// 2. 认证接口（特殊中间件）
Route::group('api/auth', function () {
    // 登录、注册等
})->middleware([CheckLoginAttempts::class]);

// 3. 仅Token认证
Route::group('api/common', function () {
    // 公共接口、选项接口等
})->middleware([TokenAuthMiddleware::class]);

// 4. Token + 权限验证
Route::group('api', function () {
    // 大部分业务接口
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);

// 5. Token + 权限 + 操作日志
Route::group('api/admin', function () {
    // 系统管理接口
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class,
    OperationLogMiddleware::class
]);
```

## 🛠️ 实施计划

### 阶段1：CRM模块合并
**目标**：将22个CRM路由文件合并为1个

**步骤**：
1. 创建 `route/modules/crm.php`
2. 按功能模块组织路由
3. 统一中间件配置
4. 删除原有22个文件

**预期效果**：
- 文件数量减少21个
- CRM路由集中管理

### 阶段2：项目模块合并
**目标**：将5个项目路由文件合并为1个

**步骤**：
1. 创建 `route/modules/project.php`
2. 合并项目相关路由
3. 统一中间件配置
4. 删除原有5个文件

### 阶段3：中间件规范化
**目标**：建立统一的中间件配置规范

**步骤**：
1. 定义标准中间件组合
2. 更新所有路由文件
3. 创建中间件配置文档
4. 建立开发规范

## 📋 中间件配置规范

### 接口分类标准

#### 1. 公开接口 (Public)
- **特征**：无需认证的接口
- **示例**：验证码、公开信息查询
- **中间件**：无
- **路由组**：`api/public`

#### 2. 认证接口 (Auth)
- **特征**：登录、注册相关
- **示例**：用户登录、密码重置
- **中间件**：`CheckLoginAttempts`
- **路由组**：`api/auth`

#### 3. 公共接口 (Common)
- **特征**：需要登录但不需要权限验证
- **示例**：选项列表、基础数据
- **中间件**：`TokenAuthMiddleware`
- **路由组**：`api/common`

#### 4. 业务接口 (Business)
- **特征**：需要登录和权限验证
- **示例**：CRM、项目管理
- **中间件**：`TokenAuthMiddleware` + `PermissionMiddleware`
- **路由组**：`api/business`

#### 5. 管理接口 (Admin)
- **特征**：需要完整权限验证和操作日志
- **示例**：系统管理、用户管理
- **中间件**：`TokenAuthMiddleware` + `PermissionMiddleware` + `OperationLogMiddleware`
- **路由组**：`api/admin`

### 路由文件命名规范

#### 模块路由文件
```
route/modules/{module}.php
```

#### 功能路由文件
```
route/{function}.php
```

#### 示例
```
route/
├── modules/
│   ├── crm.php          # CRM模块
│   ├── project.php      # 项目模块
│   └── system.php       # 系统模块
├── common.php           # 公共接口
├── auth.php             # 认证接口
└── public.php           # 公开接口
```

## 📚 开发规范文档

### 新增路由规范

#### 1. 确定接口类型
根据接口功能确定所属类型：
- 是否需要登录？
- 是否需要权限验证？
- 是否需要操作日志？

#### 2. 选择路由文件
根据接口类型选择对应的路由文件：
- 业务功能 → `modules/{module}.php`
- 公共功能 → `common.php`
- 认证功能 → `auth.php`
- 公开功能 → `public.php`

#### 3. 添加路由定义
```php
// 在对应文件中添加路由
Route::get('path', 'Controller@method');
```

### 中间件配置规范

#### 1. 使用预定义组合
优先使用标准中间件组合，避免自定义配置。

#### 2. 特殊需求处理
如需特殊中间件配置，在路由定义时单独指定：
```php
Route::get('special', 'Controller@method')
    ->middleware([CustomMiddleware::class]);
```

#### 3. 文档更新
新增特殊配置时，及时更新文档说明。

## ✅ 验收标准

### 文件数量目标
- **当前**：38个路由文件
- **目标**：≤ 15个路由文件
- **减少**：≥ 60%

### 维护性目标
- 相同模块路由集中管理
- 中间件配置标准化
- 开发规范文档完善

### 性能目标
- 路由加载时间优化
- 文件读取次数减少
- 内存占用降低

## 🚀 实施时间表

### 第1周：CRM模块合并
- 创建合并后的CRM路由文件
- 测试功能完整性
- 删除原有文件

### 第2周：其他模块合并
- 合并项目、工作流等模块
- 统一中间件配置
- 功能测试

### 第3周：规范建立
- 制定开发规范文档
- 团队培训
- 代码审查标准

### 第4周：验收测试
- 全功能测试
- 性能测试
- 文档完善

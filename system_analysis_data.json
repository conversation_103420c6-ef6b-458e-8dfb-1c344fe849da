{"generated_at": "2025-08-01 17:06:33", "controller_structure": {"crm": {"main_controllers": ["CrmBusinessController", "CrmBusinessProductController", "CrmContactController", "CrmContractController", "CrmContractProductController", "CrmContractReceivableController", "CrmCustomerMyController", "CrmCustomerSeaController", "CrmCustomerShareController", "CrmCustomerShareLogController", "CrmFollowRecordController", "CrmLeadController", "CrmLeadPoolController", "CrmProductCategoryController", "CrmProductController", "CrmProductSpecController", "CrmProductUnitController", "CrmSeaRuleController", "CrmStatisticsController", "CrmTagController", "CrmWorkReportController"], "sub_controllers": [], "total_count": 21}, "system": {"main_controllers": ["ArticleCategoryController", "ArticleController", "AttachmentCatController", "AttachmentController", "AuthController", "ConfigController", "SystemDictTypeController", "TenantSwitchController", "TenantSwitchTestController", "UploadController"], "sub_controllers": {"log": ["LoginController", "OperationController"], "permission": ["AdminController", "DepartmentController", "MenuController", "PostController", "RoleController"], "tenant": ["TenantConfigController", "TenantController"]}, "total_count": 19}, "project": {"main_controllers": ["ProjectController", "ProjectMemberController", "ProjectTaskController", "ProjectTaskRecordController"], "sub_controllers": [], "total_count": 4}, "workflow": {"main_controllers": ["ApplicationController", "CcController", "DefinitionController", "TaskController", "TypeController"], "sub_controllers": [], "total_count": 5}, "notice": {"main_controllers": ["MessageController", "NoticeTemplateController", "TemplateConfigController"], "sub_controllers": [], "total_count": 3}, "office": {"main_controllers": [], "sub_controllers": [], "total_count": 0}, "daily": {"main_controllers": ["DailyPriceOrderController"], "sub_controllers": [], "total_count": 1}, "ims": {"main_controllers": ["ImsSupplierController"], "sub_controllers": [], "total_count": 1}}, "route_analysis": {"route/Auth.php": {"exists": true, "total_lines": 25, "route_count": 5, "middleware_usage": [], "naming_patterns": []}, "route/System.php": {"exists": true, "total_lines": 111, "route_count": 62, "middleware_usage": {"permission": 1, "token": 1}, "naming_patterns": []}, "route/Crm.php": {"exists": false}, "route/Project.php": {"exists": false}, "route/Workflow.php": {"exists": true, "total_lines": 157, "route_count": 88, "middleware_usage": {"permission": 1, "token": 2}, "naming_patterns": []}, "route/Notice.php": {"exists": true, "total_lines": 76, "route_count": 29, "middleware_usage": {"permission": 1, "token": 1}, "naming_patterns": []}, "route/Office.php": {"exists": false}, "route/Daily.php": {"exists": false}, "route/Ims.php": {"exists": false}, "route/Router.php": {"exists": true, "total_lines": 51, "route_count": 20, "middleware_usage": {"token": 1}, "naming_patterns": []}}, "permission_patterns": {"module:controller:action": [{"name": "crm:crm_business:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_business:convert_contract", "title": "转合同", "type": 2, "count": 1}, {"name": "crm:crm_business:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_business:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:crm_business:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_business:transfer", "title": "阶段流转", "type": 2, "count": 1}, {"name": "crm:crm_business_product:index", "title": "CRM商机产品管理", "type": 1, "count": 1}, {"name": "crm:crm_contact:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_contact:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_contact:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:crm_contact:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_contact:export", "title": "导出", "type": 2, "count": 1}, {"name": "crm:crm_contact:import", "title": "导入", "type": 2, "count": 1}, {"name": "crm:crm_contact:index", "title": "联系人管理", "type": 1, "count": 1}, {"name": "crm:crm_contract:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_contract:approve", "title": "提交", "type": 2, "count": 1}, {"name": "crm:crm_contract:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_contract:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:crm_contract:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_contract:index", "title": "合同管理", "type": 1, "count": 1}, {"name": "crm:crm_contract:void", "title": "作废", "type": 2, "count": 1}, {"name": "crm:crm_contract_product:index", "title": "CRM合同产品管理", "type": 1, "count": 1}, {"name": "crm:crm_contract_receivable:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_contract_receivable:approve", "title": "提交", "type": 2, "count": 1}, {"name": "crm:crm_contract_receivable:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_contract_receivable:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:crm_contract_receivable:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_contract_receivable:index", "title": "回款管理", "type": 1, "count": 1}, {"name": "crm:crm_contract_receivable:void", "title": "作废", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:add_contact", "title": "新增联系人", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:add_contract", "title": "新增合同", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:add_follow", "title": "新增跟进", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:add_receivable", "title": "新增回款", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:add_receivable_more", "title": "新增回款", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:contact_list", "title": "联系人列表", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:contract_detail", "title": "合同详情", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:contract_list", "title": "合同列表", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:delete_contact", "title": "删除联系人", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:delete_contract", "title": "删除合同", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:delete_follow", "title": "删除跟进", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:delete_receivable", "title": "删除回款", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:edit_contact", "title": "编辑联系人", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:edit_contract", "title": "编辑合同", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:edit_follow", "title": "编辑跟进", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:edit_receivable", "title": "编辑回款", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:export", "title": "导出", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:follow_detail", "title": "跟进详情", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:import", "title": "导入", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:index", "title": "我的客户", "type": 1, "count": 1}, {"name": "crm:crm_customer_my:receivable_detail", "title": "回款详情", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:receivable_list", "title": "回款列表", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:recycle", "title": "回收", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:recycle_customer", "title": "回收客户", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:share", "title": "共享", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:share_customer", "title": "共享客户", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:submit_approval", "title": "提交审批", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:submit_receivable_approval", "title": "提交审批", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:transfer", "title": "转移", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:transfer_customer", "title": "转移客户", "type": 2, "count": 1}, {"name": "crm:crm_customer_my:withdraw_contract", "title": "合同撤回", "type": 2, "count": 1}, {"name": "crm:crm_customer_sea:index", "title": "CRM客户Sea管理", "type": 1, "count": 1}, {"name": "crm:crm_customer_share:index", "title": "CRM客户共享管理", "type": 1, "count": 1}, {"name": "crm:crm_customer_share_log:index", "title": "CRM客户共享日志管理", "type": 1, "count": 1}, {"name": "crm:crm_follow_record:index", "title": "跟进记录管理", "type": 1, "count": 1}, {"name": "crm:crm_lead:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_lead:assign", "title": "分配", "type": 2, "count": 1}, {"name": "crm:crm_lead:convert", "title": "转化", "type": 2, "count": 1}, {"name": "crm:crm_lead:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_lead:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:crm_lead:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_lead:export", "title": "导出", "type": 2, "count": 1}, {"name": "crm:crm_lead:follow", "title": "跟进", "type": 2, "count": 1}, {"name": "crm:crm_lead:import", "title": "导入", "type": 2, "count": 1}, {"name": "crm:crm_lead:index", "title": "我的线索", "type": 1, "count": 1}, {"name": "crm:crm_lead_pool:index", "title": "线索池管理", "type": 1, "count": 1}, {"name": "crm:crm_product:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_product:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_product:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:crm_product:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_product:export", "title": "导出", "type": 2, "count": 1}, {"name": "crm:crm_product:import", "title": "导入", "type": 2, "count": 1}, {"name": "crm:crm_product:index", "title": "产品列表", "type": 1, "count": 1}, {"name": "crm:crm_product:offline", "title": "下架", "type": 2, "count": 1}, {"name": "crm:crm_product:online", "title": "上架", "type": 2, "count": 1}, {"name": "crm:crm_product:set_price", "title": "设置价格", "type": 2, "count": 1}, {"name": "crm:crm_product_category:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_product_category:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_product_category:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_product_category:export", "title": "导出", "type": 2, "count": 1}, {"name": "crm:crm_product_category:import", "title": "导入", "type": 2, "count": 1}, {"name": "crm:crm_product_category:index", "title": "产品分类", "type": 1, "count": 1}, {"name": "crm:crm_product_spec:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_product_spec:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_product_spec:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_product_spec:index", "title": "产品规格", "type": 1, "count": 1}, {"name": "crm:crm_product_unit:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_product_unit:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:crm_product_unit:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_product_unit:index", "title": "产品单位", "type": 1, "count": 1}, {"name": "crm:crm_sea_rule:index", "title": "公海规则管理", "type": 1, "count": 1}, {"name": "crm:crm_statistics:index", "title": "统计分析", "type": 1, "count": 1}, {"name": "crm:crm_tag:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:crm_tag:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:crm_tag:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:crm_tag:index", "title": "标签管理", "type": 1, "count": 1}, {"name": "crm:crm_work_report:index", "title": "CRMWork报告管理", "type": 1, "count": 1}, {"name": "crm:customer_sea:assign", "title": "分配", "type": 2, "count": 1}, {"name": "crm:customer_sea:claim", "title": "认领", "type": 2, "count": 1}, {"name": "crm:customer_sea:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:customer_sea:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:customer_sea:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "crm:customer_sea:index", "title": "公海客户", "type": 1, "count": 1}, {"name": "crm:customer_sea:lock", "title": "锁定", "type": 2, "count": 1}, {"name": "crm:customer_sea:unlock", "title": "解锁", "type": 2, "count": 1}, {"name": "crm:follow:index", "title": "跟进记录", "type": 1, "count": 1}, {"name": "crm:follow_record:add", "title": "新增", "type": 2, "count": 1}, {"name": "crm:follow_record:delete", "title": "删除", "type": 2, "count": 1}, {"name": "crm:follow_record:detail", "title": "详情", "type": 2, "count": 1}, {"name": "crm:follow_record:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "daily:daily_price_order:add", "title": "新增", "type": 2, "count": 1}, {"name": "daily:daily_price_order:delete", "title": "删除", "type": 2, "count": 1}, {"name": "daily:daily_price_order:detail", "title": "详情", "type": 2, "count": 1}, {"name": "daily:daily_price_order:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "daily:daily_price_order:export", "title": "导出", "type": 2, "count": 1}, {"name": "daily:daily_price_order:index", "title": "每日报价", "type": 1, "count": 1}, {"name": "daily:daily_price_order:recallApproval", "title": "撤回", "type": 2, "count": 1}, {"name": "daily:daily_price_order:submitApproval", "title": "提交", "type": 2, "count": 1}, {"name": "daily:daily_price_order:voidOrder", "title": "作废", "type": 2, "count": 1}, {"name": "ims:ims_supplier:add", "title": "新增供应商", "type": 2, "count": 1}, {"name": "ims:ims_supplier:delete", "title": "删除供应商", "type": 2, "count": 1}, {"name": "ims:ims_supplier:detail", "title": "供应商详情", "type": 2, "count": 1}, {"name": "ims:ims_supplier:edit", "title": "编辑供应商", "type": 2, "count": 1}, {"name": "ims:ims_supplier:export", "title": "导出供应商", "type": 2, "count": 1}, {"name": "ims:ims_supplier:import", "title": "导入供应商", "type": 2, "count": 1}, {"name": "notice:message:batchDelete", "title": "批量删除", "type": 2, "count": 1}, {"name": "notice:message:delete", "title": "删除", "type": 2, "count": 1}, {"name": "notice:template:add", "title": "新增", "type": 2, "count": 1}, {"name": "notice:template:delete", "title": "删除", "type": 2, "count": 1}, {"name": "notice:template:detail", "title": "详情", "type": 2, "count": 1}, {"name": "notice:template:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "notice:template:index", "title": "消息模板", "type": 1, "count": 1}, {"name": "notice:template:preview", "title": "预览", "type": 2, "count": 1}, {"name": "notice:template:status", "title": "状态", "type": 2, "count": 1}, {"name": "notice:tenant:templateConfig", "title": "模板配置", "type": 1, "count": 1}, {"name": "project:project:add", "title": "新增", "type": 2, "count": 1}, {"name": "project:project:addmember", "title": "添加成员", "type": 2, "count": 1}, {"name": "project:project:delete", "title": "删除", "type": 2, "count": 1}, {"name": "project:project:detail", "title": "详情", "type": 2, "count": 1}, {"name": "project:project:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "project:project:index", "title": "Project管理", "type": 1, "count": 1}, {"name": "project:project:index", "title": "项目列表", "type": 1, "count": 1}, {"name": "project:project:projectdetail", "title": "项目详情", "type": 1, "count": 1}, {"name": "project:project:remove-member", "title": "移除成员", "type": 2, "count": 1}, {"name": "project:project_member:detail", "title": "查看成员", "type": 2, "count": 1}, {"name": "project:project_member:index", "title": "ProjectMember管理", "type": 1, "count": 1}, {"name": "project:project_task:add", "title": "新增任务", "type": 2, "count": 1}, {"name": "project:project_task:delete", "title": "删除任务", "type": 2, "count": 1}, {"name": "project:project_task:detail", "title": "详情", "type": 2, "count": 1}, {"name": "project:project_task:edit", "title": "编辑任务", "type": 2, "count": 1}, {"name": "project:project_task:index", "title": "ProjectTask管理", "type": 1, "count": 1}, {"name": "project:project_task:mytasks", "title": "我的任务", "type": 2, "count": 1}, {"name": "project:project_task_record:index", "title": "项目任务记录管理", "type": 1, "count": 1}, {"name": "project:task:index", "title": "任务管理", "type": 1, "count": 1}, {"name": "system:article:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:article:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:article:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:article:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:article:index", "title": "SystemArticle管理", "type": 1, "count": 1}, {"name": "system:article:index", "title": "列表管理", "type": 1, "count": 1}, {"name": "system:article:updateField", "title": "更新字段", "type": 2, "count": 1}, {"name": "system:article_category:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:article_category:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:article_category:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:article_category:index", "title": "SystemArticleCategory管理", "type": 1, "count": 1}, {"name": "system:article_category:index", "title": "分类管理", "type": 1, "count": 1}, {"name": "system:article_category:options", "title": "获取选项", "type": 2, "count": 1}, {"name": "system:article_category:updateField", "title": "更新字段", "type": 2, "count": 1}, {"name": "system:attachment:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:attachment:index", "title": "SystemAttachment管理", "type": 1, "count": 1}, {"name": "system:attachment:index", "title": "附件管理", "type": 1, "count": 1}, {"name": "system:attachment:move", "title": "移动分类", "type": 2, "count": 1}, {"name": "system:attachmentCat:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:attachmentCat:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:attachmentCat:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:attachment_cat:index", "title": "SystemAttachmentCat管理", "type": 1, "count": 1}, {"name": "system:auth:index", "title": "SystemAuth管理", "type": 1, "count": 1}, {"name": "system:config:detail", "title": "总后台配置", "type": 1, "count": 1}, {"name": "system:config:index", "title": "SystemConfig管理", "type": 1, "count": 1}, {"name": "system:config:save", "title": "设置", "type": 2, "count": 1}, {"name": "system:dict_type:index", "title": "SystemDictType管理", "type": 1, "count": 1}, {"name": "system:inventory:inbound", "title": "入库", "type": 1, "count": 1}, {"name": "system:inventory:outbound", "title": "出库", "type": 1, "count": 1}, {"name": "system:inventory:stocktaking", "title": "盘点", "type": 1, "count": 1}, {"name": "system:inventory:warehouse", "title": "仓库管理", "type": 1, "count": 1}, {"name": "system:log_login:index", "title": "日志Login管理", "type": 1, "count": 1}, {"name": "system:log_operation:index", "title": "日志Operation管理", "type": 1, "count": 1}, {"name": "system:permission:index", "title": "权限管理", "type": 1, "count": 1}, {"name": "system:permission_admin:index", "title": "PermissionAdmin管理", "type": 1, "count": 1}, {"name": "system:permission_department:index", "title": "PermissionDepartment管理", "type": 1, "count": 1}, {"name": "system:permission_menu:index", "title": "PermissionMenu管理", "type": 1, "count": 1}, {"name": "system:permission_post:index", "title": "PermissionPost管理", "type": 1, "count": 1}, {"name": "system:permission_role:index", "title": "PermissionRole管理", "type": 1, "count": 1}, {"name": "system:tenant:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:tenant:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:tenant:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:tenant:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:tenant:index", "title": "租户列表", "type": 1, "count": 1}, {"name": "system:tenantPackage:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:tenantPackage:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:tenantPackage:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:tenantPackage:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:tenantPackage:index", "title": "租户套餐", "type": 1, "count": 1}, {"name": "system:tenant_switch:index", "title": "SystemTenantSwitch管理", "type": 1, "count": 1}, {"name": "system:tenant_switch_test:index", "title": "SystemTenantSwitchTest管理", "type": 1, "count": 1}, {"name": "system:tenant_tenant:index", "title": "TenantTenant管理", "type": 1, "count": 1}, {"name": "system:tenant_tenant_config:index", "title": "TenantTenantConfig管理", "type": 1, "count": 1}, {"name": "system:upload:index", "title": "SystemUpload管理", "type": 1, "count": 1}, {"name": "system:user:attendance_data", "title": "考勤统计", "type": 1, "count": 1}, {"name": "user:attendance:attendance_config", "title": "考勤设置", "type": 1, "count": 1}, {"name": "workflow:application:create", "title": "新增", "type": 2, "count": 1}, {"name": "workflow:application:delete", "title": "删除", "type": 2, "count": 1}, {"name": "workflow:application:detail", "title": "详情", "type": 2, "count": 1}, {"name": "workflow:application:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "workflow:application:index", "title": "我的申请", "type": 1, "count": 1}, {"name": "workflow:application:submit", "title": "提交", "type": 2, "count": 1}, {"name": "workflow:application:urge", "title": "催办", "type": 2, "count": 1}, {"name": "workflow:application:void", "title": "作废", "type": 2, "count": 1}, {"name": "workflow:application:withdraw", "title": "撤回", "type": 2, "count": 1}, {"name": "workflow:cc:detail", "title": "详情", "type": 2, "count": 1}, {"name": "workflow:cc:index", "title": "抄送我的", "type": 1, "count": 1}, {"name": "workflow:definition:add", "title": "新增", "type": 2, "count": 1}, {"name": "workflow:definition:delete", "title": "删除", "type": 2, "count": 1}, {"name": "workflow:definition:design", "title": "设计表单", "type": 2, "count": 1}, {"name": "workflow:definition:detail", "title": "详情", "type": 2, "count": 1}, {"name": "workflow:definition:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "workflow:definition:index", "title": "流程列表", "type": 1, "count": 1}, {"name": "workflow:formType:add", "title": "新增", "type": 2, "count": 1}, {"name": "workflow:formType:delete", "title": "删除", "type": 2, "count": 1}, {"name": "workflow:formType:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "workflow:formType:index", "title": "流程类型", "type": 1, "count": 1}, {"name": "workflow:task:approve", "title": "同意", "type": 2, "count": 1}, {"name": "workflow:task:detail", "title": "详情", "type": 2, "count": 1}, {"name": "workflow:task:index", "title": "我的审批", "type": 1, "count": 1}, {"name": "workflow:task:reject", "title": "驳回", "type": 2, "count": 1}, {"name": "workflow:task:terminate", "title": "终止", "type": 2, "count": 1}, {"name": "workflow:task:transfer", "title": "转交", "type": 2, "count": 1}], "module:sub:controller:action": [{"name": "project:task:comment:add", "title": "添加评论", "type": 2, "count": 1}, {"name": "project:task:comment:delete", "title": "删除评论", "type": 2, "count": 1}, {"name": "project:task:comment:edit", "title": "编辑评论", "type": 2, "count": 1}, {"name": "project:task:follow:add", "title": "添加跟进", "type": 2, "count": 1}, {"name": "project:task:follow:delete", "title": "删除跟进", "type": 2, "count": 1}, {"name": "project:task:follow:edit", "title": "编辑跟进", "type": 2, "count": 1}, {"name": "system:ims:supplier:index", "title": "供应商管理", "type": 1, "count": 1}, {"name": "system:log:login:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:log:login:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:log:login:index", "title": "登录日志", "type": 1, "count": 1}, {"name": "system:log:operation:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:log:operation:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:log:operation:index", "title": "操作日志", "type": 1, "count": 1}, {"name": "system:permission:admin:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:permission:admin:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:permission:admin:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:permission:admin:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:permission:admin:index", "title": "管理员管理", "type": 1, "count": 1}, {"name": "system:permission:admin:reset_password", "title": "重置密码", "type": 2, "count": 1}, {"name": "system:permission:department:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:permission:department:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:permission:department:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:permission:department:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:permission:department:index", "title": "部门管理", "type": 1, "count": 1}, {"name": "system:permission:menu:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:permission:menu:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:permission:menu:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:permission:menu:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:permission:menu:index", "title": "菜单管理", "type": 1, "count": 1}, {"name": "system:permission:post:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:permission:post:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:permission:post:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:permission:post:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:permission:post:index", "title": "岗位管理", "type": 1, "count": 1}, {"name": "system:permission:role:add", "title": "新增", "type": 2, "count": 1}, {"name": "system:permission:role:delete", "title": "删除", "type": 2, "count": 1}, {"name": "system:permission:role:detail", "title": "详情", "type": 2, "count": 1}, {"name": "system:permission:role:edit", "title": "编辑", "type": 2, "count": 1}, {"name": "system:permission:role:index", "title": "角色管理", "type": 1, "count": 1}, {"name": "system:tenant:config:detail", "title": "系统配置", "type": 1, "count": 1}, {"name": "system:tenant:config:save", "title": "编辑", "type": 2, "count": 1}, {"name": "system:user:attendance_data:daily", "title": "每日统计", "type": 1, "count": 1}], "module:controller": [{"name": "crm:index", "title": "客户管理", "type": 1, "count": 1}, {"name": "crm:product", "title": "产品管理", "type": 1, "count": 1}, {"name": "crm:work_report/index", "title": "工作汇报", "type": 1, "count": 1}, {"name": "office:attendance", "title": "考勤", "type": 1, "count": 1}, {"name": "office:console", "title": "工作台", "type": 1, "count": 1}, {"name": "office:workflow", "title": "办公审批", "type": 1, "count": 1}, {"name": "system:attachmentCat", "title": "附件分类", "type": 1, "count": 1}, {"name": "system:ims", "title": "进销存", "type": 1, "count": 1}, {"name": "system:tenant", "title": "租户管理", "type": 1, "count": 1}], "single_word": [{"name": "article", "title": "公告文章", "type": 1, "count": 1}, {"name": "log", "title": "日志管理", "type": 1, "count": 1}, {"name": "message", "title": "消息中心", "type": 1, "count": 1}, {"name": "notice", "title": "消息管理", "type": 1, "count": 1}, {"name": "office", "title": "办公", "type": 1, "count": 1}, {"name": "project", "title": "项目管理", "type": 1, "count": 1}, {"name": "system", "title": "系统管理", "type": 1, "count": 1}, {"name": "UserCenter", "title": "个人中心", "type": 1, "count": 1}, {"name": "workflow", "title": "流程配置", "type": 1, "count": 1}], "inconsistent": [{"name": "system:user:attendance:attendance_data:clockIn", "title": "打卡记录", "type": 1, "count": 1}, {"name": "system:user:attendance:attendance_data:monthly", "title": "月度统计", "type": 1, "count": 1}]}, "module_permissions": {"crm": {"three_parts": 123, "four_parts": 0, "two_parts": 3, "total": 126}, "daily": {"three_parts": 9, "four_parts": 0, "two_parts": 0, "total": 9}, "ims": {"three_parts": 6, "four_parts": 0, "two_parts": 0, "total": 6}, "notice": {"three_parts": 10, "four_parts": 0, "two_parts": 0, "total": 10}, "office": {"three_parts": 0, "four_parts": 0, "two_parts": 3, "total": 3}, "project": {"three_parts": 19, "four_parts": 6, "two_parts": 0, "total": 25}, "system": {"three_parts": 55, "four_parts": 36, "two_parts": 3, "total": 96}, "user": {"three_parts": 1, "four_parts": 0, "two_parts": 0, "total": 1}, "workflow": {"three_parts": 27, "four_parts": 0, "two_parts": 0, "total": 27}}, "total_permissions": 312, "total_controllers": 54}
<template>
  <div class="deleted-user-test">
    <h2>已删除/停用用户显示测试</h2>
    
    <el-card>
      <h3>测试场景</h3>
      <p>模拟用户被删除或停用后，在出差申请中仍需显示其姓名的情况</p>
      
      <el-button @click="loadCompleteData" type="primary">加载完整数据（有姓名）</el-button>
      <el-button @click="loadIncompleteData" type="warning">加载简化数据（仅ID）</el-button>
      <el-button @click="clearData" type="danger">清空数据</el-button>
      
      <div style="margin-top: 15px;">
        <h4>当前测试数据:</h4>
        <pre>{{ JSON.stringify(testFormData.companions, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card>
      <h3>出差表单组件测试</h3>
      <div style="border: 1px solid #ddd; padding: 20px; margin: 10px 0;">
        <el-form-item label="同行人">
          <DepartmentPersonForm
            v-model="companionsForDisplay"
            placeholder="请选择同行人"
            :multiple="true"
            style="width: 100%"
          />
        </el-form-item>
      </div>
      
      <div>
        <h4>companionsForDisplay 值:</h4>
        <pre>{{ JSON.stringify(companionsForDisplay, null, 2) }}</pre>
      </div>
      
      <div>
        <h4>期望效果:</h4>
        <ul>
          <li><strong>完整数据</strong>：立即显示用户姓名，无骨架屏</li>
          <li><strong>简化数据</strong>：显示骨架屏，尝试API加载（会失败）</li>
        </ul>
      </div>
    </el-card>

    <el-card>
      <h3>出差详情组件测试</h3>
      <div style="border: 1px solid #ddd; padding: 20px; margin: 10px 0;">
        <el-descriptions-item label="同行人">
          <template v-if="companionsDisplay.length > 0">
            <el-tag 
              v-for="companion in companionsDisplay" 
              :key="companion.id"
              type="info"
              style="margin-right: 8px; margin-bottom: 4px;"
            >
              {{ companion.name }}
            </el-tag>
          </template>
          <span v-else style="color: #909399;">无同行人</span>
        </el-descriptions-item>
      </div>
      
      <div>
        <h4>companionsDisplay 值:</h4>
        <pre>{{ JSON.stringify(companionsDisplay, null, 2) }}</pre>
      </div>
      
      <div>
        <h4>期望效果:</h4>
        <ul>
          <li><strong>完整数据</strong>：显示用户姓名标签</li>
          <li><strong>简化数据</strong>：显示"无同行人"</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import DepartmentPersonForm from '@/components/custom/DepartmentPersonForm.vue'

// 测试表单数据
const testFormData = reactive({
  companions: []
})

// 模拟完整的用户数据（包含已删除/停用用户的姓名）
const completeUserData = [
  {
    "status_text": "正常",
    "gender_text": "男", 
    "name": "张三（已删除）",
    "id": 999,
    "status": 0,
    "gender": 1
  },
  {
    "status_text": "停用",
    "gender_text": "女",
    "name": "李四（已停用）", 
    "id": 888,
    "status": 0,
    "gender": 2
  }
]

// 模拟简化的用户数据（仅ID，API无法获取详情）
const incompleteUserData = [
  { id: 999 },
  { id: 888 }
]

/**
 * 同行人数据的计算属性（复制自出差表单）
 */
const companionsForDisplay = computed({
  get: () => {
    if (!Array.isArray(testFormData.companions)) {
      return []
    }

    // 检查是否有完整的用户数据（包含name字段）
    const hasCompleteData = testFormData.companions.some((companion: any) =>
      companion && typeof companion === 'object' && companion.name
    )

    if (hasCompleteData) {
      // 如果有完整数据，直接传递给组件，组件会自动缓存
      console.log('使用详情中的完整用户数据:', testFormData.companions)
      return testFormData.companions
    } else {
      // 如果只有简化数据（只有id），让组件通过API加载
      console.log('使用简化数据，组件将通过API加载:', testFormData.companions)
      return testFormData.companions
    }
  },
  set: (value: any[]) => {
    console.log('同行人数据更新:', value)
    
    // 保存时转换为简化格式（只保留id）
    if (Array.isArray(value)) {
      testFormData.companions = value.map((companion: any) => ({
        id: companion.id
      }))
    } else {
      testFormData.companions = []
    }
  }
})

/**
 * 同行人显示数据（复制自出差详情组件）
 */
const companionsDisplay = computed(() => {
  const companions = testFormData.companions
  console.log('出差详情 - 同行人数据:', companions)
  
  if (!Array.isArray(companions)) {
    return []
  }

  // 过滤出有姓名的用户数据
  return companions
    .filter((companion: any) => 
      companion && 
      typeof companion === 'object' && 
      companion.id && 
      companion.name
    )
    .map((companion: any) => ({
      id: companion.id,
      name: companion.name
    }))
})

// 测试方法
const loadCompleteData = () => {
  testFormData.companions = [...completeUserData]
  ElMessage.success('已加载完整数据（包含已删除用户姓名）')
}

const loadIncompleteData = () => {
  testFormData.companions = [...incompleteUserData]
  ElMessage.warning('已加载简化数据（仅ID，API无法获取详情）')
}

const clearData = () => {
  testFormData.companions = []
  ElMessage.info('数据已清空')
}

onMounted(() => {
  ElMessage.info('已删除/停用用户显示测试组件已加载')
})
</script>

<style scoped>
.deleted-user-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.deleted-user-test .el-card {
  margin-bottom: 20px;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #409eff;
}

h3, h4 {
  margin-bottom: 15px;
  color: #303133;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

ul {
  margin: 10px 0;
  padding-left: 20px;
}

li {
  margin-bottom: 5px;
}
</style>

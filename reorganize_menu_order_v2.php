<?php
/**
 * 重新整理权限菜单ID顺序 v2
 * 使用更合理的ID范围，避免超出数据库字段限制
 */

require_once 'vendor/autoload.php';

echo "=== 重新整理权限菜单ID顺序 v2 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取当前权限菜单:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type, status
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $allMenus = $stmt->fetchAll();
    
    echo "  当前权限总数: " . count($allMenus) . " 个\n\n";
    
    echo "2. 按模块分组并分配新的ID范围:\n";
    
    // 按模块分组
    $moduleGroups = [];
    foreach ($allMenus as $menu) {
        $parts = explode(':', $menu['name']);
        $module = $parts[0] ?? 'unknown';
        
        if (!isset($moduleGroups[$module])) {
            $moduleGroups[$module] = [];
        }
        $moduleGroups[$module][] = $menu;
    }
    
    // 定义新的ID范围 (使用较小的数字，避免超出限制)
    $moduleIdRanges = [
        'system' => ['start' => 1000, 'step' => 1],      // 1000-1099
        'crm' => ['start' => 1100, 'step' => 1],         // 1100-1299 (CRM权限较多)
        'project' => ['start' => 1300, 'step' => 1],     // 1300-1349
        'workflow' => ['start' => 1350, 'step' => 1],    // 1350-1399
        'notice' => ['start' => 1400, 'step' => 1],      // 1400-1419
        'office' => ['start' => 1420, 'step' => 1],      // 1420-1429
        'daily' => ['start' => 1430, 'step' => 1],       // 1430-1449
        'ims' => ['start' => 1450, 'step' => 1],         // 1450-1469
    ];
    
    // 生成重新分配映射
    $reorderMappings = [];
    
    foreach ($moduleGroups as $module => $menus) {
        if (!isset($moduleIdRanges[$module])) {
            echo "  ⚠️ 模块 {$module} 未定义ID范围，跳过\n";
            continue;
        }
        
        $startId = $moduleIdRanges[$module]['start'];
        $step = $moduleIdRanges[$module]['step'];
        
        // 按权限名称排序，确保逻辑顺序
        usort($menus, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        echo "  {$module}模块: " . count($menus) . "个权限，ID范围 {$startId}+\n";
        
        foreach ($menus as $index => $menu) {
            $newId = $startId + ($index * $step);
            
            $reorderMappings[] = [
                'old_id' => $menu['id'],
                'new_id' => $newId,
                'name' => $menu['name'],
                'title' => $menu['title'],
                'module' => $module
            ];
        }
    }
    
    echo "\n  总共需要重新分配: " . count($reorderMappings) . " 个权限\n\n";
    
    echo "3. 开始重新分配ID:\n";
    
    if (empty($reorderMappings)) {
        echo "  ✅ 没有需要重新分配的权限\n";
        return;
    }
    
    $pdo->beginTransaction();
    
    try {
        // 第一步：将所有ID临时设置为负数，避免主键冲突
        echo "  第一步：临时设置负数ID...\n";
        foreach ($reorderMappings as $mapping) {
            $stmt = $pdo->prepare("UPDATE system_menu SET id = ? WHERE id = ?");
            $stmt->execute([-$mapping['old_id'], $mapping['old_id']]);
        }
        
        // 第二步：设置为新的ID
        echo "  第二步：设置新的ID...\n";
        $successCount = 0;
        foreach ($reorderMappings as $mapping) {
            $stmt = $pdo->prepare("UPDATE system_menu SET id = ? WHERE id = ?");
            $stmt->execute([$mapping['new_id'], -$mapping['old_id']]);
            
            if ($successCount < 10) { // 只显示前10个
                echo "    ✅ ID:{$mapping['old_id']} -> {$mapping['new_id']} {$mapping['name']}\n";
            } elseif ($successCount == 10) {
                echo "    ... 继续处理剩余权限\n";
            }
            $successCount++;
        }
        
        // 第三步：更新相关联表的外键
        echo "  第三步：更新关联表外键...\n";
        
        // 先临时设置负数
        foreach ($reorderMappings as $mapping) {
            $stmt = $pdo->prepare("UPDATE system_role_menu SET menu_id = ? WHERE menu_id = ?");
            $stmt->execute([-$mapping['old_id'], $mapping['old_id']]);
        }
        
        // 再设置为新ID
        foreach ($reorderMappings as $mapping) {
            $stmt = $pdo->prepare("UPDATE system_role_menu SET menu_id = ? WHERE menu_id = ?");
            $stmt->execute([$mapping['new_id'], -$mapping['old_id']]);
        }
        
        echo "    ✅ 更新 system_role_menu 表完成\n";
        
        // 重置自增ID
        $maxNewId = max(array_column($reorderMappings, 'new_id'));
        $nextAutoIncrement = $maxNewId + 1;
        $stmt = $pdo->prepare("ALTER TABLE system_menu AUTO_INCREMENT = ?");
        $stmt->execute([$nextAutoIncrement]);
        echo "    ✅ 重置自增ID为: {$nextAutoIncrement}\n";
        
        $pdo->commit();
        
        echo "\n  ✅ 成功重新分配 {$successCount} 个权限ID\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
    echo "\n4. 验证重新分配结果:\n";
    
    // 按模块统计新的ID分布
    foreach ($moduleIdRanges as $module => $range) {
        if (!isset($moduleGroups[$module])) continue;
        
        $count = count($moduleGroups[$module]);
        $startId = $range['start'];
        $endId = $startId + $count - 1;
        
        echo "  {$module}模块: {$count}个权限，ID范围 {$startId}-{$endId}\n";
    }
    
    echo "\n5. 显示重新排序后的权限 (每个模块前5个):\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $reorderedMenus = $stmt->fetchAll();
    
    $currentModule = '';
    $moduleCount = 0;
    
    foreach ($reorderedMenus as $menu) {
        $parts = explode(':', $menu['name']);
        $module = $parts[0] ?? 'unknown';
        
        if ($module !== $currentModule) {
            $currentModule = $module;
            $moduleCount = 0;
            echo "\n  {$module}模块:\n";
        }
        
        if ($moduleCount < 5) {
            echo "    ID:{$menu['id']} {$menu['name']} ({$menu['title']})\n";
        } elseif ($moduleCount == 5) {
            $totalInModule = count(array_filter($reorderedMenus, function($m) use ($module) {
                return strpos($m['name'], $module . ':') === 0;
            }));
            echo "    ... 还有 " . ($totalInModule - 5) . " 个权限\n";
        }
        
        $moduleCount++;
    }
    
    echo "\n🎉 权限菜单ID重新排序完成！\n";
    echo "  现在所有权限都按照模块逻辑顺序排列，便于管理和维护。\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 菜单重新排序完成 ===\n";

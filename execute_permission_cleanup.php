<?php
/**
 * 权限清理脚本 - 删除冗余的列表权限
 * 执行方案2：删除列表按钮权限，优化权限结构
 */

require_once 'vendor/autoload.php';

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
        ]
    );
    
    echo "=== 权限清理脚本开始执行 ===\n\n";
    
    // 步骤1：验证菜单权限标识更新结果
    echo "1. 验证菜单权限标识更新结果:\n";
    $stmt = $pdo->query("
        SELECT id, title, name, type 
        FROM system_menu 
        WHERE id IN (2,3,5,7,8,29,47,60,66,75,121,134,156) 
        ORDER BY id
    ");
    $updatedMenus = $stmt->fetchAll();
    
    foreach ($updatedMenus as $menu) {
        $status = strpos($menu['name'], ':index') !== false ? '✅' : '❌';
        echo "  {$status} ID:{$menu['id']} - {$menu['title']} - {$menu['name']}\n";
    }
    echo "\n";
    
    // 步骤2：查看需要删除的列表权限
    echo "2. 查看需要删除的列表权限:\n";
    $stmt = $pdo->query("
        SELECT id, parent_id, title, name, type 
        FROM system_menu 
        WHERE type = 2 AND name LIKE '%:index' 
        ORDER BY parent_id, id
    ");
    $listPermissions = $stmt->fetchAll();
    
    echo "  需要删除的列表权限数量: " . count($listPermissions) . "\n";
    foreach ($listPermissions as $perm) {
        echo "  - ID:{$perm['id']} - {$perm['title']} - {$perm['name']}\n";
    }
    echo "\n";
    
    // 步骤3：查看这些权限被哪些角色使用
    echo "3. 查看列表权限的角色分配情况:\n";
    $listPermissionIds = array_column($listPermissions, 'id');
    if (!empty($listPermissionIds)) {
        $placeholders = str_repeat('?,', count($listPermissionIds) - 1) . '?';
        $stmt = $pdo->prepare("
            SELECT rm.menu_id, rm.role_id, r.name as role_name, m.title as menu_title
            FROM system_role_menu rm
            LEFT JOIN system_role r ON rm.role_id = r.id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE rm.menu_id IN ($placeholders)
            ORDER BY rm.role_id, rm.menu_id
        ");
        $stmt->execute($listPermissionIds);
        $roleAssignments = $stmt->fetchAll();
        
        echo "  受影响的角色权限分配数量: " . count($roleAssignments) . "\n";
        $roleGroups = [];
        foreach ($roleAssignments as $assignment) {
            $roleGroups[$assignment['role_name']][] = $assignment;
        }
        
        foreach ($roleGroups as $roleName => $assignments) {
            echo "  角色: {$roleName} (受影响权限: " . count($assignments) . "个)\n";
            foreach ($assignments as $assignment) {
                echo "    - {$assignment['menu_title']} (ID:{$assignment['menu_id']})\n";
            }
        }
    }
    echo "\n";
    
    // 步骤4：执行删除操作（需要确认）
    echo "4. 准备执行删除操作...\n";
    echo "是否继续删除列表权限？这将:\n";
    echo "  - 删除 " . count($listPermissions) . " 个列表类型的按钮权限\n";
    echo "  - 删除对应的角色权限分配\n";
    echo "  - 菜单权限已更新为包含:index的完整权限标识\n\n";
    
    // 自动执行删除（在脚本中直接执行）
    echo "开始执行删除操作...\n";
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        // 删除角色菜单关联
        if (!empty($listPermissionIds)) {
            $stmt = $pdo->prepare("DELETE FROM system_role_menu WHERE menu_id IN ($placeholders)");
            $deletedRoleMenus = $stmt->execute($listPermissionIds);
            echo "  ✅ 删除角色菜单关联: " . $stmt->rowCount() . " 条\n";
        }
        
        // 删除列表权限菜单
        $stmt = $pdo->prepare("DELETE FROM system_menu WHERE type = 2 AND name LIKE '%:index'");
        $stmt->execute();
        $deletedMenus = $stmt->rowCount();
        echo "  ✅ 删除列表权限菜单: {$deletedMenus} 条\n";
        
        // 提交事务
        $pdo->commit();
        echo "  ✅ 事务提交成功\n\n";
        
        // 步骤5：验证删除结果
        echo "5. 验证删除结果:\n";
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM system_menu WHERE type = 2 AND name LIKE '%:index'");
        $remainingCount = $stmt->fetch()['count'];
        echo "  剩余列表权限数量: {$remainingCount}\n";
        
        if ($remainingCount == 0) {
            echo "  ✅ 所有列表权限已成功删除\n";
        } else {
            echo "  ⚠️ 仍有 {$remainingCount} 个列表权限未删除\n";
        }
        
        // 验证菜单权限标识
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM system_menu 
            WHERE type = 1 AND name LIKE '%:index'
        ");
        $menuWithIndex = $stmt->fetch()['count'];
        echo "  包含:index的菜单权限数量: {$menuWithIndex}\n\n";
        
        echo "=== 权限清理完成 ===\n";
        echo "✅ 成功删除冗余的列表权限\n";
        echo "✅ 菜单权限标识已优化\n";
        echo "✅ 权限结构更加合理\n\n";
        
        echo "下一步建议:\n";
        echo "1. 重新为角色分配菜单权限\n";
        echo "2. 测试权限验证功能\n";
        echo "3. 优化权限解析逻辑\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "  ❌ 删除操作失败: " . $e->getMessage() . "\n";
        throw $e;
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}

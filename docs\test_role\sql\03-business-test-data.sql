-- =====================================================
-- 权限测试 - 业务测试数据创建脚本
-- 创建日期：2025-01-31
-- 说明：基于实际表结构创建CRM客户、线索等业务数据用于数据权限测试
-- =====================================================

SELECT '=== 开始创建权限测试业务数据 ===' as message;

-- =====================================================
-- 1. CRM客户测试数据
-- 基于实际 crm_customer 表结构创建测试数据
-- =====================================================

SELECT '1. 创建CRM客户测试数据' as step;

-- 清理现有测试数据
DELETE FROM crm_customer WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209);

-- 创建客户测试数据（使用实际字段名）
INSERT INTO `crm_customer` (
    `tenant_id`, `customer_name`, `industry`, `level`, `source`, `phone`, `website`,
    `region_province`, `region_city`, `region_district`, `address`, `remark`,
    `owner_user_id`, `status`, `creator_id`, `created_at`, `updated_at`
) VALUES
-- 销售部经理创建的客户 (user_id=202, dept_id=102)
(1, '客户A-销售部经理', '互联网', 1, '网络推广', '13800001001', 'www.customerA.com', '陕西省', '西安市', '雁塔区', '高新区科技路1号', '销售部经理创建的重要客户', 202, 1, 202, NOW(), NOW()),
(1, '客户B-销售部经理', '制造业', 2, '电话营销', '13800001002', 'www.customerB.com', '陕西省', '西安市', '碑林区', '南二环东段2号', '销售部经理创建的普通客户', 202, 1, 202, NOW(), NOW()),

-- 销售一组组长创建的客户 (user_id=203, dept_id=103)
(1, '客户C-销售一组长', '金融', 1, '朋友介绍', '13800001003', 'www.customerC.com', '陕西省', '西安市', '新城区', '东大街3号', '销售一组长创建的重要客户', 203, 1, 203, NOW(), NOW()),
(1, '客户D-销售一组长', '教育', 2, '展会', '13800001004', 'www.customerD.com', '陕西省', '西安市', '莲湖区', '西大街4号', '销售一组长创建的教育客户', 203, 1, 203, NOW(), NOW()),

-- 销售一组员工创建的客户 (user_id=204, dept_id=103)
(1, '客户E-销售一组员工', '医疗', 1, '网络推广', '13800001005', 'www.customerE.com', '陕西省', '西安市', '未央区', '凤城路5号', '销售一组员工创建的医疗客户', 204, 1, 204, NOW(), NOW()),
(1, '客户F-销售一组员工', '零售', 3, '老客户推荐', '13800001006', 'www.customerF.com', '陕西省', '西安市', '灞桥区', '纺织城6号', '销售一组员工创建的零售客户', 204, 1, 204, NOW(), NOW()),

-- 销售二组员工创建的客户 (user_id=205, dept_id=104)
(1, '客户G-销售二组员工', '物流', 1, '电话营销', '13800001007', 'www.customerG.com', '陕西省', '西安市', '长安区', '韦曲街道7号', '销售二组员工创建的物流客户', 205, 1, 205, NOW(), NOW()),
(1, '客户H-销售二组员工', '房地产', 2, '网络推广', '13800001008', 'www.customerH.com', '陕西省', '西安市', '高陵区', '鹿苑大道8号', '销售二组员工创建的房地产客户', 205, 1, 205, NOW(), NOW()),

-- 技术部经理创建的客户 (user_id=206, dept_id=105)
(1, '客户I-技术部经理', '软件', 1, '技术合作', '13800001009', 'www.customerI.com', '陕西省', '西安市', '雁塔区', '软件园9号', '技术部经理创建的软件客户', 206, 1, 206, NOW(), NOW()),

-- 技术部员工创建的客户 (user_id=207, dept_id=106)
(1, '客户J-技术部员工', '硬件', 2, '技术交流', '13800001010', 'www.customerJ.com', '陕西省', '西安市', '高新区', '科技二路10号', '技术部员工创建的硬件客户', 207, 1, 207, NOW(), NOW()),

-- 财务部员工创建的客户 (user_id=208, dept_id=108)
(1, '客户K-财务部员工', '咨询', 1, '财务咨询', '13800001011', 'www.customerK.com', '陕西省', '西安市', '碑林区', '财富中心11号', '财务部员工创建的咨询客户', 208, 1, 208, NOW(), NOW()),

-- 自定义权限用户创建的客户 (user_id=209, dept_id=105)
(1, '客户L-自定义权限', '广告', 3, '广告合作', '13800001012', 'www.customerL.com', '陕西省', '西安市', '新城区', '广告大厦12号', '自定义权限用户创建的广告客户', 209, 1, 209, NOW(), NOW());

-- =====================================================
-- 2. CRM线索测试数据
-- 基于实际 crm_lead 表结构创建测试数据
-- =====================================================

SELECT '2. 创建CRM线索测试数据' as step;

-- 清理现有测试数据
DELETE FROM crm_lead WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209);

-- 创建线索测试数据（使用实际字段名）
INSERT INTO `crm_lead` (
    `tenant_id`, `lead_name`, `industry`, `source`, `phone`, `email`, `website`,
    `region_province`, `region_city`, `region_district`, `address`, `remark`,
    `owner_user_id`, `status`, `creator_id`, `created_at`, `updated_at`
) VALUES
-- 不同用户创建的线索数据
(1, '线索A-销售经理', '互联网', '网络推广', '13900001001', '<EMAIL>', 'www.leadA.com', '陕西省', '西安市', '雁塔区', '科技路线索A地址', '销售经理创建的线索', 202, 1, 202, NOW(), NOW()),
(1, '线索B-销售组长', '制造业', '电话营销', '13900001002', '<EMAIL>', 'www.leadB.com', '陕西省', '西安市', '碑林区', '南二环线索B地址', '销售组长创建的线索', 203, 1, 203, NOW(), NOW()),
(1, '线索C-销售员工1', '金融', '朋友介绍', '13900001003', '<EMAIL>', 'www.leadC.com', '陕西省', '西安市', '新城区', '东大街线索C地址', '销售员工1创建的线索', 204, 1, 204, NOW(), NOW()),
(1, '线索D-销售员工2', '教育', '展会', '13900001004', '<EMAIL>', 'www.leadD.com', '陕西省', '西安市', '莲湖区', '西大街线索D地址', '销售员工2创建的线索', 205, 1, 205, NOW(), NOW()),
(1, '线索E-技术经理', '软件', '技术合作', '13900001005', '<EMAIL>', 'www.leadE.com', '陕西省', '西安市', '雁塔区', '软件园线索E地址', '技术经理创建的线索', 206, 1, 206, NOW(), NOW()),
(1, '线索F-技术员工', '硬件', '技术交流', '13900001006', '<EMAIL>', 'www.leadF.com', '陕西省', '西安市', '高新区', '科技二路线索F地址', '技术员工创建的线索', 207, 1, 207, NOW(), NOW()),
(1, '线索G-财务员工', '咨询', '财务咨询', '13900001007', '<EMAIL>', 'www.leadG.com', '陕西省', '西安市', '碑林区', '财富中心线索G地址', '财务员工创建的线索', 208, 1, 208, NOW(), NOW()),
(1, '线索H-自定义权限', '广告', '广告合作', '13900001008', '<EMAIL>', 'www.leadH.com', '陕西省', '西安市', '新城区', '广告大厦线索H地址', '自定义权限创建的线索', 209, 1, 209, NOW(), NOW());

-- =====================================================
-- 3. 项目管理测试数据
-- 基于实际 project_project 表结构创建测试数据
-- =====================================================

SELECT '3. 创建项目管理测试数据' as step;

-- 清理现有测试数据
DELETE FROM project_project WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209);

-- 创建项目测试数据（使用实际字段名）
INSERT INTO `project_project` (
    `tenant_id`, `project_name`, `project_code`, `project_type`, `priority`,
    `start_date`, `end_date`, `budget`, `description`, `status`,
    `creator_id`, `created_at`, `updated_at`
) VALUES
(1, '项目A-技术部经理', 'PROJ-2025-001', '软件开发', 1, '2025-01-01', '2025-06-30', 500000.00, '技术部经理创建的重要软件开发项目', 1, 206, NOW(), NOW()),
(1, '项目B-技术员工', 'PROJ-2025-002', '系统维护', 2, '2025-02-01', '2025-07-31', 200000.00, '技术员工创建的系统维护项目', 1, 207, NOW(), NOW()),
(1, '项目C-自定义权限', 'PROJ-2025-003', '产品研发', 1, '2025-03-01', '2025-08-31', 800000.00, '自定义权限用户创建的产品研发项目', 1, 209, NOW(), NOW()),
(1, '项目D-销售经理', 'PROJ-2025-004', '市场推广', 2, '2025-01-15', '2025-05-15', 300000.00, '销售经理创建的市场推广项目', 1, 202, NOW(), NOW());

-- =====================================================
-- 4. 每日报价测试数据
-- 基于实际 daily_price_order 表结构创建测试数据
-- =====================================================

SELECT '4. 创建每日报价测试数据' as step;

-- 清理现有测试数据
DELETE FROM daily_price_order WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209);

-- 创建报价测试数据（使用实际字段名）
INSERT INTO `daily_price_order` (
    `tenant_id`, `price_date`, `total_items`, `approval_status`,
    `submit_time`, `submitter_id`, `creator_id`, `created_at`, `updated_at`
) VALUES
(1, '2025-01-31', 5, 1, NOW(), 202, 202, NOW(), NOW()), -- 销售经理创建的报价单
(1, '2025-01-31', 3, 1, NOW(), 203, 203, NOW(), NOW()), -- 销售组长创建的报价单
(1, '2025-01-31', 8, 0, NOW(), 204, 204, NOW(), NOW()), -- 销售员工1创建的报价单（待审批）
(1, '2025-01-31', 2, 1, NOW(), 205, 205, NOW(), NOW()), -- 销售员工2创建的报价单
(1, '2025-01-30', 6, 1, NOW(), 206, 206, NOW(), NOW()), -- 技术经理创建的报价单
(1, '2025-01-30', 4, 0, NOW(), 207, 207, NOW(), NOW()), -- 技术员工创建的报价单（待审批）
(1, '2025-01-29', 7, 1, NOW(), 208, 208, NOW(), NOW()), -- 财务员工创建的报价单
(1, '2025-01-29', 3, 0, NOW(), 209, 209, NOW(), NOW()); -- 自定义权限用户创建的报价单（待审批）

-- =====================================================
-- 5. 数据权限测试验证
-- =====================================================

SELECT '=== 数据权限测试验证开始 ===' as message;

-- 5.1 显示测试用户信息
SELECT '5.1 测试用户信息' as step;
SELECT
    a.id as user_id,
    a.username,
    a.real_name,
    d.name as dept_name,
    r.name as role_name,
    CASE r.data_scope
        WHEN 1 THEN '全部数据'
        WHEN 2 THEN '本部门'
        WHEN 3 THEN '本部门及以下'
        WHEN 4 THEN '仅本人'
        WHEN 5 THEN '自定义'
    END as data_scope_text,
    r.data_scope_dept_ids
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
LEFT JOIN system_admin_role ar ON a.id = ar.admin_id
LEFT JOIN system_role r ON ar.role_id = r.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- 5.2 显示创建的测试数据统计
SELECT '5.2 测试数据统计' as step;
SELECT
    '客户数据' as data_type,
    COUNT(*) as total_count,
    COUNT(DISTINCT creator_id) as creator_count
FROM crm_customer
WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209)
UNION ALL
SELECT
    '线索数据',
    COUNT(*),
    COUNT(DISTINCT creator_id)
FROM crm_lead
WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209)
UNION ALL
SELECT
    '项目数据',
    COUNT(*),
    COUNT(DISTINCT creator_id)
FROM project_project
WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209)
UNION ALL
SELECT
    '报价数据',
    COUNT(*),
    COUNT(DISTINCT creator_id)
FROM daily_price_order
WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209);

-- =====================================================
-- 6. 数据权限验证查询（实际可执行）
-- =====================================================

SELECT '6. 数据权限验证查询' as step;

-- 6.1 全部数据权限测试（租户超级管理员 - user_id=201）
SELECT '6.1 全部数据权限测试（租户超级管理员）' as test_case;
SELECT
    '客户数据' as data_type,
    COUNT(*) as record_count,
    '应该看到所有12条记录' as expected_result
FROM crm_customer
WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209)
UNION ALL
SELECT
    '线索数据',
    COUNT(*),
    '应该看到所有8条记录'
FROM crm_lead
WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209);

-- 6.2 本部门及以下权限测试（销售部经理 - user_id=202, dept_id=102）
SELECT '6.2 销售部经理权限测试（本部门及以下）' as test_case;
SELECT
    '客户数据' as data_type,
    COUNT(*) as record_count,
    '应该看到销售部(102)+销售一组(103)+销售二组(104)的6条记录' as expected_result
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
WHERE c.tenant_id = 1
  AND c.creator_id IN (201,202,203,204,205,206,207,208,209)
  AND a.dept_id IN (102, 103, 104);  -- 销售部及下级部门

-- 6.3 本部门权限测试（销售一组长 - user_id=203, dept_id=103）
SELECT '6.3 销售一组长权限测试（本部门）' as test_case;
SELECT
    '客户数据' as data_type,
    COUNT(*) as record_count,
    '应该看到销售一组(103)的4条记录' as expected_result
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
WHERE c.tenant_id = 1
  AND c.creator_id IN (201,202,203,204,205,206,207,208,209)
  AND a.dept_id = 103;  -- 仅销售一组

-- 6.4 仅本人权限测试（销售一组员工 - user_id=204）
SELECT '6.4 销售一组员工权限测试（仅本人）' as test_case;
SELECT
    '客户数据' as data_type,
    COUNT(*) as record_count,
    '应该看到仅本人创建的2条记录' as expected_result
FROM crm_customer
WHERE tenant_id = 1
  AND creator_id = 204;  -- 仅本人创建

-- 6.5 自定义权限测试（自定义权限用户 - user_id=209, 权限部门：102,105）
SELECT '6.5 自定义权限测试（销售部+技术部）' as test_case;
SELECT
    '客户数据' as data_type,
    COUNT(*) as record_count,
    '应该看到销售部(102)+技术部(105)的4条记录' as expected_result
FROM crm_customer c
LEFT JOIN system_admin a ON c.creator_id = a.id
WHERE c.tenant_id = 1
  AND c.creator_id IN (201,202,203,204,205,206,207,208,209)
  AND a.dept_id IN (102, 105);  -- 销售部+技术部

-- 6.6 详细数据分布查看
SELECT '6.6 详细数据分布' as test_case;
SELECT
    a.username,
    a.real_name,
    d.name as dept_name,
    COUNT(c.id) as customer_count,
    GROUP_CONCAT(c.customer_name) as customer_names
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
LEFT JOIN crm_customer c ON a.id = c.creator_id AND c.tenant_id = 1
WHERE a.tenant_id = 1
GROUP BY a.id, a.username, a.real_name, d.name
ORDER BY a.id;

SELECT '=== 业务测试数据创建完成！===' as final_message;
SELECT '=== 现在可以使用不同角色登录系统测试数据权限了！===' as instruction;

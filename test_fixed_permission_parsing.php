<?php
/**
 * 测试修复后的权限解析逻辑
 */

require_once 'vendor/autoload.php';

echo "=== 测试修复后的权限解析逻辑 ===\n\n";

// 模拟修复后的权限解析逻辑
function camelToSnake($input) {
    return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
}

function generateCrmPermissionName($controllerName, $method) {
    $snakeName = camelToSnake($controllerName);
    if (!str_starts_with($snakeName, 'crm_')) {
        $snakeName = 'crm_' . $snakeName;
    }
    return $snakeName . ':' . $method;
}

function generateSystemPermissionName($parts, $controllerName, $method) {
    if (count($parts) > 4) {
        $subPath = strtolower($parts[3]);
        $controller = strtolower($controllerName);
        return $subPath . ':' . $controller . ':' . $method;
    } else {
        $controller = strtolower($controllerName);
        return $controller . ':' . $method;
    }
}

function generateProjectPermissionName($controllerName, $method) {
    $snakeName = camelToSnake($controllerName);
    if (!str_starts_with($snakeName, 'project_')) {
        $snakeName = 'project_' . $snakeName;
    }
    return $snakeName . ':' . $method;
}

function parsePermissionInfo($ruleName) {
    [$classPath, $method] = explode('@', $ruleName);
    $parts = explode('\\', $classPath);
    $module = strtolower($parts[1]);
    $controllerClass = $parts[count($parts) - 1];
    $controllerName = str_replace('Controller', '', $controllerClass);
    
    switch ($module) {
        case 'crm':
            $permissionName = generateCrmPermissionName($controllerName, $method);
            break;
        case 'system':
            $permissionName = generateSystemPermissionName($parts, $controllerName, $method);
            break;
        case 'project':
            $permissionName = generateProjectPermissionName($controllerName, $method);
            break;
        default:
            $permissionName = strtolower($module . '_' . $controllerName) . ':' . $method;
    }
    
    return "{$module}:{$permissionName}";
}

// 测试用例
$testCases = [
    // CRM模块
    'app\\crm\\controller\\CrmCustomerMyController@index' => 'crm:crm_customer_my:index',
    'app\\crm\\controller\\CrmLeadController@add' => 'crm:crm_lead:add',
    'app\\crm\\controller\\CrmBusinessController@edit' => 'crm:crm_business:edit',
    'app\\crm\\controller\\CrmContractController@delete' => 'crm:crm_contract:delete',
    
    // System模块 - 基础控制器
    'app\\system\\controller\\AuthController@login' => 'system:auth:login',
    'app\\system\\controller\\ConfigController@index' => 'system:config:index',
    
    // System模块 - 权限控制器（子目录）
    'app\\system\\controller\\permission\\AdminController@index' => 'system:permission:admin:index',
    'app\\system\\controller\\permission\\RoleController@add' => 'system:permission:role:add',
    'app\\system\\controller\\permission\\MenuController@edit' => 'system:permission:menu:edit',
    
    // System模块 - 日志控制器（子目录）
    'app\\system\\controller\\log\\LoginController@index' => 'system:log:login:index',
    'app\\system\\controller\\log\\OperationController@detail' => 'system:log:operation:detail',
    
    // Project模块
    'app\\project\\controller\\ProjectController@index' => 'project:project:index',
    'app\\project\\controller\\ProjectMemberController@add' => 'project:project_member:add',
    'app\\project\\controller\\ProjectTaskController@edit' => 'project:project_task:edit',
];

echo "1. 权限解析测试:\n";

$successCount = 0;
$totalCount = count($testCases);

foreach ($testCases as $input => $expected) {
    $result = parsePermissionInfo($input);
    
    $status = ($result === $expected) ? '✅' : '❌';
    echo "  {$status} {$input}\n";
    echo "    期望: {$expected}\n";
    echo "    实际: {$result}\n";
    
    if ($result === $expected) {
        $successCount++;
    } else {
        echo "    ⚠️ 解析结果不匹配！\n";
    }
    echo "\n";
}

echo "2. 测试结果统计:\n";
echo "  总测试数: {$totalCount}\n";
echo "  成功数: {$successCount}\n";
echo "  失败数: " . ($totalCount - $successCount) . "\n";
echo "  成功率: " . round($successCount / $totalCount * 100, 1) . "%\n\n";

if ($successCount === $totalCount) {
    echo "✅ 所有权限解析测试通过！\n";
} else {
    echo "❌ 部分权限解析测试失败，需要调整解析逻辑\n";
}

echo "\n=== 权限解析测试完成 ===\n";

<?php
/**
 * 测试权限验证功能
 */

require_once 'vendor/autoload.php';

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "=== 测试权限验证功能 ===\n\n";
    
    // 测试用户权限查询
    echo "1. 测试用户权限查询:\n";
    
    $testUsers = [202, 203, 204]; // 销售经理、组长、员工
    
    foreach ($testUsers as $userId) {
        echo "  测试用户ID: {$userId}\n";
        
        // 查询用户信息
        $stmt = $pdo->prepare("
            SELECT a.username, a.real_name, r.name as role_name 
            FROM system_admin a 
            LEFT JOIN system_admin_role ar ON a.id = ar.admin_id 
            LEFT JOIN system_role r ON ar.role_id = r.id 
            WHERE a.id = ? AND a.tenant_id = 1
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "    用户: {$user['real_name']} ({$user['username']}) - {$user['role_name']}\n";
            
            // 查询用户权限
            $stmt = $pdo->prepare("
                SELECT m.name 
                FROM system_admin_role ar
                LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
                LEFT JOIN system_menu m ON rm.menu_id = m.id
                WHERE ar.admin_id = ? AND m.status = 1 AND m.deleted_at IS NULL
                ORDER BY m.name
            ");
            $stmt->execute([$userId]);
            $permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "    权限数量: " . count($permissions) . " 个\n";
            echo "    权限列表: " . implode(', ', array_slice($permissions, 0, 5)) . (count($permissions) > 5 ? '...' : '') . "\n";
            
            // 测试特定权限
            $testPermissions = [
                'crm:crm_customer_my:index',
                'system:permission:admin:index',
                'project:project:index'
            ];
            
            foreach ($testPermissions as $permission) {
                $hasPermission = in_array($permission, $permissions);
                $status = $hasPermission ? '✅' : '❌';
                echo "    {$status} {$permission}\n";
            }
        } else {
            echo "    ❌ 用户不存在\n";
        }
        
        echo "\n";
    }
    
    echo "2. 测试权限解析与验证:\n";
    
    // 模拟权限验证流程
    $testRoutes = [
        'app\\crm\\controller\\CrmCustomerMyController@index',
        'app\\system\\controller\\permission\\AdminController@index',
        'app\\project\\controller\\ProjectController@index',
    ];
    
    foreach ($testRoutes as $route) {
        echo "  测试路由: {$route}\n";
        
        // 解析权限
        [$classPath, $method] = explode('@', $route);
        $parts = explode('\\', $classPath);
        $module = strtolower($parts[1]);
        $controllerClass = $parts[count($parts) - 1];
        $controller = strtolower(str_replace('Controller', '', $controllerClass));
        
        if (count($parts) > 4) {
            $subPath = strtolower($parts[3]);
            $controller = $subPath . ':' . $controller;
        }
        
        $permission = "{$module}:{$controller}:{$method}";
        echo "    解析权限: {$permission}\n";
        
        // 检查权限是否存在于数据库
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_menu WHERE name = ? AND status = 1 AND deleted_at IS NULL");
        $stmt->execute([$permission]);
        $exists = $stmt->fetchColumn() > 0;
        
        $status = $exists ? '✅' : '❌';
        echo "    权限存在: {$status}\n";
        
        if (!$exists) {
            // 查找相似权限
            $stmt = $pdo->prepare("SELECT name FROM system_menu WHERE name LIKE ? AND status = 1 AND deleted_at IS NULL LIMIT 3");
            $stmt->execute(["%{$module}%{$controller}%"]);
            $similar = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if ($similar) {
                echo "    相似权限: " . implode(', ', $similar) . "\n";
            }
        }
        
        echo "\n";
    }
    
    echo "✅ 权限验证测试完成！\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
}

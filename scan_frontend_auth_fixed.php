<?php
/**
 * 修正版：扫描所有前端文件中的权限按钮配置
 */

echo "=== 扫描所有前端权限按钮配置（修正版） ===\n\n";

function scanDirectory($dir, $pattern = '*.vue') {
    $files = [];
    if (!is_dir($dir)) {
        return $files;
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && fnmatch($pattern, $file->getFilename())) {
            $files[] = $file->getPathname();
        }
    }
    
    return $files;
}

function extractAuthFromFile($filePath) {
    $content = file_get_contents($filePath);
    $lines = explode("\n", $content);
    $authButtons = [];
    
    foreach ($lines as $lineNum => $line) {
        // 更准确的正则表达式匹配
        if (preg_match_all('/v-auth\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
            foreach ($matches[1] as $authName) {
                $authButtons[] = [
                    'file' => basename($filePath),
                    'full_path' => $filePath,
                    'line' => $lineNum + 1,
                    'auth' => $authName,
                    'context' => trim($line)
                ];
            }
        }
    }
    
    return $authButtons;
}

// 扫描前端目录
$frontendDir = 'frontend/src/views';
echo "1. 扫描前端Vue文件:\n";
echo "  扫描目录: {$frontendDir}\n";

if (!is_dir($frontendDir)) {
    echo "❌ 前端目录不存在: {$frontendDir}\n";
    exit(1);
}

$vueFiles = scanDirectory($frontendDir);
echo "  找到Vue文件: " . count($vueFiles) . " 个\n\n";

$allAuthButtons = [];
$fileStats = [];

foreach ($vueFiles as $file) {
    $authButtons = extractAuthFromFile($file);
    if (!empty($authButtons)) {
        $relativePath = str_replace('frontend/src/views/', '', $file);
        $fileStats[$relativePath] = count($authButtons);
        $allAuthButtons = array_merge($allAuthButtons, $authButtons);
    }
}

echo "2. 权限按钮统计:\n";
echo "  包含权限按钮的文件: " . count($fileStats) . " 个\n";
echo "  权限按钮总数: " . count($allAuthButtons) . " 个\n\n";

if (empty($allAuthButtons)) {
    echo "❌ 未找到任何权限按钮，可能需要检查扫描逻辑\n";
    
    // 手动检查几个关键文件
    $keyFiles = [
        'frontend/src/views/permission/Admin.vue',
        'frontend/src/views/permission/Role.vue',
        'frontend/src/views/permission/Menu.vue'
    ];
    
    echo "\n手动检查关键文件:\n";
    foreach ($keyFiles as $file) {
        if (file_exists($file)) {
            echo "  检查文件: {$file}\n";
            $content = file_get_contents($file);
            if (preg_match_all('/v-auth\s*=\s*["\']([^"\']+)["\']/', $content, $matches)) {
                echo "    找到权限: " . count($matches[1]) . " 个\n";
                foreach ($matches[1] as $auth) {
                    echo "      - {$auth}\n";
                }
            } else {
                echo "    未找到权限按钮\n";
            }
        } else {
            echo "  文件不存在: {$file}\n";
        }
        echo "\n";
    }
    
    exit(1);
}

echo "3. 按文件分组的权限按钮:\n";
foreach ($fileStats as $file => $count) {
    echo "  📁 {$file}: {$count} 个权限按钮\n";
}

echo "\n4. 权限格式分析:\n";

$formatStats = [
    'three_parts' => 0,    // module:controller:action
    'four_parts' => 0,     // module:sub:controller:action  
    'two_parts' => 0,      // module:controller
    'other' => 0
];

$needsUpdate = [];
$uniqueAuths = [];

foreach ($allAuthButtons as $button) {
    $auth = $button['auth'];
    $parts = explode(':', $auth);
    $partCount = count($parts);
    
    // 去重统计
    if (!in_array($auth, $uniqueAuths)) {
        $uniqueAuths[] = $auth;
    }
    
    if ($partCount == 3) {
        $formatStats['three_parts']++;
    } elseif ($partCount == 4) {
        $formatStats['four_parts']++;
        
        // 四段式需要转换为三段式
        $module = $parts[0];
        $sub = $parts[1]; 
        $controller = $parts[2];
        $action = $parts[3];
        $newAuth = "{$module}:{$sub}_{$controller}:{$action}";
        
        $needsUpdate[] = [
            'file' => $button['file'],
            'full_path' => $button['full_path'],
            'line' => $button['line'],
            'old_auth' => $auth,
            'new_auth' => $newAuth,
            'context' => $button['context']
        ];
    } elseif ($partCount == 2) {
        $formatStats['two_parts']++;
    } else {
        $formatStats['other']++;
    }
}

foreach ($formatStats as $format => $count) {
    echo "  {$format}: {$count} 个\n";
}

echo "\n5. 唯一权限统计:\n";
echo "  不重复的权限: " . count($uniqueAuths) . " 个\n";

echo "\n6. 所有唯一权限列表:\n";
sort($uniqueAuths);
foreach ($uniqueAuths as $auth) {
    $parts = explode(':', $auth);
    $partCount = count($parts);
    $status = '';
    
    if ($partCount == 4) {
        $status = '❌ 需要转换';
    } elseif ($partCount == 3) {
        $status = '✅ 格式正确';
    } else {
        $status = '⚠️ 格式异常';
    }
    
    echo "  {$status} {$auth}\n";
}

echo "\n7. 需要更新的权限按钮:\n";
echo "  需要更新总数: " . count($needsUpdate) . " 个\n\n";

if (!empty($needsUpdate)) {
    $updateByFile = [];
    foreach ($needsUpdate as $update) {
        $updateByFile[$update['file']][] = $update;
    }
    
    foreach ($updateByFile as $file => $updates) {
        echo "  📁 {$file}: " . count($updates) . " 处需要更新\n";
        foreach ($updates as $update) {
            echo "    第{$update['line']}行: {$update['old_auth']} → {$update['new_auth']}\n";
        }
        echo "\n";
    }
    
    echo "8. 工作量评估:\n";
    $totalFiles = count($updateByFile);
    $totalUpdates = count($needsUpdate);
    
    echo "  📁 需要修改的文件: {$totalFiles} 个\n";
    echo "  🔧 需要更新的权限按钮: {$totalUpdates} 个\n";
    echo "  ⏱️ 预估修改时间: " . ($totalFiles * 10 + $totalUpdates * 3) . " 分钟\n";
    echo "  🧪 测试验证时间: " . ($totalFiles * 15) . " 分钟\n";
    echo "  📝 总计时间: " . ($totalFiles * 25 + $totalUpdates * 3) . " 分钟\n";
    
    echo "\n9. 实施计划:\n";
    echo "  阶段1: 备份需要修改的 {$totalFiles} 个文件\n";
    echo "  阶段2: 批量更新 {$totalUpdates} 个权限按钮配置\n";
    echo "  阶段3: 逐个页面测试权限控制功能\n";
    echo "  阶段4: 验证按钮显示/隐藏逻辑正确性\n";
} else {
    echo "8. 结果:\n";
    echo "  ✅ 所有权限按钮格式都已正确，无需更新\n";
}

echo "\n=== 扫描完成 ===\n";

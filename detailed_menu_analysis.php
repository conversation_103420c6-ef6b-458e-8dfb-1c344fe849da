<?php
/**
 * 详细分析system_menu表权限命名规范
 */

require_once 'vendor/autoload.php';

echo "=== 详细分析system_menu表权限命名规范 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取所有权限数据:\n";
    
    $stmt = $pdo->prepare("
        SELECT name, title, type
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $allPermissions = $stmt->fetchAll();
    
    echo "  总权限数: " . count($allPermissions) . " 个\n\n";
    
    echo "2. 按模块分组分析权限命名:\n";
    
    $moduleGroups = [];
    foreach ($allPermissions as $perm) {
        $name = $perm['name'];
        $parts = explode(':', $name);
        
        if (count($parts) >= 2) {
            $module = $parts[0];
            if (!isset($moduleGroups[$module])) {
                $moduleGroups[$module] = [];
            }
            $moduleGroups[$module][] = $perm;
        } else {
            // 单词权限
            if (!isset($moduleGroups['single_word'])) {
                $moduleGroups['single_word'] = [];
            }
            $moduleGroups['single_word'][] = $perm;
        }
    }
    
    foreach ($moduleGroups as $module => $permissions) {
        echo "  {$module}模块: " . count($permissions) . " 个权限\n";
        
        // 分析命名模式
        $patterns = [
            'three_parts_underscore' => [], // module:controller_name:action
            'three_parts_simple' => [],     // module:controller:action  
            'two_parts' => [],              // module:controller
            'other' => []
        ];
        
        foreach ($permissions as $perm) {
            $parts = explode(':', $perm['name']);
            
            if (count($parts) == 3) {
                if (strpos($parts[1], '_') !== false) {
                    $patterns['three_parts_underscore'][] = $perm;
                } else {
                    $patterns['three_parts_simple'][] = $perm;
                }
            } elseif (count($parts) == 2) {
                $patterns['two_parts'][] = $perm;
            } else {
                $patterns['other'][] = $perm;
            }
        }
        
        foreach ($patterns as $pattern => $perms) {
            if (!empty($perms)) {
                echo "    {$pattern}: " . count($perms) . " 个\n";
                // 显示前5个示例
                for ($i = 0; $i < min(5, count($perms)); $i++) {
                    $type = $perms[$i]['type'] == 1 ? '菜单' : '按钮';
                    echo "      - {$perms[$i]['name']} ({$perms[$i]['title']}) [{$type}]\n";
                }
                if (count($perms) > 5) {
                    echo "      ... 还有 " . (count($perms) - 5) . " 个\n";
                }
            }
        }
        echo "\n";
    }
    
    echo "3. 命名不一致性问题分析:\n";
    
    $inconsistentModules = [];
    foreach ($moduleGroups as $module => $permissions) {
        if ($module === 'single_word') continue;
        
        $hasUnderscore = false;
        $hasSimple = false;
        
        foreach ($permissions as $perm) {
            $parts = explode(':', $perm['name']);
            if (count($parts) == 3) {
                if (strpos($parts[1], '_') !== false) {
                    $hasUnderscore = true;
                } else {
                    $hasSimple = true;
                }
            }
        }
        
        if ($hasUnderscore && $hasSimple) {
            $inconsistentModules[] = $module;
        }
    }
    
    if (!empty($inconsistentModules)) {
        echo "  发现命名不一致的模块:\n";
        foreach ($inconsistentModules as $module) {
            echo "    - {$module}模块同时存在下划线和非下划线命名\n";
        }
    } else {
        echo "  ✅ 各模块内部命名基本一致\n";
    }
    
    echo "\n4. 与控制器文件对比分析:\n";
    
    // 分析CRM模块的权限命名
    echo "  4.1 CRM模块权限vs控制器对比:\n";
    if (isset($moduleGroups['crm'])) {
        $crmControllers = [];
        foreach ($moduleGroups['crm'] as $perm) {
            $parts = explode(':', $perm['name']);
            if (count($parts) >= 2) {
                $controller = $parts[1];
                if (!in_array($controller, $crmControllers)) {
                    $crmControllers[] = $controller;
                }
            }
        }
        
        echo "    权限中的控制器名称:\n";
        foreach ($crmControllers as $controller) {
            echo "      - {$controller}\n";
        }
        
        // 实际控制器文件
        $actualControllers = [];
        if (is_dir('app/crm/controller')) {
            $files = glob('app/crm/controller/*Controller.php');
            foreach ($files as $file) {
                $filename = basename($file, '.php');
                $controllerName = str_replace('Controller', '', $filename);
                // 转换为下划线格式
                $snakeName = strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $controllerName));
                $actualControllers[] = $snakeName;
            }
        }
        
        echo "    实际控制器文件（转换为下划线格式）:\n";
        foreach ($actualControllers as $controller) {
            echo "      - {$controller}\n";
        }
        
        // 对比分析
        $missingInPermissions = array_diff($actualControllers, $crmControllers);
        $extraInPermissions = array_diff($crmControllers, $actualControllers);
        
        if (!empty($missingInPermissions)) {
            echo "    ⚠️ 控制器存在但权限缺失:\n";
            foreach ($missingInPermissions as $controller) {
                echo "      - {$controller}\n";
            }
        }
        
        if (!empty($extraInPermissions)) {
            echo "    ⚠️ 权限存在但控制器缺失:\n";
            foreach ($extraInPermissions as $controller) {
                echo "      - {$controller}\n";
            }
        }
    }
    
    echo "\n5. 权限命名规范化方案建议:\n";
    echo "  基于以上分析，我将提供详细的规范化方案...\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 分析完成 ===\n";

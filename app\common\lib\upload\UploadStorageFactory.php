<?php

namespace app\common\lib\upload;

use app\common\exception\ValidateFailedException;
use app\common\lib\upload\driver\AliyunDriver;
use app\common\lib\upload\driver\LocalDriver;
use app\common\lib\upload\driver\QiniuDriver;
use app\common\lib\upload\driver\TencentDriver;
use app\system\service\ConfigService;

class UploadStorageFactory
{
	/**
	 * 创建存储驱动实例
	 *
	 * @param string $type 存储类型
	 * @return UploadStorageInterface
	 * @throws \Exception
	 */
	public static function create(string $type): UploadStorageInterface
	{
		return match ($type) {
			'local' => new LocalDriver(),
			'qnoss' => new QiniuDriver(),
			'txoss' => new TencentDriver(),
			'alioss' => new AliyunDriver(),
			default => throw new \Exception('不支持的存储类型')
		};
	}
	
	/**
	 * 获取存储配置
	 *
	 * @param string $storage  存储类型
	 * @param int    $tenantId 租户ID
	 * @return array
	 */
	public static function getConfig(string $storage, int $tenantId = 0): array
	{
		// todo 获取配置信息，优先使用租户配置，没有则使用平台配置
		// 实际项目中可以从数据表system_attachment_config中读取
		/*$config = \think\facade\Db::name('system_attachment_config')
		                          ->where('storage', $type)
		                          ->where('tenant_id', $tenantId)
		                          ->where('status', 1)
		                          ->order('is_default DESC')
		                          ->find();*/

		// 临时配置，用于测试
		$config = match ($storage) {
			'local' => [
				'upload_path' => '/uploads/',
				'domain' => request()->domain(),
				'max_size' => 50 * 1024 * 1024, // 50MB
			],
			'qnoss' => [
				'access_key' => '',
				'secret_key' => '',
				'bucket' => '',
				'domain' => '',
				'deadline' => 3600,
			],
			default => []
		};
		
		$uploadInfo = ConfigService::getInstance()
		                           ->getInfo('upload');
		
		if (empty($config)) {
			
			$configType = $uploadInfo['upload_allow_type'];
			if ($configType != $storage) {
				throw new ValidateFailedException('未找到可用的存储配置');
			}
			// 没有找到租户配置，使用平台配置
			$config = [
				'access_key' => $uploadInfo[$configType . '_access_key_id'] ?? '',
				'secret_key' => $uploadInfo[$configType . '_access_key_secret'] ?? '',
				'bucket'     => $uploadInfo[$configType . '_bucket'] ?? '',
				'domain'     => $uploadInfo[$configType . '_domain'] ?? '',
			];
			
			foreach ($config as $key => $value) {
				if (empty($value)) {
					throw new ValidateFailedException('未找到可用的存储配置');
				}
			}
			$config['upload_allow_ext']  = intval($uploadInfo['upload_allow_ext'] ?? 10);
			$config['upload_allow_size'] = intval($uploadInfo['upload_allow_size'] ?? 1024 * 1024 * 10);
		}
		
		$config['deadline'] = $uploadInfo['deadline'] ?? 3600;
		
		return $config;
	}
}
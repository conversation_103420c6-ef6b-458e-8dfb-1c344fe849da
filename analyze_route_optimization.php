<?php
/**
 * 分析route目录优化方案
 * 分析文件过多问题和中间件配置规范
 */

echo "=== Route目录优化分析 ===\n\n";

// 扫描route目录
$routeDir = 'route';
$routeFiles = [];

if (is_dir($routeDir)) {
    $items = scandir($routeDir);
    foreach ($items as $item) {
        if ($item == '.' || $item == '..' || pathinfo($item, PATHINFO_EXTENSION) != 'php') {
            continue;
        }
        
        $filePath = $routeDir . DIRECTORY_SEPARATOR . $item;
        $fileSize = filesize($filePath);
        $lineCount = count(file($filePath));
        
        $routeFiles[] = [
            'name' => $item,
            'path' => $filePath,
            'size' => $fileSize,
            'lines' => $lineCount
        ];
    }
}

// 按文件大小排序
usort($routeFiles, function($a, $b) {
    return $b['size'] <=> $a['size'];
});

echo "1. Route目录文件统计:\n";
echo "  总文件数: " . count($routeFiles) . " 个\n";
echo "  总大小: " . array_sum(array_column($routeFiles, 'size')) . " 字节\n\n";

echo "2. 文件大小排序（前10个）:\n";
foreach (array_slice($routeFiles, 0, 10) as $file) {
    $sizeKB = round($file['size'] / 1024, 2);
    echo "  {$file['name']}: {$sizeKB}KB ({$file['lines']} 行)\n";
}
echo "\n";

// 分析文件命名模式
echo "3. 文件命名模式分析:\n";
$patterns = [
    'crm_' => [],
    'system_' => [],
    'project_' => [],
    'workflow_' => [],
    'notice_' => [],
    'daily_' => [],
    'hr_' => [],
    'ims_' => [],
    'tenant_' => [],
    'other' => []
];

foreach ($routeFiles as $file) {
    $name = $file['name'];
    $matched = false;
    
    foreach ($patterns as $pattern => $files) {
        if ($pattern !== 'other' && strpos($name, $pattern) === 0) {
            $patterns[$pattern][] = $file;
            $matched = true;
            break;
        }
    }
    
    if (!$matched) {
        $patterns['other'][] = $file;
    }
}

foreach ($patterns as $pattern => $files) {
    if (!empty($files)) {
        echo "  {$pattern}: " . count($files) . " 个文件\n";
        foreach ($files as $file) {
            echo "    - {$file['name']}\n";
        }
        echo "\n";
    }
}

// 分析中间件使用情况
echo "4. 中间件使用情况分析:\n";

$middlewareStats = [
    'TokenAuthMiddleware' => 0,
    'PermissionMiddleware' => 0,
    'OperationLogMiddleware' => 0,
    'CheckLoginAttempts' => 0,
    'no_middleware' => 0
];

$middlewareFiles = [];

foreach ($routeFiles as $file) {
    $content = file_get_contents($file['path']);
    $fileMiddlewares = [];
    
    foreach ($middlewareStats as $middleware => $count) {
        if ($middleware === 'no_middleware') continue;
        
        if (strpos($content, $middleware) !== false) {
            $middlewareStats[$middleware]++;
            $fileMiddlewares[] = $middleware;
        }
    }
    
    if (empty($fileMiddlewares)) {
        $middlewareStats['no_middleware']++;
        $fileMiddlewares[] = 'no_middleware';
    }
    
    $middlewareFiles[$file['name']] = $fileMiddlewares;
}

foreach ($middlewareStats as $middleware => $count) {
    echo "  {$middleware}: {$count} 个文件\n";
}
echo "\n";

// 分析需要不同中间件配置的路由
echo "5. 中间件配置分类:\n";

$middlewareCategories = [
    'auth_only' => [], // 只需要Token认证
    'auth_permission' => [], // 需要Token认证+权限验证
    'auth_permission_log' => [], // 需要Token认证+权限验证+操作日志
    'public' => [], // 公开接口
    'special' => [] // 特殊配置
];

foreach ($middlewareFiles as $fileName => $middlewares) {
    if (in_array('no_middleware', $middlewares)) {
        $middlewareCategories['public'][] = $fileName;
    } elseif (in_array('TokenAuthMiddleware', $middlewares) && 
              in_array('PermissionMiddleware', $middlewares) && 
              in_array('OperationLogMiddleware', $middlewares)) {
        $middlewareCategories['auth_permission_log'][] = $fileName;
    } elseif (in_array('TokenAuthMiddleware', $middlewares) && 
              in_array('PermissionMiddleware', $middlewares)) {
        $middlewareCategories['auth_permission'][] = $fileName;
    } elseif (in_array('TokenAuthMiddleware', $middlewares)) {
        $middlewareCategories['auth_only'][] = $fileName;
    } else {
        $middlewareCategories['special'][] = $fileName;
    }
}

foreach ($middlewareCategories as $category => $files) {
    if (!empty($files)) {
        echo "  {$category}: " . count($files) . " 个文件\n";
        foreach ($files as $file) {
            echo "    - {$file}\n";
        }
        echo "\n";
    }
}

echo "=== 分析完成 ===\n";

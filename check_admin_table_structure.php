<?php
/**
 * 检查system_admin表结构
 */

require_once 'vendor/autoload.php';

echo "=== 检查system_admin表结构 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. system_admin表结构:\n";
    
    $stmt = $pdo->prepare("DESCRIBE system_admin");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $column) {
        echo "  {$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Default']}\n";
    }
    
    echo "\n2. 租户1的用户数据:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, username, email, status, created_at
        FROM system_admin 
        WHERE tenant_id = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    echo "  租户1用户数: " . count($users) . " 个\n\n";
    
    foreach ($users as $user) {
        $status = $user['status'] == 1 ? '正常' : '禁用';
        echo "  ID:{$user['id']} {$user['username']} - {$status}\n";
        echo "    邮箱: {$user['email']}\n";
        echo "    创建时间: {$user['created_at']}\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";

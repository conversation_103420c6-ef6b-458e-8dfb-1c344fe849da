#!/usr/bin/env python3
"""
直接测试MCP MySQL功能
"""
import os
import sys

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

print("=== MCP MySQL 测试 ===")
print(f"Python版本: {sys.version}")
print(f"MYSQL_HOST: {os.environ.get('MYSQL_HOST')}")
print(f"MYSQL_DATABASE: {os.environ.get('MYSQL_DATABASE')}")

try:
    print("\n1. 测试mysql.connector...")
    import mysql.connector
    
    conn = mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )
    print("✅ mysql.connector连接成功")
    
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM system_menu")
    count = cursor.fetchone()[0]
    print(f"✅ 查询成功，system_menu表有 {count} 条记录")
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"❌ mysql.connector测试失败: {e}")

try:
    print("\n2. 测试mcp_server_mysql...")
    import mcp_server_mysql
    print("✅ mcp_server_mysql导入成功")
    
except Exception as e:
    print(f"❌ mcp_server_mysql导入失败: {e}")

print("\n=== 测试完成 ===")

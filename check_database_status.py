#!/usr/bin/env python3
"""
检查数据库当前状态
"""
import os
import mysql.connector

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

def get_db_connection():
    return mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )

def main():
    print("=== 检查数据库当前状态 ===\n")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 1. 检查用户表
    print("1. 检查用户表状态:")
    cursor.execute("SELECT COUNT(*) FROM system_admin WHERE tenant_id = 1")
    admin_count = cursor.fetchone()[0]
    print(f"  system_admin表用户数: {admin_count} 个")
    
    if admin_count > 0:
        cursor.execute("SELECT id, username, real_name FROM system_admin WHERE tenant_id = 1 ORDER BY id")
        users = cursor.fetchall()
        for user in users:
            print(f"    ID:{user[0]} - {user[1]} ({user[2]})")
    
    # 2. 检查角色表
    print(f"\n2. 检查角色表状态:")
    cursor.execute("SELECT COUNT(*) FROM system_role WHERE tenant_id = 1")
    role_count = cursor.fetchone()[0]
    print(f"  system_role表角色数: {role_count} 个")
    
    if role_count > 0:
        cursor.execute("SELECT id, name, remark FROM system_role WHERE tenant_id = 1 ORDER BY id")
        roles = cursor.fetchall()
        for role in roles:
            print(f"    ID:{role[0]} - {role[1]} ({role[2]})")
    
    # 3. 检查用户角色关联
    print(f"\n3. 检查用户角色关联:")
    cursor.execute("SELECT COUNT(*) FROM system_admin_role WHERE tenant_id = 1")
    admin_role_count = cursor.fetchone()[0]
    print(f"  system_admin_role表关联数: {admin_role_count} 个")
    
    # 4. 检查角色权限关联
    print(f"\n4. 检查角色权限关联:")
    cursor.execute("SELECT COUNT(*) FROM system_role_menu WHERE tenant_id = 1")
    role_menu_count = cursor.fetchone()[0]
    print(f"  system_role_menu表关联数: {role_menu_count} 个")
    
    # 5. 检查权限菜单
    print(f"\n5. 检查权限菜单:")
    cursor.execute("SELECT COUNT(*) FROM system_menu WHERE status = 1 AND deleted_at IS NULL")
    menu_count = cursor.fetchone()[0]
    print(f"  system_menu表权限数: {menu_count} 个")
    
    cursor.execute("SELECT COUNT(*) FROM system_menu WHERE status = 1 AND deleted_at IS NULL AND type = 1")
    menu_type1_count = cursor.fetchone()[0]
    print(f"  菜单权限数: {menu_type1_count} 个")
    
    cursor.execute("SELECT COUNT(*) FROM system_menu WHERE status = 1 AND deleted_at IS NULL AND type = 2")
    menu_type2_count = cursor.fetchone()[0]
    print(f"  按钮权限数: {menu_type2_count} 个")
    
    # 6. 检查权限模块分布
    print(f"\n6. 权限模块分布:")
    cursor.execute("""
        SELECT 
            SUBSTRING_INDEX(name, ':', 1) as module,
            COUNT(*) as total,
            SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as menu_count,
            SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as button_count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL AND name LIKE '%:%'
        GROUP BY SUBSTRING_INDEX(name, ':', 1)
        ORDER BY total DESC
    """)
    
    modules = cursor.fetchall()
    for module in modules:
        module_name, total, menu_count, button_count = module
        print(f"  {module_name}: {total}个权限 (菜单:{menu_count}, 按钮:{button_count})")
    
    cursor.close()
    conn.close()
    
    print(f"\n=== 检查完成 ===")
    
    return {
        'admin_count': admin_count,
        'role_count': role_count,
        'admin_role_count': admin_role_count,
        'role_menu_count': role_menu_count,
        'menu_count': menu_count,
        'need_create_data': admin_count == 0 or role_count == 0
    }

if __name__ == "__main__":
    result = main()
    if result['need_create_data']:
        print(f"\n⚠️ 需要创建测试数据")
    else:
        print(f"\n✅ 数据已存在，可以更新")

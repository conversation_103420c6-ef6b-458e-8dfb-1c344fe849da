<?php
/**
 * 检查project模块的权限标识
 */

require_once 'vendor/autoload.php';

echo "=== 检查project模块的权限标识 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 查找所有project模块权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'project:%' AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $projectPermissions = $stmt->fetchAll();
    
    foreach ($projectPermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n2. 分析project权限命名规律:\n";
    
    $patterns = [];
    foreach ($projectPermissions as $perm) {
        $parts = explode(':', $perm['name']);
        if (count($parts) >= 2) {
            $pattern = $parts[1]; // project:xxx 中的 xxx 部分
            if (!in_array($pattern, $patterns)) {
                $patterns[] = $pattern;
            }
        }
    }
    
    echo "  发现的权限模式:\n";
    foreach ($patterns as $pattern) {
        echo "    project:{$pattern}\n";
    }
    
    echo "\n3. 建议的project权限解析规则:\n";
    echo "  ProjectController@index 应该解析为: project:project:index\n";
    echo "  ProjectMemberController@add 应该解析为: project:project_member:add\n";
    echo "  ProjectTaskController@edit 应该解析为: project:project_task:edit\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";

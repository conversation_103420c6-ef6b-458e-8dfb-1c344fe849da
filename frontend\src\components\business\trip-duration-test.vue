<template>
  <div class="trip-duration-test">
    <h2>出差时长算法一致性测试</h2>
    
    <el-card>
      <h3>测试说明</h3>
      <p>验证出差表单和出差明细表格使用相同的时长计算算法</p>
      
      <el-button @click="loadTestData" type="primary">加载测试数据</el-button>
      <el-button @click="clearData" type="warning">清空数据</el-button>
      
      <div style="margin-top: 15px;">
        <h4>测试场景:</h4>
        <ul>
          <li><strong>场景1</strong>: 2025-08-01 09:00:00 到 2025-08-01 18:00:00 (同一天，跨午休)</li>
          <li><strong>场景2</strong>: 2025-08-01 14:00:00 到 2025-08-02 12:00:00 (跨天)</li>
          <li><strong>场景3</strong>: 2025-08-01 09:30:00 到 2025-08-01 10:15:00 (短时间，测试半小时取整)</li>
        </ul>
      </div>
    </el-card>

    <el-card>
      <h3>出差明细表格测试</h3>
      <TripItemTable
        v-model="testItems"
        :readonly="false"
      />
      
      <div style="margin-top: 15px;">
        <h4>明细数据:</h4>
        <pre>{{ JSON.stringify(testItems, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card>
      <h3>出差表单时长计算测试</h3>
      <div style="border: 1px solid #ddd; padding: 20px; margin: 10px 0;">
        <el-form-item label="出差时长">
          <el-input
            v-model="formTotalDuration"
            style="width: 200px"
            readonly
            placeholder="自动计算"
          >
            <template #append>小时</template>
          </el-input>
          <div style="margin-top: 4px; color: #909399; font-size: 12px">
            按半小时向上取整规则自动计算（累计各行程时长）
          </div>
        </el-form-item>
      </div>
      
      <div>
        <h4>表单计算结果:</h4>
        <p><strong>总时长:</strong> {{ formTotalDuration }} 小时</p>
      </div>
    </el-card>

    <el-card>
      <h3>算法一致性验证</h3>
      <div style="padding: 15px; background: #f5f5f5; border-radius: 4px;">
        <div style="margin-bottom: 10px;">
          <strong>明细表格总计:</strong> 
          <span :style="{ color: tableTotal === formTotalDuration ? '#67c23a' : '#f56c6c' }">
            {{ tableTotal }} 小时
          </span>
        </div>
        <div style="margin-bottom: 10px;">
          <strong>表单计算结果:</strong> 
          <span :style="{ color: tableTotal === formTotalDuration ? '#67c23a' : '#f56c6c' }">
            {{ formTotalDuration }} 小时
          </span>
        </div>
        <div>
          <strong>一致性检查:</strong> 
          <el-tag :type="tableTotal === formTotalDuration ? 'success' : 'danger'">
            {{ tableTotal === formTotalDuration ? '✅ 算法一致' : '❌ 算法不一致' }}
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import TripItemTable from './TripItemTable.vue'
import { calculateWorkingHoursWithHalfHourRule } from '@/utils/date'

// 测试数据
const testItems = ref([])

// 测试数据模板
const testDataTemplate = [
  {
    transport_type: 1,
    trip_mode: 2,
    departure_city: '北京市',
    destination_city: '上海市',
    departure_city_code: [1, 1101],
    destination_city_code: [2, 2101],
    start_time: '2025-08-01 09:00:00',
    end_time: '2025-08-01 18:00:00',
    duration: 0
  },
  {
    transport_type: 2,
    trip_mode: 2,
    departure_city: '上海市',
    destination_city: '广州市',
    departure_city_code: [2, 2101],
    destination_city_code: [3, 4401],
    start_time: '2025-08-01 14:00:00',
    end_time: '2025-08-02 12:00:00',
    duration: 0
  },
  {
    transport_type: 4,
    trip_mode: 2,
    departure_city: '广州市',
    destination_city: '深圳市',
    departure_city_code: [3, 4401],
    destination_city_code: [3, 4403],
    start_time: '2025-08-01 09:30:00',
    end_time: '2025-08-01 10:15:00',
    duration: 0
  }
]

// 表单总时长计算（复制自出差表单的算法）
const formTotalDuration = computed(() => {
  if (testItems.value && testItems.value.length > 0) {
    let totalHours = 0

    testItems.value.forEach((item: any) => {
      if (item.start_time && item.end_time) {
        // 使用新的工作时间计算方法
        // 默认工作时间配置：08:00-12:00,14:00-18:00
        const hours = calculateWorkingHoursWithHalfHourRule(
          item.start_time,
          item.end_time,
          '08:00-12:00,14:00-18:00'
        )
        totalHours += hours
      }
    })

    return totalHours
  } else {
    return 0
  }
})

// 明细表格总计
const tableTotal = computed(() => {
  return testItems.value.reduce((sum, item) => sum + (item.duration || 0), 0)
})

// 测试方法
const loadTestData = () => {
  testItems.value = JSON.parse(JSON.stringify(testDataTemplate))
  ElMessage.success('测试数据已加载，请观察时长计算结果')
}

const clearData = () => {
  testItems.value = []
  ElMessage.info('数据已清空')
}

// 监听数据变化
watch(testItems, () => {
  console.log('测试数据变化:', testItems.value)
  console.log('表单计算结果:', formTotalDuration.value)
  console.log('明细表格总计:', tableTotal.value)
  console.log('算法一致性:', tableTotal.value === formTotalDuration.value)
}, { deep: true })

onMounted(() => {
  ElMessage.info('出差时长算法一致性测试组件已加载')
})
</script>

<style scoped>
.trip-duration-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.trip-duration-test .el-card {
  margin-bottom: 20px;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #409eff;
}

h3, h4 {
  margin-bottom: 15px;
  color: #303133;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

ul {
  margin: 10px 0;
  padding-left: 20px;
}

li {
  margin-bottom: 5px;
}
</style>

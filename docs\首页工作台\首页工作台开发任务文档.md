# 首页工作台开发任务文档

## 📋 项目概述

**项目名称**：首页工作台开发  
**创建日期**：2025-01-14  
**版本**：v1.0  
**开发目标**：基于现有系统菜单，开发高效直观的首页工作台

## 🎯 功能需求确认

### 第一行：关键指标概览区（3个卡片）
- ✅ **客户统计**：总数、本月新增、环比增长
- ✅ **合同金额**：总额、本月签约、完成率  
- ✅ **项目进度**：进行中项目、整体完成率

### 第二行：任务与快捷操作区

#### 左侧：我的待办任务
- ✅ **工作流审批任务**：复用现有API
- ✅ **CRM客户跟进**：需要开发聚合API
- ✅ **项目任务管理**：需要筛选当前用户任务
- ✅ **统一列表显示**：按优先级和时间排序

#### 右侧：快捷操作面板（5个入口）
基于system_menu表数据确认的路由：

| 快捷入口 | 菜单ID | 路由路径 | 组件路径 | 状态 |
|---------|--------|----------|----------|------|
| 我的客户 | - | `/crm/crm_customer_my` | 需确认 | ✅ 可用 |
| 工作汇报 | 2581 | `/crm_work_report` | `/crm/crm_work_report/list` | ✅ 已存在 |
| 我的申请 | - | `/workflow/application` | 需确认 | ✅ 可用 |
| 项目管理 | 2441 | `/project_list` | `/project/ProjectList` | ✅ 已存在 |
| 每日报价 | 2600 | `/daily_price_order` | `/daily/daily_price_order/list` | ✅ 已存在 |

### 第三行：汇报与新闻资讯区

#### 左侧：最新工作汇报
- **数据源**：菜单ID 2581 - 工作汇报模块
- **路由**：`/crm_work_report`
- **组件**：`/crm/crm_work_report/list`

#### 右侧：企业新闻资讯  
- **数据源**：菜单ID 107 - 新闻列表，父级2582 - 公司新闻
- **路由**：`/article_list`
- **组件**：`/system/article/list`

## 🔧 技术实施方案

### 📊 第一行：关键指标概览区

#### 后端API开发
```php
// app/dashboard/controller/WorkbenchController.php
class WorkbenchController extends BaseController 
{
    /**
     * 获取关键指标统计
     */
    public function getKeyStatistics(): Json
    {
        $service = new WorkbenchStatisticsService();
        $data = [
            'customer_stats' => $service->getCustomerStatistics(),
            'contract_stats' => $service->getContractStatistics(), 
            'project_stats' => $service->getProjectStatistics()
        ];
        return $this->success('获取成功', $data);
    }
}
```

#### 前端组件开发
```vue
<!-- KeyStatistics.vue -->
<template>
  <div class="key-statistics">
    <div class="stat-card">
      <div class="stat-header">
        <h4>客户统计</h4>
        <i class="icon-customer"></i>
      </div>
      <div class="stat-content">
        <div class="main-number">{{ customerStats.total }}</div>
        <div class="sub-info">
          <span>本月新增: {{ customerStats.monthlyNew }}</span>
          <span class="growth" :class="customerStats.growthClass">
            {{ customerStats.growth }}
          </span>
        </div>
      </div>
    </div>
    <!-- 其他卡片... -->
  </div>
</template>
```

### 📋 第二行左侧：我的待办任务

#### 后端API开发
```php
// app/dashboard/service/TodoAggregationService.php
class TodoAggregationService 
{
    public function aggregateUserTodos(int $userId): array
    {
        return [
            'workflow_tasks' => $this->getWorkflowTodos($userId),
            'customer_follow' => $this->getCustomerFollowTodos($userId),
            'project_tasks' => $this->getProjectTaskTodos($userId)
        ];
    }
    
    private function getWorkflowTodos(int $userId): array
    {
        // 调用现有工作流API
        return WorkflowTaskService::getInstance()
            ->getMyTasks($userId, ['status' => 'pending']);
    }
    
    private function getCustomerFollowTodos(int $userId): array
    {
        // 基于CRM跟进记录表
        return CrmFollowRecordService::getInstance()
            ->getFollowReminders($userId);
    }
    
    private function getProjectTaskTodos(int $userId): array
    {
        // 基于项目任务表
        return ProjectTaskService::getInstance()
            ->getMyTasks($userId, ['status' => ['pending', 'in_progress']]);
    }
}
```

#### 前端组件开发
```vue
<!-- TodoTasks.vue -->
<template>
  <div class="todo-tasks-widget">
    <div class="widget-header">
      <h4>我的待办任务</h4>
      <span class="total-count">({{ totalCount }})</span>
    </div>
    
    <div class="todo-list">
      <div 
        v-for="task in allTasks" 
        :key="`${task.type}-${task.id}`"
        class="task-item"
        :class="{ 'urgent': task.urgent }"
        @click="handleTaskClick(task)"
      >
        <ElTag :type="getTaskTypeColor(task.type)" size="small">
          {{ getTaskTypeText(task.type) }}
        </ElTag>
        <span class="task-title">{{ task.title }}</span>
        <span class="task-time">{{ formatTime(task.time) }}</span>
        <ElTag v-if="task.urgent" type="danger" size="small">⚠️</ElTag>
      </div>
    </div>
  </div>
</template>
```

### 🚀 第二行右侧：快捷操作面板

#### 快捷操作配置
```typescript
// 基于system_menu表的实际数据配置
const quickActions = [
  {
    name: '我的客户',
    icon: '👥',
    path: '/crm/crm_customer_my',
    menuId: null, // 需要确认对应的菜单ID
    color: 'primary'
  },
  {
    name: '工作汇报',
    icon: '📄', 
    path: '/crm_work_report',
    menuId: 2581,
    color: 'success'
  },
  {
    name: '我的申请',
    icon: '📊',
    path: '/workflow/application', 
    menuId: null, // 需要确认对应的菜单ID
    color: 'warning'
  },
  {
    name: '项目管理',
    icon: '📋',
    path: '/project_list',
    menuId: 2441,
    color: 'info'
  },
  {
    name: '每日报价',
    icon: '💰',
    path: '/daily_price_order',
    menuId: 2600,
    color: 'danger'
  }
]
```

#### 前端组件开发
```vue
<!-- QuickActions.vue -->
<template>
  <div class="quick-actions-panel">
    <div class="panel-header">
      <h4>快捷操作</h4>
    </div>
    <div class="actions-grid">
      <div 
        v-for="action in quickActions" 
        :key="action.name"
        class="action-item"
        :class="`action-${action.color}`"
        @click="handleActionClick(action)"
      >
        <div class="action-icon">{{ action.icon }}</div>
        <div class="action-name">{{ action.name }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const handleActionClick = (action) => {
  router.push(action.path)
}
</script>
```

### 📄 第三行：汇报与新闻资讯区

#### 工作汇报组件
```vue
<!-- WorkReports.vue -->
<template>
  <div class="work-reports-widget">
    <div class="widget-header">
      <h4>最新工作汇报</h4>
      <el-button text @click="viewAllReports">查看更多</el-button>
    </div>
    <div class="reports-list">
      <div v-for="report in reports" :key="report.id" class="report-item">
        <div class="report-header">
          <span class="reporter">{{ report.reporter_name }}</span>
          <span class="report-type">{{ report.type_text }}</span>
          <span class="report-time">{{ formatTime(report.created_at) }}</span>
        </div>
        <div class="report-content">{{ report.summary }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const viewAllReports = () => {
  router.push('/crm_work_report')
}
</script>
```

#### 企业新闻组件
```vue
<!-- CompanyNews.vue -->
<template>
  <div class="company-news-widget">
    <div class="widget-header">
      <h4>企业新闻资讯</h4>
      <el-button text @click="viewAllNews">查看更多</el-button>
    </div>
    <div class="news-list">
      <div v-for="news in newsList" :key="news.id" class="news-item">
        <div class="news-title">{{ news.title }}</div>
        <div class="news-meta">
          <span class="news-time">{{ formatTime(news.created_at) }}</span>
          <el-tag v-if="news.is_important" type="danger" size="small">重要</el-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const viewAllNews = () => {
  router.push('/article_list')
}
</script>
```

## 📊 开发工作量评估

### 后端开发（6天）
| 模块 | 工作项 | 工作量 | 难度 |
|------|--------|--------|------|
| 关键指标API | 客户、合同、项目统计接口 | 1.5天 | 简单 |
| 待办任务API | 聚合工作流、CRM、项目待办 | 3天 | 中等 |
| 工作汇报API | 最新汇报摘要接口 | 0.5天 | 简单 |
| 企业新闻API | 新闻列表接口 | 0.5天 | 简单 |
| 路由配置 | 新增工作台相关路由 | 0.5天 | 简单 |

### 前端开发（6天）
| 模块 | 工作项 | 工作量 | 难度 |
|------|--------|--------|------|
| 关键指标组件 | 3个统计卡片组件 | 1天 | 简单 |
| 待办任务组件 | 统一待办列表组件 | 2天 | 中等 |
| 快捷操作组件 | 5个快捷入口面板 | 1天 | 简单 |
| 工作汇报组件 | 汇报摘要显示组件 | 1天 | 简单 |
| 企业新闻组件 | 新闻列表显示组件 | 1天 | 简单 |

### 系统集成与测试（4天）
| 阶段 | 工作项 | 工作量 | 难度 |
|------|--------|--------|------|
| 页面集成 | Dashboard主页面集成 | 1天 | 中等 |
| 响应式调优 | 移动端适配 | 1天 | 中等 |
| 功能测试 | 各模块功能测试 | 1天 | 简单 |
| 性能优化 | 缓存、懒加载等 | 1天 | 中等 |

### **总工作量：16天**

## 🎯 实施计划

### 第一阶段：后端API开发（6天）
- [ ] Day 1-1.5：关键指标统计API开发
- [ ] Day 2-4：待办任务聚合API开发（重点）
- [ ] Day 5：工作汇报和企业新闻API开发
- [ ] Day 6：路由配置和API测试

### 第二阶段：前端组件开发（6天）
- [ ] Day 7：关键指标卡片组件开发
- [ ] Day 8-9：待办任务组件开发（重点）
- [ ] Day 10：快捷操作面板组件开发
- [ ] Day 11：工作汇报组件开发
- [ ] Day 12：企业新闻组件开发

### 第三阶段：系统集成（4天）
- [ ] Day 13：Dashboard主页面集成
- [ ] Day 14：响应式样式调优
- [ ] Day 15：功能测试和bug修复
- [ ] Day 16：性能优化和上线准备

## 🔍 风险评估与应对

### 高风险项
1. **待办任务数据聚合复杂度**
   - 风险：不同模块数据结构差异大
   - 应对：提前调研各模块数据结构，设计统一的数据格式

2. **快捷入口路由验证**
   - 风险：部分路由可能不存在或权限问题
   - 应对：逐一验证每个快捷入口的可用性

### 中风险项
1. **性能优化需求**
   - 风险：首页加载速度可能较慢
   - 应对：采用懒加载、缓存等优化策略

## ✅ 验收标准

### 功能完整性
- [ ] 3个关键指标卡片正常显示统计数据
- [ ] 待办任务列表正确聚合并排序显示
- [ ] 5个快捷操作入口正常跳转
- [ ] 工作汇报摘要正常显示
- [ ] 企业新闻列表正常显示

### 性能指标
- [ ] 首页加载时间 < 2秒
- [ ] 数据刷新响应时间 < 1秒
- [ ] 移动端适配良好

### 用户体验
- [ ] 界面美观，符合设计规范
- [ ] 交互流畅，无明显卡顿
- [ ] 错误处理友好

---

**准备开始**：请确认以上开发任务文档，确认后开始实施代码开发工作！

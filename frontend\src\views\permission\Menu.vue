<template>
  <ArtTableFullScreen>
    <div class="menu-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <!--      <ArtSearchBar
              v-model:filter="formFilters"
              :items="formItems"
              :showExpand="false"
              @reset="handleReset"
              @search="handleSearch"
            ></ArtSearchBar>-->

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          :showZebra="false"
          v-model:columns="columnChecks"
          @refresh="handleRefresh"
        >
          <template #left>
            <ElButton
              @click="showModel('menu', null, true)"
              icon="Plus"
              v-auth="'system:permission_menu:add'"
              type="primary"
              v-ripple
            >
              新增
            </ElButton>
            <ElButton @click="toggleExpand" v-ripple>
              {{ isExpanded ? '收起' : '展开' }}
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <div v-if="loading" class="skeleton-loading">
          <!-- 简单的骨架屏 -->
          <div v-for="i in 6" :key="i" class="skeleton-row">
            <div class="skeleton-item" style="width: 20%"></div>
            <div class="skeleton-item" style="width: 15%"></div>
            <div class="skeleton-item" style="width: 25%"></div>
            <div class="skeleton-item" style="width: 20%"></div>
            <div class="skeleton-item" style="width: 20%"></div>
          </div>
        </div>

        <ArtTable
          v-else
          ref="tableRef"
          :loading="false"
          :data="tableData"
          :marginTop="10"
          :stripe="false"
          :default-expand-all="isExpanded"
          :pagination="false"
        >
          <template #default>
            <ElTableColumn v-for="col in columns" :key="col.prop || col.type" v-bind="col" />
          </template>
        </ArtTable>

        <ElDialog :title="dialogTitle" v-model="dialogVisible" width="700px" align-center>
          <ElForm ref="formRef" :model="form" :rules="rules" label-width="85px">
            <ElFormItem label="菜单类型">
              <ElRadioGroup v-model="labelPosition" :disabled="disableMenuType">
                <ElRadioButton :value="'menu'">菜单</ElRadioButton>
                <ElRadioButton :value="'button'">权限</ElRadioButton>
              </ElRadioGroup>
            </ElFormItem>

            <template v-if="labelPosition === 'menu'">
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="菜单名称" prop="title">
                    <ElInput v-model="form.title" placeholder="菜单名称"></ElInput>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="图标" prop="icon">
                    <ArtIconSelector
                      :iconType="iconType"
                      :defaultIcon="form.icon"
                      @getIcon="getFormIcon"
                      width="229px"
                    />
                  </ElFormItem>
                </ElCol>
              </ElRow>
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="权限标识" prop="name">
                    <ElInput v-model="form.name" placeholder="权限标识"></ElInput>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="菜单排序" prop="sort" style="width: 100%">
                    <ElInputNumber
                      v-model="form.sort"
                      style="width: 100%"
                      @change="handleChange"
                      :min="1"
                      controls-position="right"
                    />
                  </ElFormItem>
                </ElCol>
              </ElRow>

              <ElFormItem label="路由地址" prop="path">
                <ElInput v-model="form.path" placeholder="页面访问地址(以'/'开头)"></ElInput>
              </ElFormItem>

              <ElFormItem label="视图地址" prop="component">
                <ElInput
                  v-model="form.component"
                  placeholder="视图地址，请指向views目录文件"
                ></ElInput>
              </ElFormItem>

              <ElFormItem label="外部链接" prop="link">
                <ElInput
                  v-model="form.link"
                  placeholder="第三方Url,格式:https://www.xxx.com"
                ></ElInput>
              </ElFormItem>

              <ElRow :gutter="20">
                <ElCol :span="5">
                  <ElFormItem label="是否启用" prop="status">
                    <ElSwitch v-model="form.status" :active-value="1"></ElSwitch>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="5">
                  <ElFormItem label="页面缓存" prop="keep_alive">
                    <ElSwitch v-model="form.keep_alive" :active-value="1"></ElSwitch>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="5">
                  <ElFormItem label="是否显示" prop="isHidden">
                    <ElSwitch v-model="form.visible" :active-value="1"></ElSwitch>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="5">
                  <ElFormItem label="是否内嵌" prop="external">
                    <ElSwitch v-model="form.external" :active-value="1"></ElSwitch>
                  </ElFormItem>
                </ElCol>
              </ElRow>

              <!--              <ElFormItem label="权限描述" prop="description">
                              <ArtWangEditor v-model="form.description" />
                            </ElFormItem>-->
            </template>

            <template v-if="labelPosition === 'button'">
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="权限名称" prop="title">
                    <ElInput v-model="form.title" placeholder="权限名称"></ElInput>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="权限标识" prop="name">
                    <ElInput v-model="form.name" placeholder="权限标识"></ElInput>
                  </ElFormItem>
                </ElCol>
              </ElRow>
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="权限排序" prop="sort" style="width: 100%">
                    <ElInputNumber
                      v-model="form.sort"
                      style="width: 100%"
                      @change="handleChange"
                      :min="1"
                      controls-position="right"
                    />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </template>
          </ElForm>

          <template #footer>
            <span class="dialog-footer">
              <ElButton @click="dialogVisible = false">取 消</ElButton>
              <ElButton type="primary" @click="submitForm()">确 定</ElButton>
            </span>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped>
  /* 简单骨架屏样式 */
  .skeleton-loading {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
  }

  .skeleton-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    align-items: center;
  }

  .skeleton-item {
    height: 20px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
    border-radius: 4px;
  }

  @keyframes skeleton-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
</style>

<script setup lang="ts">
  import type { FormInstance, FormRules } from 'element-plus'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { IconTypeEnum } from '@/enums/appEnum'
  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import { ElPopover, ElButton } from 'element-plus'
  import { MenuListType } from '@/types/menu'
  import { MenuApi } from '@/api/menuApi'
  import { useAuth } from '@/composables/useAuth'

  const { hasAuth } = useAuth()

  const loading = ref(false)

  // 列配置
  const columnOptions = [
    // { label: '勾选', type: 'selection' },
    { label: '名单', prop: 'title' },
    { label: '标识', prop: 'name' },
    { label: '路由地址', prop: 'path' },
    { label: '组件路径', prop: 'component' },
    { label: '状态', prop: 'status' },
    { label: '创建时间', prop: 'created_at' },
    { label: '操作', prop: 'operation' }
  ]

  // 构建菜单类型标签
  const buildMenuTypeTag = (row: MenuListType) => {
    if (row.children && row.children.length > 0) {
      return 'info'
    } else if (row.meta?.link && row.meta?.isIframe) {
      return 'success'
    } else if (row.path) {
      return 'primary'
    } else if (row.meta?.link) {
      return 'warning'
    }
  }

  // 构建菜单类型文本
  const buildMenuTypeText = (row: MenuListType) => {
    if (row.children && row.children.length > 0) {
      return '目录'
    } else if (row.meta?.link && row.meta?.isIframe) {
      return '内嵌'
    } else if (row.path) {
      return '菜单'
    } else if (row.meta?.link) {
      return '外链'
    }
  }

  // 动态列配置
  const { columnChecks, columns } = useCheckedColumns(() => [
    {
      prop: 'title',
      label: '菜单名称',
      maxWidth: 220
    },
    {
      prop: 'type',
      label: '菜单类型',
      formatter: (row: MenuListType) => {
        return h(ElTag, { type: buildMenuTypeTag(row) }, () => buildMenuTypeText(row))
      }
    },
    {
      prop: 'path',
      label: '路由',
      formatter: (row: MenuListType) => {
        return row.meta?.link || row.path || ''
      }
    },
    {
      prop: 'meta.authList',
      label: '可操作权限',
      formatter: (row) => {
        return h(
          'div',
          { style: 'display: flex; flex-wrap: wrap; justify-content: flex-start;gap:10px;' },
          row.meta.authList?.map((item: MenuListType['meta'], index: number) => {
            const hasEdit = hasAuth('system:permission_menu:edit')
            const hasDelete = hasAuth('system:permission_menu:delete')

            if (!hasEdit && !hasDelete) {
              return h(
                ElButton,
                {
                  class: 'small-btn',
                  style: 'margin-left:0;',
                  disabled: true // 可选：禁用无权限按钮
                },
                { default: () => item.title }
              )
            }
            return h(
              ElPopover,
              {
                placement: 'top-start',
                title: '操作',
                width: 200,
                trigger: 'click',
                key: index
              },
              {
                default: () =>
                  h('div', { style: 'margin: 0; text-align: right' }, [
                    hasAuth('system:permission_menu:edit') &&
                      h(
                        ElButton,
                        {
                          size: 'small',
                          type: 'primary',
                          onClick: () => showModel('button', item)
                        },
                        { default: () => '编辑' }
                      ),
                    hasAuth('system:permission_menu:delete') &&
                      h(
                        ElButton,
                        {
                          size: 'small',
                          type: 'danger',
                          onClick: () => deleteAuth(item.id)
                        },
                        { default: () => '删除' }
                      )
                  ]),
                reference: () =>
                  h(
                    ElButton,
                    {
                      class: 'small-btn',
                      style: 'margin-left:0;'
                    },
                    { default: () => item.title }
                  )
              }
            )
          })
        )
      }
    },
    {
      prop: 'visible',
      label: '隐藏菜单',
      width: 120,
      formatter: (row) => {
        return h(ElTag, { type: row.visible === 0 ? 'danger' : 'info' }, () =>
          row.visible === 0 ? '是' : '否'
        )
      }
    },
    {
      prop: 'operation',
      label: '操作',
      width: 260,
      formatter: (row: MenuListType) => {
        return h('div', [
          hasAuth('system:permission_menu:add') &&
            h(ArtButtonTable, {
              text: '添加',
              type: 'add',
              onClick: () => showModel('menu', null, false, row.id)
            }),
          hasAuth('system:permission_menu:edit') &&
            h(ArtButtonTable, {
              text: '编辑',
              type: 'edit',
              onClick: () => showDialog('edit', row)
            }),
          hasAuth('system:permission_menu:delete') &&
            h(ArtButtonTable, {
              text: '删除',
              type: 'delete',
              onClick: () => deleteMenu(row.id)
            })
        ])
      }
    }
  ])

  const handleRefresh = () => {
    getTableData()
  }

  const dialogVisible = ref(false)
  const form = reactive({
    // 菜单
    parent_id: 0,
    title: '',
    name: '',
    path: '',
    component: '',
    type: 0,
    icon: '',
    status: 1,
    sort: 1,
    keep_alive: 1,
    visible: 1,
    link: '',
    external: 0,
    // description:'',
    // 权限 (修改这部分)
    authName: '',
    authLabel: '',
    authIcon: '',
    authSort: 1
  })
  const iconType = ref(IconTypeEnum.UNICODE)

  const labelPosition = ref('menu')
  const rules = reactive<FormRules>({
    title: [
      { required: true, message: '请输入菜单名称', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '请输入权限唯一标识', trigger: 'blur' },
      { min: 2, max: 150, message: '长度在 2 到 150 个字符', trigger: 'blur' }
    ],
    path: [{ required: true, message: '请输入路由地址', trigger: 'blur' }],
    // 修改这部分
    authName: [{ required: true, message: '请输入权限名称', trigger: 'blur' }],
    authLabel: [{ required: true, message: '请输入权限权限标识', trigger: 'blur' }]
  })

  const tableData = ref<any[]>([])

  onMounted(() => {
    getTableData()
  })

  const getTableData = async () => {
    loading.value = true

    try {
      const res = await MenuApi.list()

      if (res.code === 1) {
        tableData.value = res.data || []

        // 等待Vue渲染完成后再隐藏loading
        await nextTick()
        setTimeout(() => {
          loading.value = false
        }, 100)
      } else {
        loading.value = false
      }
    } catch (error) {
      console.error('获取菜单数据失败:', error)
      loading.value = false
    }
  }

  // 过滤后的表格数据
  /*const filteredTableData = computed(() => {
  // 递归搜索函数
  const searchMenu = (items: MenuListType[]): MenuListType[] => {
  return items.filter((item) => {
  // 获取搜索关键词，转换为小写并去除首尾空格
  const searchName = appliedFilters.name?.toLowerCase().trim() || ''
  const searchRoute = appliedFilters.route?.toLowerCase().trim() || ''

  // 获取菜单标题和路径，确保它们存在
  const menuTitle = formatMenuTitle(item.meta?.title || '').toLowerCase()
  const menuPath = (item.path || '').toLowerCase()

  // 使用 includes 进行模糊匹配
  const nameMatch = !searchName || menuTitle.includes(searchName)
  const routeMatch = !searchRoute || menuPath.includes(searchRoute)

  // 如果有子菜单，递归搜索
  if (item.children && item.children.length > 0) {
  const matchedChildren = searchMenu(item.children)
  // 如果子菜单有匹配项，保留当前菜单
  if (matchedChildren.length > 0) {
  item.children = matchedChildren
  return true
  }
  }

  return nameMatch && routeMatch
  })
  }

  return searchMenu(tableData.value)
  })*/

  const isEdit = ref(false)
  const formRef = ref<FormInstance>()
  const dialogTitle = computed(() => {
    const type = labelPosition.value === 'menu' ? '菜单' : '权限'
    return isEdit.value ? `编辑${type}` : `新建${type}`
  })

  const getFormIcon = (icon: string) => {
    form.icon = icon
  }

  // 添加当前编辑菜单ID
  const currentMenuId = ref(0)

  const showDialog = (type: string, row: MenuListType) => {
    if (row && row.id) {
      showModel('menu', row, true)
    }
  }

  const handleChange = () => {}

  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          const params =
            labelPosition.value === 'menu'
              ? {
                  title: form.title,
                  path: form.path,
                  name: form.name,
                  icon: form.icon,
                  sort: form.sort,
                  status: form.status,
                  type: 1,
                  keep_alive: form.keep_alive,
                  visible: form.visible,
                  link: form.link,
                  external: form.external,
                  component: form.component
                }
              : {
                  title: form.title,
                  name: form.name,
                  sort: form.sort,
                  type: 2
                }
          params.parent_id = form.parent_id
          let res
          if (isEdit.value) {
            // 编辑菜单
            res = await MenuApi.edit(currentMenuId.value, params)
          } else {
            // 新增菜单
            res = await MenuApi.add(params)
          }

          if (res.code === 1) {
            ElMessage.success(`${isEdit.value ? '编辑' : '新增'}成功`)
            dialogVisible.value = false
            await getTableData() // 刷新列表
          }
        } catch (error) {
          console.error(`${isEdit.value ? '编辑' : '新增'}失败`, error)
          // ElMessage.error(`${isEdit.value ? '编辑' : '新增'}失败`)
        }
      }
    })
  }

  const showModel = async (type: string, row?: any, lock: boolean = false, parentId?: number) => {
    dialogVisible.value = true
    labelPosition.value = type
    isEdit.value = false
    lockMenuType.value = lock
    resetForm()
    if (row) {
      isEdit.value = true
      const res = await MenuApi.detail(row.id)
      if (res.code === 1 && res.data) {
        await nextTick(() => {
          form.parent_id = res.data.parent_id
          // 权限按钮数据回显
          form.title = res.data.title
          form.name = res.data.name
          form.sort = res.data.sort || 1
          currentMenuId.value = row.id
          // 回显数据
          if (type === 'menu') {
            // 菜单数据回显
            form.path = res.data.path
            form.icon = res.data.icon
            form.type = res.data.type
            form.keep_alive = res.data.keep_alive
            form.visible = res.data.visible
            form.component = res.data.component
            form.status = res.data.status
            form.link = res.data.link
            form.external = res.data.external
          }
        })
      }
    } else {
      await nextTick(() => {
        if (parentId) {
          form.parent_id = parentId
        }
      })
    }
  }

  const resetForm = () => {
    formRef.value?.resetFields()
    Object.assign(form, {
      // 菜单
      parent_id: 0,
      title: '',
      name: '',
      path: '',
      component: '',
      icon: '',
      sort: 1,
      isMenu: true,
      keepAlive: true,
      isHidden: true,
      link: '',
      isIframe: false
    })
  }

  const deleteMenu = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除该菜单吗？删除后无法恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await MenuApi.delete([id])
      if (res.code === 1) {
        ElMessage.success('删除成功')
        await getTableData() // 刷新列表
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除菜单失败', error)
      }
    }
  }

  const deleteAuth = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除该权限吗？删除后无法恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await MenuApi.delete([id])
      if (res.code === 1) {
        ElMessage.success('删除成功')
        await getTableData() // 刷新列表
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除权限失败', error)
      }
    }
  }

  // 修改计算属性，增加锁定控制参数
  const disableMenuType = computed(() => {
    // 编辑权限时锁定为权限类型
    if (isEdit.value && labelPosition.value === 'button') return true
    // 编辑菜单时锁定为菜单类型
    if (isEdit.value && labelPosition.value === 'menu') return true
    // 顶部添加菜单按钮时锁定为菜单类型
    return !isEdit.value && labelPosition.value === 'menu' && lockMenuType.value
  })

  // 添加一个控制变量
  const lockMenuType = ref(false)

  const isExpanded = ref(false)
  const tableRef = ref()

  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value[isExpanded.value ? 'expandAll' : 'collapseAll']()
      }
    })
  }
</script>

<style lang="scss" scoped>
  .menu-page {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }

    :deep(.small-btn) {
      height: 30px !important;
      padding: 0 10px !important;
      font-size: 12px !important;
    }
  }
</style>

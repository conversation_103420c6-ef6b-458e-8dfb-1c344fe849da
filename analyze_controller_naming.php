<?php
/**
 * 控制器命名规范分析脚本
 * 分析所有控制器文件的命名规范，生成统一规范方案
 */

echo "=== 控制器命名规范分析 ===\n\n";

// 扫描所有控制器文件
function scanControllers($dir, $prefix = '') {
    $controllers = [];
    $items = scandir($dir);
    
    foreach ($items as $item) {
        if ($item == '.' || $item == '..') continue;
        
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        $relativePath = $prefix . $item;
        
        if (is_dir($path)) {
            // 递归扫描子目录
            $subControllers = scanControllers($path, $relativePath . '/');
            $controllers = array_merge($controllers, $subControllers);
        } elseif (is_file($path) && pathinfo($item, PATHINFO_EXTENSION) == 'php') {
            // 分析PHP文件
            $content = file_get_contents($path);
            if (preg_match('/class\s+(\w+)/', $content, $matches)) {
                $className = $matches[1];
                $controllers[] = [
                    'file' => $relativePath,
                    'path' => $path,
                    'class' => $className,
                    'hasController' => strpos($className, 'Controller') !== false,
                    'module' => explode('/', $relativePath)[0] ?? '',
                    'namespace' => $prefix
                ];
            }
        }
    }
    
    return $controllers;
}

// 扫描app目录下的所有控制器
$controllers = scanControllers('app');

echo "1. 控制器文件统计:\n";
echo "  总数: " . count($controllers) . " 个\n";

$withController = array_filter($controllers, function($c) { return $c['hasController']; });
$withoutController = array_filter($controllers, function($c) { return !$c['hasController']; });

echo "  带Controller后缀: " . count($withController) . " 个\n";
echo "  不带Controller后缀: " . count($withoutController) . " 个\n\n";

// 按模块分组
$moduleGroups = [];
foreach ($controllers as $controller) {
    $module = $controller['module'];
    $moduleGroups[$module][] = $controller;
}

echo "2. 按模块分组统计:\n";
foreach ($moduleGroups as $module => $moduleControllers) {
    $withCount = count(array_filter($moduleControllers, function($c) { return $c['hasController']; }));
    $withoutCount = count(array_filter($moduleControllers, function($c) { return !$c['hasController']; }));
    
    echo "  {$module}模块: 总计{" . count($moduleControllers) . "}个 (带后缀:{$withCount}, 不带后缀:{$withoutCount})\n";
}
echo "\n";

echo "3. 需要重命名的控制器文件:\n";
foreach ($withoutController as $controller) {
    $newClassName = $controller['class'] . 'Controller';
    $newFileName = str_replace('.php', 'Controller.php', $controller['file']);
    
    echo "  📁 {$controller['file']}\n";
    echo "    类名: {$controller['class']} → {$newClassName}\n";
    echo "    文件: {$controller['file']} → {$newFileName}\n";
    echo "    路径: {$controller['path']}\n";
    echo "\n";
}

echo "4. 需要更新的路由文件分析:\n";

// 扫描路由文件
function scanRoutes($dir) {
    $routes = [];
    $items = scandir($dir);
    
    foreach ($items as $item) {
        if ($item == '.' || $item == '..' || pathinfo($item, PATHINFO_EXTENSION) != 'php') continue;
        
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        $content = file_get_contents($path);
        
        // 查找路由定义
        if (preg_match_all('/Route::\w+\([^,]+,\s*[\'"]([^\'\"]+)[\'"]/', $content, $matches)) {
            foreach ($matches[1] as $routeTarget) {
                if (strpos($routeTarget, 'app\\') === 0) {
                    $routes[] = [
                        'file' => $item,
                        'target' => $routeTarget,
                        'path' => $path
                    ];
                }
            }
        }
    }
    
    return $routes;
}

$routes = scanRoutes('route');

echo "  路由文件数量: " . count(array_unique(array_column($routes, 'file'))) . " 个\n";
echo "  路由定义数量: " . count($routes) . " 个\n\n";

// 分析哪些路由需要更新
$needUpdateRoutes = [];
foreach ($routes as $route) {
    foreach ($withoutController as $controller) {
        $controllerPath = str_replace(['app/', '/'], ['app\\', '\\'], dirname($controller['file']));
        $expectedTarget = $controllerPath . '\\' . $controller['class'];
        
        if (strpos($route['target'], $expectedTarget) !== false) {
            $newTarget = str_replace($controller['class'], $controller['class'] . 'Controller', $route['target']);
            $needUpdateRoutes[] = [
                'file' => $route['file'],
                'old' => $route['target'],
                'new' => $newTarget,
                'controller' => $controller['class']
            ];
        }
    }
}

echo "5. 需要更新的路由引用:\n";
$routeFileGroups = [];
foreach ($needUpdateRoutes as $route) {
    $routeFileGroups[$route['file']][] = $route;
}

foreach ($routeFileGroups as $file => $fileRoutes) {
    echo "  📄 route/{$file}:\n";
    foreach ($fileRoutes as $route) {
        echo "    {$route['old']}\n";
        echo "    → {$route['new']}\n";
    }
    echo "\n";
}

echo "6. 修改工作量评估:\n";
echo "  需要重命名的控制器文件: " . count($withoutController) . " 个\n";
echo "  需要更新的路由文件: " . count($routeFileGroups) . " 个\n";
echo "  需要更新的路由引用: " . count($needUpdateRoutes) . " 个\n\n";

echo "7. 风险评估:\n";
echo "  🟢 低风险: 文件重命名不会影响URL路由\n";
echo "  🟢 低风险: 只需要更新类名引用，不涉及业务逻辑\n";
echo "  🟡 中风险: 需要确保所有路由引用都正确更新\n";
echo "  🟡 中风险: 需要重启服务器清除类缓存\n\n";

echo "8. 建议的实施步骤:\n";
echo "  1. 备份当前代码\n";
echo "  2. 批量重命名控制器文件\n";
echo "  3. 批量更新路由文件中的类名引用\n";
echo "  4. 更新权限解析逻辑\n";
echo "  5. 清除缓存并重启服务\n";
echo "  6. 全面测试所有功能\n\n";

echo "=== 分析完成 ===\n";

# 权限管理手册

**版本**: v2.0  
**更新日期**: 2025-01-31  
**适用系统**: CRM管理系统

---

## 🎯 权限管理概述

### 权限模型
系统采用 **RBAC（基于角色的访问控制）** 模型：
- **用户(User)**: 系统使用者
- **角色(Role)**: 权限的集合
- **权限(Permission)**: 具体的功能操作
- **租户(Tenant)**: 数据隔离单位

### 权限层级
```
租户 → 用户 → 角色 → 权限 → 功能
```

### 权限类型
- **菜单权限**: 控制页面访问
- **按钮权限**: 控制功能操作
- **数据权限**: 控制数据范围

---

## 👥 用户管理

### 查看用户列表
1. 进入 **系统管理 → 用户管理**
2. 查看当前租户下的所有用户
3. 支持按用户名、邮箱、状态筛选

### 添加新用户
1. 点击 **"添加用户"** 按钮
2. 填写用户基本信息：
   - **用户名**: 登录账号（必填）
   - **邮箱**: 用户邮箱（必填）
   - **密码**: 登录密码（必填）
   - **状态**: 启用/禁用
3. 点击 **"保存"** 完成创建

### 编辑用户信息
1. 在用户列表中点击 **"编辑"** 按钮
2. 修改用户信息
3. 点击 **"保存"** 确认修改

### 分配用户角色
1. 在用户列表中点击 **"分配角色"** 按钮
2. 选择要分配的角色（可多选）
3. 点击 **"确认"** 完成分配

### 重置用户密码
1. 在用户列表中点击 **"重置密码"** 按钮
2. 输入新密码
3. 点击 **"确认"** 完成重置

---

## 🎭 角色管理

### 查看角色列表
1. 进入 **系统管理 → 角色管理**
2. 查看当前租户下的所有角色
3. 显示角色名称、描述、状态等信息

### 添加新角色
1. 点击 **"添加角色"** 按钮
2. 填写角色信息：
   - **角色名称**: 角色的名称（必填）
   - **角色描述**: 角色的详细说明
   - **状态**: 启用/禁用
3. 点击 **"保存"** 完成创建

### 编辑角色信息
1. 在角色列表中点击 **"编辑"** 按钮
2. 修改角色信息
3. 点击 **"保存"** 确认修改

### 分配角色权限
1. 在角色列表中点击 **"分配权限"** 按钮
2. 在权限树中勾选要分配的权限
3. 支持按模块展开/收起
4. 点击 **"保存"** 完成分配

---

## 🔐 权限管理

### 权限结构
系统权限按模块组织：
```
├── 系统管理
│   ├── 用户管理
│   │   ├── 用户列表
│   │   ├── 添加用户
│   │   ├── 编辑用户
│   │   └── 删除用户
│   ├── 角色管理
│   └── 权限管理
├── CRM管理
│   ├── 我的客户
│   ├── 公海客户
│   ├── 线索管理
│   └── 商机管理
├── 项目管理
├── 工作流管理
└── 其他模块...
```

### 权限命名规范
```
格式: module:controller:action
示例:
- system:permission_admin:index  # 系统管理员列表
- crm:customer_my:add           # 添加我的客户
- project:task:edit             # 编辑项目任务
- workflow:application:delete   # 删除工作流申请
```

### 查看权限列表
1. 进入 **系统管理 → 权限管理**
2. 查看所有系统权限
3. 支持按模块、类型筛选

### 权限分配原则
- **最小权限原则**: 用户只拥有必要的权限
- **职责分离**: 不同角色拥有不同权限
- **权限继承**: 子权限继承父权限
- **动态调整**: 根据需要动态调整权限

---

## 🏢 多租户管理

### 租户隔离
- **数据隔离**: 不同租户的数据完全隔离
- **用户隔离**: 用户只能访问本租户数据
- **权限隔离**: 权限配置在租户内独立

### 租户管理员
每个租户都有独立的管理员：
- **租户超级管理员**: 拥有租户内所有权限
- **系统管理员**: 负责用户和权限管理
- **业务管理员**: 负责具体业务模块

### 租户配置
1. 进入 **系统管理 → 租户管理**
2. 查看租户基本信息
3. 配置租户级别的设置

---

## 📋 权限配置实例

### 实例1：销售人员权限配置
**角色**: 销售人员  
**权限范围**: CRM模块基础功能

```
✅ 允许的权限:
- crm:customer_my:index     # 查看我的客户
- crm:customer_my:add       # 添加客户
- crm:customer_my:edit      # 编辑客户
- crm:lead:index           # 查看线索
- crm:lead:add             # 添加线索
- crm:business:index       # 查看商机

❌ 禁止的权限:
- crm:customer_my:delete    # 删除客户
- system:admin:*           # 系统管理
- crm:statistics:*         # CRM统计
```

### 实例2：项目经理权限配置
**角色**: 项目经理  
**权限范围**: 项目管理全部功能

```
✅ 允许的权限:
- project:project:*         # 项目管理所有权限
- project:task:*           # 任务管理所有权限
- project:member:*         # 成员管理所有权限
- workflow:application:*   # 工作流申请权限

❌ 禁止的权限:
- system:admin:*           # 系统管理
- crm:*                   # CRM模块
```

### 实例3：系统管理员权限配置
**角色**: 系统管理员  
**权限范围**: 系统管理功能

```
✅ 允许的权限:
- system:permission_admin:* # 用户管理
- system:permission_role:*  # 角色管理
- system:permission_menu:*  # 权限管理
- system:log:*             # 日志管理

❌ 禁止的权限:
- crm:*                   # CRM模块
- project:*               # 项目模块
- workflow:definition:*   # 工作流定义
```

---

## 🔧 权限配置操作

### 快速配置角色权限
1. **选择角色模板**
   - 销售人员模板
   - 项目经理模板
   - 系统管理员模板
   - 普通员工模板

2. **批量权限操作**
   - 全选模块权限
   - 批量取消权限
   - 复制其他角色权限

3. **权限预览**
   - 预览权限配置效果
   - 检查权限冲突
   - 验证权限完整性

### 权限继承配置
1. **设置父角色**
   - 选择要继承的父角色
   - 子角色自动获得父角色权限
   - 可在父角色基础上增加权限

2. **权限覆盖**
   - 子角色可覆盖父角色权限
   - 支持权限的增加和减少
   - 保持权限层级关系

---

## 🔍 权限审计

### 权限变更日志
1. 进入 **系统管理 → 操作日志**
2. 筛选权限相关操作
3. 查看权限变更历史

### 用户权限报告
1. 生成用户权限报告
2. 导出权限配置清单
3. 定期审查权限分配

### 权限合规检查
- **权限冗余检查**: 发现重复权限
- **权限缺失检查**: 发现缺少的权限
- **权限冲突检查**: 发现权限冲突

---

## ⚠️ 注意事项

### 权限配置原则
1. **最小权限原则**: 只分配必要的权限
2. **职责分离**: 避免权限过度集中
3. **定期审查**: 定期检查权限分配
4. **及时回收**: 离职人员及时回收权限

### 安全建议
1. **强密码策略**: 要求使用强密码
2. **定期更换**: 定期更换密码
3. **登录监控**: 监控异常登录
4. **权限审计**: 定期进行权限审计

### 常见问题
1. **权限不生效**: 检查角色分配和权限配置
2. **权限冲突**: 检查多角色权限冲突
3. **权限缺失**: 检查权限是否正确分配
4. **登录失败**: 检查用户状态和密码

---

## 📞 技术支持

### 权限问题排查
1. **检查用户角色**: 确认用户是否分配了正确角色
2. **检查角色权限**: 确认角色是否拥有相应权限
3. **检查权限状态**: 确认权限是否启用
4. **清除缓存**: 清除权限缓存重新加载

### 联系支持
- **技术支持邮箱**: <EMAIL>
- **系统管理员**: <EMAIL>
- **紧急联系**: 请联系系统管理员

---

**权限管理是系统安全的核心，请严格按照规范进行配置和管理。**

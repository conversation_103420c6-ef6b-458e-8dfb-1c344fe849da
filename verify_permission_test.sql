-- =====================================================
-- 权限测试验证脚本
-- 执行日期：2025-01-31
-- 说明：验证权限测试数据和权限配置的正确性
-- =====================================================

-- =====================================================
-- 第一步：验证测试环境数据
-- =====================================================
SELECT '=== 权限测试环境验证 ===' as message;

-- 1.1 验证部门数据
SELECT '1. 部门数据验证' as step;
SELECT 
    id,
    name,
    parent_id,
    CASE 
        WHEN parent_id = 0 THEN '顶级部门'
        ELSE CONCAT('下级部门(父ID:', parent_id, ')')
    END as level_info
FROM system_dept 
WHERE tenant_id = 1 
ORDER BY parent_id, id;

-- 1.2 验证角色数据
SELECT '2. 角色数据验证' as step;
SELECT 
    id,
    name,
    data_scope,
    CASE data_scope
        WHEN 1 THEN '全部数据'
        WHEN 2 THEN '本部门'
        WHEN 3 THEN '本部门及以下'
        WHEN 4 THEN '仅本人'
        WHEN 5 THEN '自定义'
    END as data_scope_text,
    data_scope_dept_ids
FROM system_role 
WHERE tenant_id = 1 
ORDER BY id;

-- 1.3 验证用户数据
SELECT '3. 用户数据验证' as step;
SELECT 
    a.id,
    a.username,
    a.real_name,
    d.name as dept_name,
    a.status,
    CASE a.status WHEN 1 THEN '正常' ELSE '禁用' END as status_text
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- 1.4 验证用户角色关联
SELECT '4. 用户角色关联验证' as step;
SELECT 
    a.username,
    a.real_name,
    r.name as role_name,
    r.data_scope,
    CASE r.data_scope
        WHEN 1 THEN '全部数据'
        WHEN 2 THEN '本部门'
        WHEN 3 THEN '本部门及以下'
        WHEN 4 THEN '仅本人'
        WHEN 5 THEN '自定义'
    END as permission_scope
FROM system_admin a
LEFT JOIN system_admin_role ar ON a.id = ar.admin_id
LEFT JOIN system_role r ON ar.role_id = r.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- =====================================================
-- 第二步：验证按钮权限配置
-- =====================================================
SELECT '=== 按钮权限配置验证 ===' as message;

-- 2.1 各角色的菜单权限统计
SELECT '1. 角色菜单权限统计' as step;
SELECT 
    r.name as role_name,
    COUNT(rm.menu_id) as menu_count,
    GROUP_CONCAT(rm.menu_id ORDER BY rm.menu_id) as menu_ids
FROM system_role r
LEFT JOIN system_role_menu rm ON r.id = rm.role_id AND rm.tenant_id = 1
WHERE r.tenant_id = 1
GROUP BY r.id, r.name
ORDER BY r.id;

-- 2.2 关键菜单权限分配情况
SELECT '2. 关键菜单权限分配' as step;
SELECT 
    m.id as menu_id,
    m.title as menu_title,
    m.name as menu_name,
    m.type,
    CASE m.type 
        WHEN 0 THEN '目录'
        WHEN 1 THEN '菜单'
        WHEN 2 THEN '按钮'
    END as menu_type,
    GROUP_CONCAT(r.name ORDER BY r.id) as assigned_roles
FROM system_menu m
LEFT JOIN system_role_menu rm ON m.id = rm.menu_id AND rm.tenant_id = 1
LEFT JOIN system_role r ON rm.role_id = r.id AND r.tenant_id = 1
WHERE m.id IN (3, 37, 16, 17, 18, 38, 31, 189, 192, 2420, 2421, 2422, 2423, 2424, 2425, 2426, 2432)
GROUP BY m.id, m.title, m.name, m.type
ORDER BY m.id;

-- =====================================================
-- 第三步：数据权限测试准备
-- =====================================================
SELECT '=== 数据权限测试准备 ===' as message;

-- 3.1 创建测试客户数据（如果表存在）
SELECT '1. 准备创建测试客户数据' as step;

-- 检查是否存在CRM客户表
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'CRM客户表存在，可以创建测试数据'
        ELSE 'CRM客户表不存在，跳过客户数据测试'
    END as table_status
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'crm_customer';

-- 如果表存在，可以执行以下插入语句（手动执行）
/*
-- 清理现有测试客户数据
DELETE FROM crm_customer WHERE tenant_id = 1;

-- 创建测试客户数据
INSERT INTO crm_customer (name, phone, creator_id, tenant_id, created_at, updated_at) VALUES
('客户A-销售经理', '13800001001', 202, 1, NOW(), NOW()),
('客户B-销售组长', '13800001002', 203, 1, NOW(), NOW()),
('客户C-销售员工1', '13800001003', 204, 1, NOW(), NOW()),
('客户D-销售员工2', '13800001004', 205, 1, NOW(), NOW()),
('客户E-技术经理', '13800001005', 206, 1, NOW(), NOW()),
('客户F-技术员工', '13800001006', 207, 1, NOW(), NOW()),
('客户G-财务员工', '13800001007', 208, 1, NOW(), NOW()),
('客户H-自定义权限', '13800001008', 209, 1, NOW(), NOW());
*/

-- =====================================================
-- 第四步：数据权限验证查询
-- =====================================================
SELECT '=== 数据权限验证查询 ===' as message;

-- 4.1 模拟不同用户的数据权限查询
SELECT '1. 数据权限查询模拟' as step;

-- 全部数据权限测试（tenant_admin - user_id=201）
SELECT '租户超级管理员权限测试：' as test_type;
SELECT 
    a.id as creator_id,
    a.username as creator_name,
    a.real_name,
    d.name as dept_name,
    '应该能看到所有用户' as permission_note
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- 本部门及以下权限测试（sales_manager - user_id=202, dept_id=102）
SELECT '销售部经理权限测试：' as test_type;
SELECT 
    a.id as creator_id,
    a.username as creator_name,
    a.real_name,
    d.name as dept_name,
    CASE 
        WHEN d.id IN (102, 103, 104) THEN '✅ 应该能看到'
        ELSE '❌ 不应该看到'
    END as permission_result
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- 本部门权限测试（sales_leader1 - user_id=203, dept_id=103）
SELECT '销售一组长权限测试：' as test_type;
SELECT 
    a.id as creator_id,
    a.username as creator_name,
    a.real_name,
    d.name as dept_name,
    CASE 
        WHEN d.id = 103 THEN '✅ 应该能看到'
        ELSE '❌ 不应该看到'
    END as permission_result
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- 仅本人权限测试（sales_staff1 - user_id=204）
SELECT '销售一组员工权限测试：' as test_type;
SELECT 
    a.id as creator_id,
    a.username as creator_name,
    a.real_name,
    d.name as dept_name,
    CASE 
        WHEN a.id = 204 THEN '✅ 应该能看到'
        ELSE '❌ 不应该看到'
    END as permission_result
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- 自定义权限测试（custom_user - user_id=209, 权限部门：102,105）
SELECT '自定义权限用户测试：' as test_type;
SELECT 
    a.id as creator_id,
    a.username as creator_name,
    a.real_name,
    d.name as dept_name,
    CASE 
        WHEN d.id IN (102, 105) THEN '✅ 应该能看到'
        ELSE '❌ 不应该看到'
    END as permission_result
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
WHERE a.tenant_id = 1
ORDER BY a.id;

-- =====================================================
-- 第五步：测试结果汇总
-- =====================================================
SELECT '=== 测试结果汇总 ===' as message;

-- 5.1 环境数据统计
SELECT 
    '环境数据统计' as category,
    '部门数量' as item,
    COUNT(*) as count
FROM system_dept WHERE tenant_id = 1
UNION ALL
SELECT 
    '环境数据统计',
    '角色数量',
    COUNT(*)
FROM system_role WHERE tenant_id = 1
UNION ALL
SELECT 
    '环境数据统计',
    '用户数量',
    COUNT(*)
FROM system_admin WHERE tenant_id = 1
UNION ALL
SELECT 
    '环境数据统计',
    '用户角色关联',
    COUNT(*)
FROM system_admin_role WHERE tenant_id = 1
UNION ALL
SELECT 
    '环境数据统计',
    '角色菜单权限',
    COUNT(*)
FROM system_role_menu WHERE tenant_id = 1;

-- 5.2 测试账号清单
SELECT '=== 测试账号清单 ===' as message;
SELECT 
    CONCAT('账号: ', a.username) as login_info,
    CONCAT('密码: password') as password_info,
    CONCAT('姓名: ', a.real_name) as name_info,
    CONCAT('部门: ', d.name) as dept_info,
    CONCAT('角色: ', r.name) as role_info,
    CONCAT('权限: ', CASE r.data_scope
        WHEN 1 THEN '全部数据'
        WHEN 2 THEN '本部门'
        WHEN 3 THEN '本部门及以下'
        WHEN 4 THEN '仅本人'
        WHEN 5 THEN '自定义'
    END) as permission_info
FROM system_admin a
LEFT JOIN system_dept d ON a.dept_id = d.id
LEFT JOIN system_admin_role ar ON a.id = ar.admin_id
LEFT JOIN system_role r ON ar.role_id = r.id
WHERE a.tenant_id = 1
ORDER BY a.id;

SELECT '=== 权限测试验证完成！===' as final_message;
SELECT '=== 请使用上述测试账号登录系统进行实际权限测试 ===' as instruction;

# 数据库设计文档

**版本**: v1.0  
**更新日期**: 2025-01-31  
**文档状态**: 草稿

---

## 📊 数据库设计概述

### 设计原则
1. **职责分离**：物理文件存储与用户权限分离
2. **数据一致性**：通过外键约束和事务保证数据一致性
3. **性能优化**：合理的索引设计，支持高效查询
4. **扩展性**：预留扩展字段，支持未来功能扩展

### 核心表关系
```
system_attachment (物理文件表)
    ↑ 1:N
system_attachment_user (用户文件关联表)
    ↓ N:1
system_admin (用户表)
```

---

## 🗃️ 表结构设计

### 1. 物理文件表 (system_attachment)

#### 表结构
```sql
DROP TABLE IF EXISTS `system_attachment`;
CREATE TABLE `system_attachment` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `name` varchar(100) NOT NULL COMMENT '系统生成的文件名',
    `path` varchar(255) NOT NULL COMMENT '文件路径',
    `extension` varchar(10) DEFAULT NULL COMMENT '文件扩展名',
    `size` bigint(20) NOT NULL DEFAULT 0 COMMENT '文件大小(字节)',
    `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
    `storage` varchar(20) NOT NULL DEFAULT 'local' COMMENT '存储方式：local,qnoss,alioss,txoss',
    `storage_id` varchar(100) DEFAULT NULL COMMENT '存储平台ID(etag/hash等)',
    `file_md5` varchar(32) NOT NULL COMMENT '文件MD5值',
    `file_hash` varchar(64) DEFAULT NULL COMMENT '文件SHA256值(备用)',
    `ref_count` int(11) NOT NULL DEFAULT 0 COMMENT '引用计数',
    `storage_meta` json DEFAULT NULL COMMENT '存储平台元数据',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_storage_identifier` (`storage`, `file_md5`, `storage_id`),
    KEY `idx_file_md5` (`file_md5`),
    KEY `idx_storage_id` (`storage_id`),
    KEY `idx_storage` (`storage`),
    KEY `idx_ref_count` (`ref_count`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附件文件表';
```

#### 字段说明
| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | 自增 |
| name | varchar(100) | 系统生成文件名 | 用于存储和访问 |
| path | varchar(255) | 文件路径 | 完整的存储路径 |
| extension | varchar(10) | 文件扩展名 | 如：jpg, pdf, docx |
| size | bigint(20) | 文件大小 | 单位：字节 |
| mime_type | varchar(100) | MIME类型 | 如：image/jpeg |
| storage | varchar(20) | 存储方式 | local/qnoss/alioss/txoss |
| storage_id | varchar(100) | 存储平台ID | 云存储的etag或hash |
| file_md5 | varchar(32) | 文件MD5值 | 用于去重 |
| file_hash | varchar(64) | 文件SHA256值 | 备用hash算法 |
| ref_count | int(11) | 引用计数 | 被多少用户引用 |
| storage_meta | json | 存储元数据 | 云存储的额外信息 |

#### 索引设计
- **主键索引**：`PRIMARY KEY (id)`
- **唯一索引**：`uk_storage_identifier (storage, file_md5, storage_id)` - 防止重复文件
- **查询索引**：
  - `idx_file_md5 (file_md5)` - MD5查询
  - `idx_storage_id (storage_id)` - 存储ID查询
  - `idx_storage (storage)` - 按存储类型查询
  - `idx_ref_count (ref_count)` - 引用计数查询
  - `idx_created_at (created_at)` - 时间排序
  - `idx_deleted_at (deleted_at)` - 软删除查询

### 2. 用户文件关联表 (system_attachment_user)

#### 表结构
```sql
DROP TABLE IF EXISTS `system_attachment_user`;
CREATE TABLE `system_attachment_user` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `attachment_id` bigint(20) UNSIGNED NOT NULL COMMENT '附件ID',
    `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户分类ID',
    `display_name` varchar(255) NOT NULL COMMENT '用户自定义显示名称',
    `original_name` varchar(255) NOT NULL COMMENT '原始上传文件名',
    `upload_ip` varchar(45) DEFAULT NULL COMMENT '上传IP',
    `upload_source` varchar(50) DEFAULT 'web' COMMENT '上传来源：web,api,mobile',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_attachment_user_tenant` (`attachment_id`, `user_id`, `tenant_id`, `deleted_at`),
    KEY `idx_user_tenant` (`user_id`, `tenant_id`),
    KEY `idx_attachment_id` (`attachment_id`),
    KEY `idx_cate_user` (`cate_id`, `user_id`, `tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_creator_id` (`creator_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted_at` (`deleted_at`),
    CONSTRAINT `fk_attachment_user_attachment` FOREIGN KEY (`attachment_id`) REFERENCES `system_attachment` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_attachment_user_user` FOREIGN KEY (`user_id`) REFERENCES `system_admin` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户文件关联表';
```

#### 字段说明
| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | 自增 |
| attachment_id | bigint(20) | 附件ID | 外键关联 |
| user_id | bigint(20) | 用户ID | 外键关联 |
| tenant_id | bigint(20) | 租户ID | 数据隔离 |
| cate_id | int(11) | 用户分类ID | 用户自定义分类 |
| display_name | varchar(255) | 显示名称 | 用户看到的文件名 |
| original_name | varchar(255) | 原始文件名 | 用户上传时的文件名 |
| upload_ip | varchar(45) | 上传IP | 审计用途 |
| upload_source | varchar(50) | 上传来源 | web/api/mobile |
| creator_id | bigint(20) | 创建者ID | 操作人员 |

#### 索引设计
- **主键索引**：`PRIMARY KEY (id)`
- **唯一索引**：`uk_attachment_user_tenant` - 防止用户重复关联同一文件
- **查询索引**：
  - `idx_user_tenant (user_id, tenant_id)` - 用户文件查询
  - `idx_attachment_id (attachment_id)` - 附件关联查询
  - `idx_cate_user (cate_id, user_id, tenant_id)` - 分类查询
  - `idx_tenant_id (tenant_id)` - 租户查询
  - `idx_creator_id (creator_id)` - 创建者查询

#### 外键约束
- `fk_attachment_user_attachment`：关联到 `system_attachment.id`
- `fk_attachment_user_user`：关联到 `system_admin.id`

---

## 🔍 查询优化设计

### 常用查询场景

#### 1. 用户文件列表查询
```sql
-- 查询用户的文件列表（最常用）
SELECT 
    au.id,
    au.display_name,
    au.cate_id,
    au.created_at,
    a.size,
    a.extension,
    a.mime_type
FROM system_attachment_user au
JOIN system_attachment a ON au.attachment_id = a.id
WHERE au.user_id = ? 
  AND au.tenant_id = ?
  AND au.deleted_at IS NULL
  AND a.deleted_at IS NULL
ORDER BY au.created_at DESC
LIMIT 20;

-- 使用索引：idx_user_tenant + JOIN优化
```

#### 2. 文件去重查询
```sql
-- 通过MD5查找现有文件
SELECT id, ref_count 
FROM system_attachment 
WHERE storage = ? 
  AND file_md5 = ? 
  AND deleted_at IS NULL;

-- 使用索引：uk_storage_identifier
```

#### 3. 引用计数查询
```sql
-- 检查文件引用数量
SELECT COUNT(*) 
FROM system_attachment_user 
WHERE attachment_id = ? 
  AND deleted_at IS NULL;

-- 使用索引：idx_attachment_id
```

### 性能优化策略

#### 1. 分页查询优化
```sql
-- 使用覆盖索引避免回表
SELECT au.id, au.display_name, au.created_at
FROM system_attachment_user au
WHERE au.user_id = ? AND au.tenant_id = ?
ORDER BY au.created_at DESC
LIMIT 20 OFFSET 0;

-- 然后根据ID批量查询详细信息
SELECT a.size, a.extension, a.mime_type
FROM system_attachment a
WHERE a.id IN (?, ?, ...);
```

#### 2. 统计查询优化
```sql
-- 用户文件统计
SELECT 
    COUNT(*) as total_files,
    SUM(a.size) as total_size
FROM system_attachment_user au
JOIN system_attachment a ON au.attachment_id = a.id
WHERE au.user_id = ? 
  AND au.tenant_id = ?
  AND au.deleted_at IS NULL
  AND a.deleted_at IS NULL;
```

---

## 🔧 数据维护设计

### 引用计数维护

#### 自动维护触发器
```sql
-- 插入用户关联时增加引用计数
DELIMITER $$
CREATE TRIGGER tr_attachment_user_insert
AFTER INSERT ON system_attachment_user
FOR EACH ROW
BEGIN
    UPDATE system_attachment 
    SET ref_count = ref_count + 1 
    WHERE id = NEW.attachment_id;
END$$

-- 删除用户关联时减少引用计数
CREATE TRIGGER tr_attachment_user_delete
AFTER UPDATE ON system_attachment_user
FOR EACH ROW
BEGIN
    IF NEW.deleted_at IS NOT NULL AND OLD.deleted_at IS NULL THEN
        UPDATE system_attachment 
        SET ref_count = ref_count - 1 
        WHERE id = NEW.attachment_id;
    END IF;
END$$
DELIMITER ;
```

### 数据清理策略

#### 孤儿文件清理
```sql
-- 查找引用计数为0的文件
SELECT id, path, storage 
FROM system_attachment 
WHERE ref_count = 0 
  AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 清理孤儿文件记录
DELETE FROM system_attachment 
WHERE ref_count = 0 
  AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
```

#### 软删除数据清理
```sql
-- 清理30天前的软删除记录
DELETE FROM system_attachment_user 
WHERE deleted_at IS NOT NULL 
  AND deleted_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

DELETE FROM system_attachment 
WHERE deleted_at IS NOT NULL 
  AND deleted_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

---

## 📈 监控指标设计

### 性能监控表
```sql
-- 附件使用统计表
CREATE TABLE `system_attachment_stats` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `date` date NOT NULL COMMENT '统计日期',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `total_files` int(11) NOT NULL DEFAULT 0 COMMENT '总文件数',
    `total_size` bigint(20) NOT NULL DEFAULT 0 COMMENT '总大小',
    `unique_files` int(11) NOT NULL DEFAULT 0 COMMENT '去重后文件数',
    `unique_size` bigint(20) NOT NULL DEFAULT 0 COMMENT '去重后大小',
    `dedup_ratio` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '去重率',
    `upload_count` int(11) NOT NULL DEFAULT 0 COMMENT '当日上传数',
    `delete_count` int(11) NOT NULL DEFAULT 0 COMMENT '当日删除数',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_date_tenant` (`date`, `tenant_id`),
    KEY `idx_date` (`date`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB COMMENT='附件使用统计表';
```

### 监控查询
```sql
-- 去重效果统计
SELECT 
    COUNT(DISTINCT au.attachment_id) as unique_files,
    COUNT(*) as total_references,
    ROUND((1 - COUNT(DISTINCT au.attachment_id) / COUNT(*)) * 100, 2) as dedup_ratio
FROM system_attachment_user au
WHERE au.deleted_at IS NULL;

-- 存储空间统计
SELECT 
    storage,
    COUNT(*) as file_count,
    SUM(size) as total_size,
    AVG(size) as avg_size
FROM system_attachment 
WHERE deleted_at IS NULL
GROUP BY storage;
```

---

## ⚠️ 注意事项

### 数据一致性
1. **事务控制**：所有涉及多表操作的业务必须使用事务
2. **外键约束**：确保数据引用完整性
3. **触发器维护**：自动维护引用计数的准确性

### 性能考虑
1. **索引维护**：定期分析索引使用情况，优化索引策略
2. **分区策略**：考虑按时间分区，提高查询性能
3. **归档策略**：定期归档历史数据，控制表大小

### 扩展性
1. **预留字段**：storage_meta等JSON字段支持扩展
2. **版本兼容**：新增字段使用DEFAULT值，保证兼容性
3. **分库分表**：预留分库分表的可能性

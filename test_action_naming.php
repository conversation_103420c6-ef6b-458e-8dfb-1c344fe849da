<?php
/**
 * 测试Action命名规范
 */

require_once 'vendor/autoload.php';

echo "=== 测试Action命名规范 ===\n\n";

// 模拟权限解析服务
class TestPermissionService
{
    /**
     * 驼峰转下划线
     */
    private function camelToSnake($input)
    {
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
    }
    
    /**
     * 生成权限名称（新规范）
     */
    private function generatePermissionName(string $module, array $parts, string $controllerName, string $method): string
    {
        // 统一的权限解析逻辑，不再区分模块
        $snakeName = $this->camelToSnake($controllerName);
        
        // 将方法名也转换为下划线格式，保持命名风格统一
        $methodSnakeName = $this->camelToSnake($method);
        
        // 处理子目录控制器
        if (count($parts) > 4) {
            $subPath = strtolower($parts[3]);
            return $subPath . '_' . $snakeName . ':' . $methodSnakeName;
        }
        
        // 基础控制器直接使用控制器名
        return $snakeName . ':' . $methodSnakeName;
    }
    
    /**
     * 解析权限
     */
    public function parsePermission(string $ruleName): string
    {
        [$classPath, $method] = explode('@', $ruleName);
        $parts = explode('\\', $classPath);
        
        $module = strtolower($parts[1]);
        $controllerClass = $parts[count($parts) - 1];
        $controllerName = str_replace('Controller', '', $controllerClass);
        
        $permissionPath = $this->generatePermissionName($module, $parts, $controllerName, $method);
        
        return strtolower("{$module}:{$permissionPath}");
    }
}

// 测试用例
$testCases = [
    // 基础控制器
    'app\\crm\\controller\\CrmCustomerMyController@index' => 'crm:crm_customer_my:index',
    'app\\crm\\controller\\CrmCustomerMyController@add' => 'crm:crm_customer_my:add',
    'app\\crm\\controller\\CrmCustomerMyController@addUser' => 'crm:crm_customer_my:add_user',
    'app\\crm\\controller\\CrmCustomerMyController@getUserList' => 'crm:crm_customer_my:get_user_list',
    'app\\crm\\controller\\CrmCustomerMyController@deleteUserData' => 'crm:crm_customer_my:delete_user_data',
    
    // 子目录控制器
    'app\\system\\controller\\permission\\AdminController@index' => 'system:permission_admin:index',
    'app\\system\\controller\\permission\\AdminController@addUser' => 'system:permission_admin:add_user',
    'app\\system\\controller\\permission\\AdminController@getUserInfo' => 'system:permission_admin:get_user_info',
    
    // 其他模块
    'app\\project\\controller\\ProjectTaskController@index' => 'project:project_task:index',
    'app\\project\\controller\\ProjectTaskController@updateTaskStatus' => 'project:project_task:update_task_status',
    'app\\workflow\\controller\\ApplicationController@submitApplication' => 'workflow:application:submit_application',
];

$service = new TestPermissionService();

echo "测试Action命名规范转换:\n\n";

$passedTests = 0;
$totalTests = count($testCases);

foreach ($testCases as $input => $expected) {
    $result = $service->parsePermission($input);
    $status = ($result === $expected) ? '✅' : '❌';
    
    echo "{$status} {$input}\n";
    echo "   解析结果: {$result}\n";
    echo "   期望结果: {$expected}\n";
    
    if ($result === $expected) {
        $passedTests++;
    } else {
        echo "   ⚠️ 不匹配!\n";
    }
    echo "\n";
}

echo "测试结果: {$passedTests}/{$totalTests} 通过\n";

if ($passedTests === $totalTests) {
    echo "✅ 所有测试通过！Action命名规范正确实施。\n";
} else {
    echo "❌ 部分测试失败，需要检查权限解析逻辑。\n";
}

echo "\n=== 新的Action命名规范说明 ===\n";
echo "1. 所有action都转换为下划线格式\n";
echo "2. addUser -> add_user\n";
echo "3. getUserList -> get_user_list\n";
echo "4. deleteUserData -> delete_user_data\n";
echo "5. 保持与controller命名风格一致\n";

echo "\n=== 测试完成 ===\n";

<?php
declare(strict_types=1);

namespace app\hr\controller;

use app\common\core\base\BaseController;
use app\hr\service\HrMonthlyStatsService;
use app\hr\service\HrWorkTimeService;
use think\response\Json;

/**
 * HR月度统计控制器
 * 提供请假、外出、出差的月度统计API接口
 */
class HrMonthlyStatsController extends BaseController
{
    protected $service;

    public function initialize()
    {
        parent::initialize();
        $this->service = new HrMonthlyStatsService();
    }
    
    /**
     * 获取员工月度统计
     * 
     * @return Json
     */
    public function getEmployeeStats(): Json
    {
        $adminId = $this->request->param('admin_id', 0, 'int');
        $yearMonth = $this->request->param('year_month', date('Y-m'), 'string');
        
        // 如果未指定员工ID，使用当前登录用户
        if (!$adminId) {
            $adminId = get_user_id();
        }
        
        try {
            $stats = $this->service->getEmployeeMonthlyStats($adminId, $yearMonth);
            return $this->success('获取成功', $stats);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取部门月度统计
     * 
     * @return Json
     */
    public function getDepartmentStats(): Json
    {
        $deptId = $this->request->param('dept_id', 0, 'int');
        $yearMonth = $this->request->param('year_month', date('Y-m'), 'string');
        
        if (!$deptId) {
            return $this->error('部门ID不能为空');
        }
        
        try {
            $stats = $this->service->getDepartmentMonthlyStats($deptId, $yearMonth);
            return $this->success('获取成功', $stats);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取员工年度统计
     * 
     * @return Json
     */
    public function getEmployeeYearlyStats(): Json
    {
        $adminId = $this->request->param('admin_id', 0, 'int');
        $year = $this->request->param('year', (int)date('Y'), 'int');
        
        // 如果未指定员工ID，使用当前登录用户
        if (!$adminId) {
            $adminId = get_user_id();
        }
        
        try {
            $stats = $this->service->getEmployeeYearlyStats($adminId, $year);
            return $this->success('获取成功', $stats);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取统计数据概览
     * 
     * @return Json
     */
    public function getStatsOverview(): Json
    {
        $adminId = $this->request->param('admin_id', 0, 'int');
        $yearMonth = $this->request->param('year_month', date('Y-m'), 'string');
        
        try {
            $stats = $this->service->getStatsOverview($adminId, $yearMonth);
            return $this->success('获取成功', $stats);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 导出员工月度统计
     * 
     * @return Json
     */
    public function exportEmployeeStats(): Json
    {
        $adminId = $this->request->param('admin_id', 0, 'int');
        $yearMonth = $this->request->param('year_month', date('Y-m'), 'string');
        
        // 如果未指定员工ID，使用当前登录用户
        if (!$adminId) {
            $adminId = get_user_id();
        }
        
        try {
            $stats = $this->service->getEmployeeMonthlyStats($adminId, $yearMonth);
            
            // 构建导出数据
            $exportData = [
                'title' => "员工月度统计报表 - {$yearMonth}",
                'data' => $stats,
                'summary' => [
                    '统计月份' => $yearMonth,
                    '员工ID' => $adminId,
                    '请假时长' => $stats['leave_stats']['total_hours'] . '小时',
                    '外出时长' => $stats['outing_hours'] . '小时',
                    '出差时长' => $stats['business_trip_hours'] . '小时',
                    '总计时长' => $stats['display_format'],
                    '每日工作时间' => $stats['daily_work_hours'] . '小时'
                ]
            ];
            
            return $this->success('导出数据准备完成', $exportData);
        } catch (\Exception $e) {
            return $this->error('导出失败：' . $e->getMessage());
        }
    }
    
    /**
     * 导出部门月度统计
     * 
     * @return Json
     */
    public function exportDepartmentStats(): Json
    {
        $deptId = $this->request->param('dept_id', 0, 'int');
        $yearMonth = $this->request->param('year_month', date('Y-m'), 'string');
        
        if (!$deptId) {
            return $this->error('部门ID不能为空');
        }
        
        try {
            $stats = $this->service->getDepartmentMonthlyStats($deptId, $yearMonth);
            
            // 构建导出数据
            $exportData = [
                'title' => "部门月度统计报表 - {$yearMonth}",
                'data' => $stats,
                'summary' => [
                    '统计月份' => $yearMonth,
                    '部门ID' => $deptId,
                    '员工人数' => $stats['employee_count'] . '人',
                    '部门总时长' => $stats['display_format'],
                    '每日工作时间' => $stats['employees'][0]['daily_work_hours'] ?? 8 . '小时'
                ]
            ];
            
            return $this->success('导出数据准备完成', $exportData);
        } catch (\Exception $e) {
            return $this->error('导出失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取所有员工月度统计
     *
     * @return Json
     */
    public function getAllEmployeeStats(): Json
    {
        $yearMonth = $this->request->param('year_month', date('Y-m'), 'string');
        $page = $this->request->param('page', 1, 'int');
        $limit = $this->request->param('limit', 10, 'int');

        try {
            $result = $this->service->getAllEmployeeMonthlyStats($yearMonth, $page, $limit);
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取统计配置信息
     *
     * @return Json
     */
    public function getStatsConfig(): Json
    {
        try {
            $workTimeService = new HrWorkTimeService();
            $config = $workTimeService->getWorkTimeConfig();

            $statsConfig = [
                'work_time_config' => $config,
                'leave_types' => [
                    1 => '年假',
                    2 => '事假',
                    3 => '病假',
                    4 => '婚假',
                    5 => '产假',
                    6 => '丧假',
                    7 => '其他'
                ],
                'approval_status' => [
                    0 => '草稿',
                    1 => '审批中',
                    2 => '已通过',
                    3 => '已拒绝',
                    4 => '已撤回'
                ],
                'calculation_rules' => [
                    'min_unit' => '0.5小时',
                    'rule_description' => '不足0.5小时按0.5小时计算，大于0.5小时小于1小时按1小时计算',
                    'display_format' => '超过每日工作时间时显示为"X天Y小时"格式'
                ]
            ];

            return $this->success('获取成功', $statsConfig);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
}

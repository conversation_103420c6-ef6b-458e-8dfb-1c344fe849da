<?php
/**
 * 权限命名规范化生成器
 * 生成权限映射关系和批量更新SQL
 */

require_once 'vendor/autoload.php';

echo "=== 权限命名规范化生成器 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取当前权限数据:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type, status, sort
        FROM system_menu
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $allPermissions = $stmt->fetchAll();
    
    echo "  总权限数: " . count($allPermissions) . " 个\n\n";
    
    // 权限映射数组
    $permissionMappings = [];
    $updateSqls = [];
    $insertSqls = [];
    $deleteSqls = [];
    
    echo "2. 生成权限映射关系:\n";
    
    foreach ($allPermissions as $perm) {
        $oldName = $perm['name'];
        $newName = generateStandardPermissionName($oldName);
        
        if ($oldName !== $newName) {
            $permissionMappings[] = [
                'id' => $perm['id'],
                'old_name' => $oldName,
                'new_name' => $newName,
                'title' => $perm['title'],
                'type' => $perm['type'],
                'change_type' => 'rename'
            ];
            
            $updateSqls[] = "UPDATE system_menu SET name = '{$newName}' WHERE id = {$perm['id']}; -- {$oldName} -> {$newName}";
        }
    }
    
    echo "  需要更新的权限: " . count($permissionMappings) . " 个\n";
    
    // 显示前10个映射示例
    echo "  映射示例:\n";
    for ($i = 0; $i < min(10, count($permissionMappings)); $i++) {
        $mapping = $permissionMappings[$i];
        echo "    {$mapping['old_name']} -> {$mapping['new_name']}\n";
    }
    
    echo "\n3. 检查缺失的权限:\n";
    
    // 检查实际控制器文件
    $actualControllers = getActualControllers();
    $existingPermissions = array_column($allPermissions, 'name');
    
    $missingPermissions = [];
    foreach ($actualControllers as $module => $controllers) {
        foreach ($controllers as $controller) {
            $expectedPermission = "{$module}:{$controller}:index";
            if (!in_array($expectedPermission, $existingPermissions)) {
                $missingPermissions[] = [
                    'name' => $expectedPermission,
                    'title' => generateTitleFromController($controller),
                    'module' => $module,
                    'controller' => $controller
                ];
            }
        }
    }
    
    echo "  缺失的权限: " . count($missingPermissions) . " 个\n";
    foreach ($missingPermissions as $missing) {
        echo "    + {$missing['name']} ({$missing['title']})\n";
        
        $insertSqls[] = "INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('{$missing['name']}', '{$missing['title']}', 1, 1, 100, NOW(), NOW());";
    }
    
    echo "\n4. 检查冗余的权限:\n";
    
    // 检查可能的冗余权限
    $redundantPermissions = [];
    foreach ($allPermissions as $perm) {
        $name = $perm['name'];
        
        // 检查是否是明显的冗余权限
        if (isRedundantPermission($name)) {
            $redundantPermissions[] = $perm;
            $deleteSqls[] = "DELETE FROM system_menu WHERE id = {$perm['id']}; -- 删除冗余权限: {$name}";
        }
    }
    
    echo "  冗余的权限: " . count($redundantPermissions) . " 个\n";
    foreach ($redundantPermissions as $redundant) {
        echo "    - {$redundant['name']} ({$redundant['title']})\n";
    }
    
    echo "\n5. 生成SQL脚本:\n";
    
    // 生成完整的SQL脚本
    $sqlScript = "-- 权限命名规范化SQL脚本\n";
    $sqlScript .= "-- 生成时间: " . date('Y-m-d H:i:s') . "\n";
    $sqlScript .= "-- 总更新数: " . count($updateSqls) . " 个\n";
    $sqlScript .= "-- 总新增数: " . count($insertSqls) . " 个\n";
    $sqlScript .= "-- 总删除数: " . count($deleteSqls) . " 个\n\n";
    
    $sqlScript .= "-- 开始事务\n";
    $sqlScript .= "START TRANSACTION;\n\n";
    
    if (!empty($updateSqls)) {
        $sqlScript .= "-- 1. 更新现有权限名称\n";
        foreach ($updateSqls as $sql) {
            $sqlScript .= $sql . "\n";
        }
        $sqlScript .= "\n";
    }
    
    if (!empty($insertSqls)) {
        $sqlScript .= "-- 2. 添加缺失权限\n";
        foreach ($insertSqls as $sql) {
            $sqlScript .= $sql . "\n";
        }
        $sqlScript .= "\n";
    }
    
    if (!empty($deleteSqls)) {
        $sqlScript .= "-- 3. 删除冗余权限\n";
        foreach ($deleteSqls as $sql) {
            $sqlScript .= $sql . "\n";
        }
        $sqlScript .= "\n";
    }
    
    $sqlScript .= "-- 提交事务\n";
    $sqlScript .= "COMMIT;\n\n";
    $sqlScript .= "-- 如有问题，可以回滚：ROLLBACK;\n";
    
    // 保存SQL脚本
    file_put_contents('permission_normalization.sql', $sqlScript);
    echo "  ✅ SQL脚本已生成: permission_normalization.sql\n";
    
    // 生成权限映射JSON
    $mappingData = [
        'generated_at' => date('Y-m-d H:i:s'),
        'total_permissions' => count($allPermissions),
        'updates' => count($permissionMappings),
        'inserts' => count($missingPermissions),
        'deletes' => count($redundantPermissions),
        'mappings' => $permissionMappings,
        'missing' => $missingPermissions,
        'redundant' => $redundantPermissions
    ];
    
    file_put_contents('permission_mapping.json', json_encode($mappingData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "  ✅ 权限映射已生成: permission_mapping.json\n";
    
    echo "\n6. 执行建议:\n";
    echo "  1. 请先检查生成的SQL脚本: permission_normalization.sql\n";
    echo "  2. 在测试环境执行SQL脚本进行验证\n";
    echo "  3. 确认无误后在生产环境执行\n";
    echo "  4. 执行后运行权限验证脚本\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

/**
 * 生成标准权限名称
 */
function generateStandardPermissionName($oldName) {
    $parts = explode(':', $oldName);
    
    // 单词权限，需要归类到模块
    if (count($parts) == 1) {
        $word = $parts[0];
        $module = guessModuleFromWord($word);
        return "{$module}:{$module}_{$word}:index";
    }
    
    // 二段式权限，补充action
    if (count($parts) == 2) {
        $module = $parts[0];
        $controller = $parts[1];
        
        // 确保控制器名包含模块前缀
        if (!str_starts_with($controller, $module . '_')) {
            $controller = $module . '_' . $controller;
        }
        
        return "{$module}:{$controller}:index";
    }
    
    // 三段式权限
    if (count($parts) == 3) {
        $module = $parts[0];
        $controller = $parts[1];
        $action = $parts[2];
        
        // 如果控制器名不包含下划线，添加模块前缀
        if (strpos($controller, '_') === false) {
            $controller = $module . '_' . $controller;
        }
        
        return "{$module}:{$controller}:{$action}";
    }
    
    // 四段式及以上保持不变（已经是正确格式）
    return $oldName;
}

/**
 * 从单词猜测模块
 */
function guessModuleFromWord($word) {
    $moduleMap = [
        'article' => 'system',
        'log' => 'system',
        'message' => 'notice',
        'notice' => 'notice',
        'office' => 'office',
        'user' => 'system',
        'workflow' => 'workflow'
    ];
    
    return $moduleMap[$word] ?? 'system';
}

/**
 * 获取实际控制器文件
 */
function getActualControllers() {
    $controllers = [];
    $modules = ['crm', 'system', 'project'];
    
    foreach ($modules as $module) {
        $controllers[$module] = [];
        $dir = "app/{$module}/controller";
        
        if (is_dir($dir)) {
            // 主目录控制器
            $files = glob($dir . '/*Controller.php');
            foreach ($files as $file) {
                $filename = basename($file, '.php');
                $controllerName = str_replace('Controller', '', $filename);
                $snakeName = camelToSnake($controllerName);
                
                // 确保包含模块前缀
                if (!str_starts_with($snakeName, $module . '_')) {
                    $snakeName = $module . '_' . $snakeName;
                }
                
                $controllers[$module][] = $snakeName;
            }
            
            // 子目录控制器
            $subdirs = glob($dir . '/*', GLOB_ONLYDIR);
            foreach ($subdirs as $subdir) {
                $subfiles = glob($subdir . '/*Controller.php');
                foreach ($subfiles as $file) {
                    $filename = basename($file, '.php');
                    $controllerName = str_replace('Controller', '', $filename);
                    $snakeName = camelToSnake($controllerName);
                    $subdirName = basename($subdir);
                    
                    $controllers[$module][] = $subdirName . '_' . $snakeName;
                }
            }
        }
    }
    
    return $controllers;
}

/**
 * 驼峰转下划线
 */
function camelToSnake($input) {
    return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
}

/**
 * 从控制器名生成标题
 */
function generateTitleFromController($controller) {
    $parts = explode('_', $controller);
    $title = '';
    
    foreach ($parts as $part) {
        switch ($part) {
            case 'crm':
                $title .= 'CRM';
                break;
            case 'customer':
                $title .= '客户';
                break;
            case 'business':
                $title .= '商机';
                break;
            case 'contract':
                $title .= '合同';
                break;
            case 'product':
                $title .= '产品';
                break;
            case 'share':
                $title .= '共享';
                break;
            case 'log':
                $title .= '日志';
                break;
            case 'statistics':
                $title .= '统计';
                break;
            case 'report':
                $title .= '报告';
                break;
            default:
                $title .= ucfirst($part);
        }
    }
    
    return $title . '管理';
}

/**
 * 检查是否是冗余权限
 */
function isRedundantPermission($name) {
    // 明显的冗余权限模式
    $redundantPatterns = [
        '/^crm:index$/',
        '/^crm:product$/',
        '/^crm:work_report\/index$/',
        '/^crm:customer_sea$/',
        '/^crm:follow$/',
        '/^crm:follow_record$/'
    ];
    
    foreach ($redundantPatterns as $pattern) {
        if (preg_match($pattern, $name)) {
            return true;
        }
    }
    
    return false;
}

echo "\n=== 权限映射生成完成 ===\n";

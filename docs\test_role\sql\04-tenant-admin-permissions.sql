-- =====================================================
-- 权限测试 - tenant_admin租户超级管理员权限配置
-- 创建日期：2025-01-31
-- 说明：为tenant_admin用户配置完整的租户超级管理员权限
-- =====================================================

SELECT '=== 开始配置tenant_admin租户超级管理员权限 ===' as message;

-- =====================================================
-- 1. 确认租户超级管理员角色
-- =====================================================

SELECT '1. 确认租户超级管理员角色' as step;

-- 检查租户超级管理员角色是否存在
SELECT 
    CASE 
        WHEN EXISTS(SELECT 1 FROM system_role WHERE id = 101 AND name = '租户超级管理员') 
        THEN '✅ 租户超级管理员角色已存在'
        ELSE '❌ 租户超级管理员角色不存在，需要创建'
    END as role_status;

-- 如果不存在则创建租户超级管理员角色
INSERT IGNORE INTO `system_role` (
    `id`, `name`, `remark`, `status`, `tenant_id`, `created_at`, `updated_at`
) VALUES (
    101, '租户超级管理员', '租户内全部数据权限', 1, 1, NOW(), NOW()
);

-- =====================================================
-- 2. 确认tenant_admin用户
-- =====================================================

SELECT '2. 确认tenant_admin用户' as step;

-- 检查tenant_admin用户是否存在
SELECT 
    CASE 
        WHEN EXISTS(SELECT 1 FROM system_admin WHERE id = 201 AND username = 'tenant_admin') 
        THEN '✅ tenant_admin用户已存在'
        ELSE '❌ tenant_admin用户不存在，需要创建'
    END as user_status;

-- 如果不存在则创建tenant_admin用户
INSERT IGNORE INTO `system_admin` (
    `id`, `username`, `password`, `real_name`, `email`, `phone`, 
    `status`, `tenant_id`, `created_at`, `updated_at`
) VALUES (
    201, 'tenant_admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    '租户管理员', '<EMAIL>', '13800000201', 
    1, 1, NOW(), NOW()
);

-- =====================================================
-- 3. 为租户超级管理员分配所有权限
-- =====================================================

SELECT '3. 为租户超级管理员分配所有权限' as step;

-- 清除现有权限分配
DELETE FROM system_role_menu WHERE role_id = 101;

-- 为租户超级管理员分配所有可用权限
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`)
SELECT 101, id, 1
FROM system_menu 
WHERE status = 1 AND deleted_at IS NULL;

-- 统计分配的权限数量
SELECT 
    COUNT(*) as total_permissions,
    '个权限已分配给租户超级管理员角色' as message
FROM system_role_menu 
WHERE role_id = 101;

-- =====================================================
-- 4. 为tenant_admin用户分配角色
-- =====================================================

SELECT '4. 为tenant_admin用户分配角色' as step;

-- 清除现有角色分配
DELETE FROM system_admin_role WHERE admin_id = 201;

-- 为tenant_admin用户分配租户超级管理员角色
INSERT INTO `system_admin_role` (`admin_id`, `role_id`, `tenant_id`) 
VALUES (201, 101, 1);

-- =====================================================
-- 5. 验证权限分配结果
-- =====================================================

SELECT '5. 验证权限分配结果' as step;

-- 验证用户权限总数
SELECT 
    COUNT(*) as user_permission_count,
    '个权限已分配给tenant_admin用户' as message
FROM system_admin_role ar
LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
LEFT JOIN system_menu m ON rm.menu_id = m.id
WHERE ar.admin_id = 201 AND m.status = 1 AND m.deleted_at IS NULL;

-- 验证权限模块分布
SELECT 
    SUBSTRING_INDEX(m.name, ':', 1) as module,
    COUNT(*) as permission_count,
    SUM(CASE WHEN m.type = 1 THEN 1 ELSE 0 END) as menu_count,
    SUM(CASE WHEN m.type = 2 THEN 1 ELSE 0 END) as button_count
FROM system_admin_role ar
LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
LEFT JOIN system_menu m ON rm.menu_id = m.id
WHERE ar.admin_id = 201 AND m.status = 1 AND m.deleted_at IS NULL 
AND m.name LIKE '%:%'
GROUP BY SUBSTRING_INDEX(m.name, ':', 1)
ORDER BY permission_count DESC;

-- =====================================================
-- 6. 验证关键权限
-- =====================================================

SELECT '6. 验证关键权限' as step;

-- 验证tenant_admin是否拥有关键权限
SELECT 
    'crm:crm_customer_my:index' as permission_name,
    CASE 
        WHEN EXISTS(
            SELECT 1 FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = 201 AND m.name = 'crm:crm_customer_my:index'
        ) THEN '✅ 拥有'
        ELSE '❌ 缺少'
    END as has_permission
UNION ALL
SELECT 
    'system:permission:admin:index' as permission_name,
    CASE 
        WHEN EXISTS(
            SELECT 1 FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = 201 AND m.name = 'system:permission:admin:index'
        ) THEN '✅ 拥有'
        ELSE '❌ 缺少'
    END as has_permission
UNION ALL
SELECT 
    'system:permission:role:index' as permission_name,
    CASE 
        WHEN EXISTS(
            SELECT 1 FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = 201 AND m.name = 'system:permission:role:index'
        ) THEN '✅ 拥有'
        ELSE '❌ 缺少'
    END as has_permission
UNION ALL
SELECT 
    'system:permission:menu:index' as permission_name,
    CASE 
        WHEN EXISTS(
            SELECT 1 FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = 201 AND m.name = 'system:permission:menu:index'
        ) THEN '✅ 拥有'
        ELSE '❌ 缺少'
    END as has_permission
UNION ALL
SELECT 
    'project:project:index' as permission_name,
    CASE 
        WHEN EXISTS(
            SELECT 1 FROM system_admin_role ar
            LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE ar.admin_id = 201 AND m.name = 'project:project:index'
        ) THEN '✅ 拥有'
        ELSE '❌ 缺少'
    END as has_permission;

-- =====================================================
-- 7. 权限覆盖率验证
-- =====================================================

SELECT '7. 权限覆盖率验证' as step;

-- 计算权限覆盖率
SELECT 
    (SELECT COUNT(*) FROM system_admin_role ar
     LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
     LEFT JOIN system_menu m ON rm.menu_id = m.id
     WHERE ar.admin_id = 201 AND m.status = 1 AND m.deleted_at IS NULL) as tenant_admin_permissions,
    (SELECT COUNT(*) FROM system_menu WHERE status = 1 AND deleted_at IS NULL) as total_system_permissions,
    ROUND(
        (SELECT COUNT(*) FROM system_admin_role ar
         LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
         LEFT JOIN system_menu m ON rm.menu_id = m.id
         WHERE ar.admin_id = 201 AND m.status = 1 AND m.deleted_at IS NULL) * 100.0 /
        (SELECT COUNT(*) FROM system_menu WHERE status = 1 AND deleted_at IS NULL), 1
    ) as coverage_percentage;

-- =====================================================
-- 8. 登录信息说明
-- =====================================================

SELECT '8. tenant_admin登录信息' as step;

SELECT 
    'tenant_admin' as username,
    'password' as default_password,
    '租户超级管理员' as role_name,
    '拥有租户内所有功能的完整权限' as description,
    '建议首次登录后立即修改密码' as security_note;

-- =====================================================
-- 完成信息
-- =====================================================

SELECT '=== tenant_admin租户超级管理员权限配置完成 ===' as message;

SELECT 
    '✅ 租户超级管理员角色已配置' as status_1,
    '✅ tenant_admin用户已创建' as status_2,
    '✅ 全部权限已分配' as status_3,
    '✅ 权限验证通过' as status_4,
    '✅ 可以开始使用' as status_5;

#!/usr/bin/env python3
"""
检查数据库表结构
"""
import os
import mysql.connector

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

def get_db_connection():
    return mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )

def main():
    print("=== 检查数据库表结构 ===\n")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 检查关键表的结构
    tables = ['crm_customer', 'crm_lead', 'project_project', 'daily_price_order']
    
    for table in tables:
        print(f"📋 表: {table}")
        try:
            cursor.execute(f"DESCRIBE {table}")
            columns = cursor.fetchall()
            
            print(f"  字段列表:")
            for col in columns:
                field_name, field_type, null, key, default, extra = col
                print(f"    {field_name}: {field_type}")
            
            # 检查是否有数据
            cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE tenant_id = 1")
            count = cursor.fetchone()[0]
            print(f"  数据量: {count} 条")
            
        except Exception as e:
            print(f"  ❌ 错误: {e}")
        
        print()
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    main()

<?php

namespace app\system\controller\permission;

use app\common\core\base\BaseAdminController;
use app\system\service\DeptService;
use think\response\Json;

/**
 * 部门控制器
 */
class DepartmentController extends BaseAdminController
{
	
	/**
	 * @var DeptService
	 */
	private DeptService $service;
	
	public function initialize(): void
	{
		parent::initialize();
		
		$this->service = DeptService::getInstance();
	}
	
	/**
	 * 获取部门列表
	 */
	public function index(): J<PERSON>
	{
		return $this->success('获取成功', $this->service->getList(input()));
	}
	
	/**
	 * 获取部门详情
	 */
	public function detail($id): Json
	{
		return $this->success('创建成功', $this->service->getDetail((int)$id));
	}
	
	/**
	 * 创建部门
	 */
	public function add(): Json
	{
		return $this->service->create(input(), $this->adminId, $this->tenantId)
			? $this->success('创建成功')
			: $this->error('创建失败');
	}
	
	/**
	 * 更新部门
	 */
	public function edit($id): J<PERSON>
	{
		return $this->service->update((int)$id, input())
			? $this->success('操作成功')
			: $this->error('操作失败');
	}
	
	/**
	 * 删除部门
	 */
	public function delete(): Json
	{
		return $this->service->delete(input('id/d'))
			? $this->success('删除成功')
			: $this->error('删除失败');
	}
	
	/**
	 * 用于下拉选择
	 */
	public function options(): Json
	{
		return $this->success('获取成功', $this->service->getOptions($this->adminId, $this->tenantId));
	}
	
}

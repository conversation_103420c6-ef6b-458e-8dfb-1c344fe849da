import request from '@/utils/http'
import type { UploadConfig, UploadToken } from '@/utils/upload/IUploader'
import axios, { AxiosRequestConfig } from 'axios'
import { useUserStore } from '@/store/modules/user'

/**
 * API响应接口
 */
interface ApiResponse<T = any> {
  code: number
  data?: T
  message?: string
}

/**
 * 上传相关API - 重构版本
 *
 * 上传流程说明：
 * 1. 本地上传：文件先上传到后端服务器，然后由后端处理，支持文件去重
 * 2. 云存储上传：前端直接上传到云存储，不经过后端服务器
 *    - 前端从后端获取上传Token和配置
 *    - 前端直接将文件上传到云存储
 *    - 云存储通过回调通知后端上传完成，支持文件去重
 */
export class UploadApi {
  /**
   * 获取上传配置
   */
  static async getConfig() {
    return request.get<UploadConfig>({
      url: 'system/getUploadConfig'
    })
  }

  /**
   * 获取上传Token（使用新的AttachmentController）
   * @param storage 存储类型
   * @param params 额外参数
   * @description 仅云存储上传时使用，用于获取直传所需的Token
   */
  static async getToken(storage: string, params?: Record<string, any>) {
    return request.get<UploadToken>({
      url: 'system/getUploadToken',
      params: {
        storage,
        ...params
      }
    })
  }

  /**
   * 上传附件到服务器（使用新的AttachmentController）
   * @description 支持本地上传和云存储上传，自动处理文件去重
   * @param formData 包含文件的表单数据
   * @param config 请求配置
   * @returns 上传结果
   */
  static async upload(formData: FormData, config?: AxiosRequestConfig): Promise<ApiResponse> {
    // 获取用户Token
    const userStore = useUserStore()

    // 合并配置
    const mergedConfig: AxiosRequestConfig = {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: userStore.accessToken,
        ...(config?.headers || {})
      }
    }

    // 发送请求到新的AttachmentController
    const response = await axios.post(
      `${import.meta.env.VITE_API_URL}/system/upload`,
      formData,
      mergedConfig
    )

    return response.data
  }

  /**
   * 处理云存储上传回调
   * @param storage 存储类型
   * @param params 回调参数
   */
  static async uploadCallback(storage: string, params: Record<string, any>) {
    return request.post<ApiResponse>({
      url: `system/uploadCallback/${storage}`,
      data: params
    })
  }
}

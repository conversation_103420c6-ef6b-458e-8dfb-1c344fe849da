<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="60%"
    top="5vh"
    :close-on-click-modal="false"
    :before-close="handleDialogClose"
    destroy-on-close
    class="hr-leave-dialog"
  >
    <div class="dialog-content" v-loading="loading">
      <!-- 主表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
        class="leave-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <User />
            </el-icon>
            基本信息
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="请假类型" prop="leave_type" required>
                <el-select
                  v-model="formData.leave_type"
                  placeholder="请选择请假类型"
                  style="width: 100%"
                  :disabled="!canEdit"
                >
                  <el-option
                    v-for="item in leaveTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="请假时长" prop="duration" required>
                <el-input-number
                  v-model="formData.duration"
                  :min="0.5"
                  :max="2920"
                  :step="0.5"
                  :precision="1"
                  placeholder="请假时长（小时）"
                  style="width: 100%"
                  :disabled="!canEdit"
                />
                <div class="field-tip">按半小时向上取整规则自动计算，也可手动调整</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 时间信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <Clock />
            </el-icon>
            时间信息
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="开始时间" prop="start_time" required>
                <el-date-picker
                  v-model="formData.start_time"
                  type="datetime"
                  placeholder="选择开始时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                  :disabled="!canEdit"
                  @change="handleTimeChange"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="结束时间" prop="end_time" required>
                <el-date-picker
                  v-model="formData.end_time"
                  type="datetime"
                  placeholder="选择结束时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                  :disabled="!canEdit"
                  @change="handleTimeChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 详细信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <Document />
            </el-icon>
            详细信息
          </div>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="请假原因" prop="reason" required>
                <el-input
                  v-model="formData.reason"
                  type="textarea"
                  :rows="4"
                  placeholder="请详细说明请假原因（最多500字符）"
                  maxlength="500"
                  show-word-limit
                  :disabled="!canEdit"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="紧急联系人" prop="emergency_contact">
                <el-input
                  v-model="formData.emergency_contact"
                  placeholder="请输入紧急联系人姓名"
                  :disabled="!canEdit"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="联系电话" prop="emergency_phone">
                <el-input
                  v-model="formData.emergency_phone"
                  placeholder="请输入紧急联系人电话"
                  :disabled="!canEdit"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 附件信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <Paperclip />
            </el-icon>
            附件信息
          </div>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="相关附件" prop="attachment">
                <FormUploader
                  v-model="formData.attachment"
                  fileType="image"
                  :limit="5"
                  :multiple="true"
                  :disabled="!canEdit"
                  returnValueMode="string"
                  buttonText="选择附件"
                  tipText="支持上传图片格式，最多5个。可按住Ctrl键选择多个文件"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注信息" prop="remark">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="其他需要说明的信息（可选）"
                  maxlength="200"
                  show-word-limit
                  :disabled="!canEdit"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 审批信息（仅查看时显示） -->
        <div class="form-section" v-if="formData.id && formData.approval_status > 0">
          <div class="section-title">
            <el-icon>
              <Checked />
            </el-icon>
            审批信息
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="提交时间">
                <el-input :value="formData.submit_time || '未提交'" readonly style="width: 100%" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="审批时间">
                <el-input
                  :value="formData.approval_time || '未审批'"
                  readonly
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="formData.approval_opinion">
            <el-col :span="24">
              <el-form-item label="审批意见">
                <el-input :value="formData.approval_opinion" type="textarea" :rows="3" readonly />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel"> 取消</el-button>

        <el-button v-if="canEdit" type="primary" @click="handleSave" :loading="saving">
          保存草稿
        </el-button>

        <el-button v-if="canEdit" type="success" @click="handleSubmit" :loading="submitting">
          提交审批
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

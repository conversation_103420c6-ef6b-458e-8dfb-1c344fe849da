<?php
/**
 * 执行权限统一SQL脚本
 */

require_once 'vendor/autoload.php';

echo "=== 执行权限统一SQL脚本 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 执行前验证:\n";
    
    // 检查当前权限数量
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE status = 1 AND deleted_at IS NULL");
    $stmt->execute();
    $beforeCount = $stmt->fetch()['count'];
    echo "  执行前权限数量: {$beforeCount} 个\n";
    
    // 检查格式分布
    $stmt = $pdo->prepare("
        SELECT 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END as format_type,
            COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END
    ");
    $stmt->execute();
    $beforeFormats = $stmt->fetchAll();
    
    echo "  执行前格式分布:\n";
    foreach ($beforeFormats as $format) {
        echo "    {$format['format_type']}: {$format['count']} 个\n";
    }
    
    echo "\n2. 读取并执行SQL脚本:\n";
    
    $sqlScript = file_get_contents('permission_unification.sql');
    if (!$sqlScript) {
        throw new Exception("无法读取SQL脚本文件");
    }
    
    // 分割SQL语句
    $statements = explode(';', $sqlScript);
    $executedCount = 0;
    $errorCount = 0;
    
    $pdo->beginTransaction();
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // 跳过注释和空语句
        if (empty($statement) || strpos($statement, '--') === 0 || 
            strpos($statement, 'START TRANSACTION') !== false ||
            strpos($statement, 'COMMIT') !== false ||
            strpos($statement, 'ROLLBACK') !== false) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executedCount++;
            
            if ($executedCount % 10 == 0) {
                echo "    已执行 {$executedCount} 条语句...\n";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "    ❌ 执行失败: " . substr($statement, 0, 50) . "...\n";
            echo "       错误: " . $e->getMessage() . "\n";
        }
    }
    
    if ($errorCount > 0) {
        echo "\n  ⚠️ 发现 {$errorCount} 个错误，回滚事务\n";
        $pdo->rollBack();
        throw new Exception("SQL执行过程中发现错误，已回滚");
    } else {
        $pdo->commit();
        echo "\n  ✅ 所有SQL语句执行成功，已提交事务\n";
        echo "  总执行语句数: {$executedCount} 条\n";
    }
    
    echo "\n3. 执行后验证:\n";
    
    // 检查执行后权限数量
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE status = 1 AND deleted_at IS NULL");
    $stmt->execute();
    $afterCount = $stmt->fetch()['count'];
    echo "  执行后权限数量: {$afterCount} 个\n";
    echo "  权限数量变化: " . ($afterCount - $beforeCount) . " 个\n";
    
    // 检查格式分布
    $stmt = $pdo->prepare("
        SELECT 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END as format_type,
            COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END
    ");
    $stmt->execute();
    $afterFormats = $stmt->fetchAll();
    
    echo "  执行后格式分布:\n";
    foreach ($afterFormats as $format) {
        echo "    {$format['format_type']}: {$format['count']} 个\n";
    }
    
    // 检查统一效果
    $threePartsCount = 0;
    $otherCount = 0;
    
    foreach ($afterFormats as $format) {
        if ($format['format_type'] === '三段式') {
            $threePartsCount = $format['count'];
        } else {
            $otherCount += $format['count'];
        }
    }
    
    $unificationRate = round($threePartsCount / $afterCount * 100, 1);
    echo "  统一率: {$unificationRate}% ({$threePartsCount}/{$afterCount})\n";
    
    if ($unificationRate >= 95) {
        echo "  ✅ 权限格式统一成功\n";
    } else {
        echo "  ⚠️ 权限格式统一不完整，需要进一步检查\n";
    }
    
    echo "\n4. 权限统一完成!\n";
    echo "  ✅ 权限数据已统一为三段式格式\n";
    echo "  ✅ 可以继续下一步：更新权限解析逻辑\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "\n如果需要回滚，请执行:\n";
    echo "ROLLBACK;\n";
}

echo "\n=== 权限统一执行完成 ===\n";

# Route目录优化方案

**版本**: v1.0  
**创建日期**: 2025-01-31  
**适用系统**: CRM管理系统  
**技术栈**: ThinkPHP 8

---

## 📊 现状分析

### 当前问题
1. **文件过多**：38个路由文件，管理复杂
2. **命名不规范**：CRM模块有22个独立文件
3. **中间件配置分散**：相同中间件配置重复定义
4. **维护困难**：修改中间件需要更新多个文件

### 文件分布统计
| 模块前缀 | 文件数量 | 占比 | 状态 |
|----------|----------|------|------|
| **crm_** | 22个 | 58% | 🔴 需要合并 |
| **project_** | 5个 | 13% | 🟡 可以合并 |
| **system** | 1个 | 3% | ✅ 已优化 |
| **其他** | 10个 | 26% | 🟡 需要整理 |

### 中间件配置分析
- **仅Token认证**: 30个文件
- **完整权限验证**: 1个文件 (System.php)
- **公开接口**: 1个文件
- **特殊配置**: 1个文件

---

## 🎯 优化方案

### 方案1：按模块合并（推荐）

#### 目标结构
```
route/
├── modules/
│   ├── crm.php          # 合并22个CRM路由文件
│   ├── project.php      # 合并5个项目路由文件
│   ├── system.php       # 保持现有System.php
│   ├── workflow.php     # 保持现有Workflow.php
│   ├── notice.php       # 保持现有Notice.php
│   ├── hr.php           # 保持现有hr.php
│   ├── daily.php        # 每日报价路由
│   └── ims.php          # 库存管理路由
├── common.php           # 公共接口
├── auth.php             # 认证接口
└── public.php           # 公开接口
```

#### 优化效果
- **文件数量**：从38个减少到11个 (减少71%)
- **维护性**：按模块组织，逻辑清晰
- **可读性**：相关功能集中管理

### 方案2：按中间件分类

#### 目标结构
```
route/
├── middleware/
│   ├── public.php              # 公开接口（无中间件）
│   ├── auth_only.php           # 仅Token认证
│   ├── auth_permission.php     # Token + 权限验证
│   └── auth_permission_log.php # Token + 权限 + 日志
└── modules/
    ├── crm.php                 # CRM模块路由定义
    ├── system.php              # 系统模块路由定义
    └── ...
```

---

## 🛠️ 实施计划

### 阶段1：CRM模块合并
**目标**：将22个CRM路由文件合并为1个

**当前CRM文件列表**：
```
crm_business.php
crm_business_product.php
crm_contact.php
crm_contract.php
crm_contract_product.php
crm_contract_receivable.php
crm_customer_my.php
crm_customer_sea.php
crm_customer_share.php
crm_customer_share_log.php
crm_follow_record.php
crm_lead.php
crm_lead_pool.php
crm_product.php
crm_product_category.php
crm_product_spec.php
crm_product_unit.php
crm_sea_rule.php
crm_statistics.php
crm_tag.php
crm_work_report.php
crm_work_report_template.php
```

**合并后结构**：
```php
// route/modules/crm.php
<?php
use think\facade\Route;
use app\common\middleware\TokenAuthMiddleware;
use app\common\middleware\PermissionMiddleware;

$nameSpace = 'app\crm\controller';

// CRM客户管理
Route::group('crm/customer', function () use ($nameSpace) {
    Route::get('my/index', $nameSpace . '\CrmCustomerMyController@index');
    Route::post('my/add', $nameSpace . '\CrmCustomerMyController@add');
    Route::put('my/edit', $nameSpace . '\CrmCustomerMyController@edit');
    Route::delete('my/delete', $nameSpace . '\CrmCustomerMyController@delete');
    
    Route::get('sea/index', $nameSpace . '\CrmCustomerSeaController@index');
    Route::post('sea/claim', $nameSpace . '\CrmCustomerSeaController@claim');
    
    Route::get('share/index', $nameSpace . '\CrmCustomerShareController@index');
    Route::post('share/add', $nameSpace . '\CrmCustomerShareController@add');
});

// CRM线索管理
Route::group('crm/lead', function () use ($nameSpace) {
    Route::get('index', $nameSpace . '\CrmLeadController@index');
    Route::post('add', $nameSpace . '\CrmLeadController@add');
    Route::put('edit', $nameSpace . '\CrmLeadController@edit');
    
    Route::get('pool/index', $nameSpace . '\CrmLeadPoolController@index');
    Route::post('pool/assign', $nameSpace . '\CrmLeadPoolController@assign');
});

// CRM产品管理
Route::group('crm/product', function () use ($nameSpace) {
    Route::get('index', $nameSpace . '\CrmProductController@index');
    Route::post('add', $nameSpace . '\CrmProductController@add');
    Route::put('edit', $nameSpace . '\CrmProductController@edit');
    Route::delete('delete', $nameSpace . '\CrmProductController@delete');
    
    Route::get('category/index', $nameSpace . '\CrmProductCategoryController@index');
    Route::post('category/add', $nameSpace . '\CrmProductCategoryController@add');
    
    Route::get('spec/index', $nameSpace . '\CrmProductSpecController@index');
    Route::get('unit/index', $nameSpace . '\CrmProductUnitController@index');
});

// CRM商机管理
Route::group('crm/business', function () use ($nameSpace) {
    Route::get('index', $nameSpace . '\CrmBusinessController@index');
    Route::post('add', $nameSpace . '\CrmBusinessController@add');
    Route::put('edit', $nameSpace . '\CrmBusinessController@edit');
    
    Route::get('product/index', $nameSpace . '\CrmBusinessProductController@index');
    Route::post('product/add', $nameSpace . '\CrmBusinessProductController@add');
});

// CRM合同管理
Route::group('crm/contract', function () use ($nameSpace) {
    Route::get('index', $nameSpace . '\CrmContractController@index');
    Route::post('add', $nameSpace . '\CrmContractController@add');
    Route::put('edit', $nameSpace . '\CrmContractController@edit');
    
    Route::get('product/index', $nameSpace . '\CrmContractProductController@index');
    Route::get('receivable/index', $nameSpace . '\CrmContractReceivableController@index');
});

// CRM其他功能
Route::group('crm', function () use ($nameSpace) {
    Route::get('contact/index', $nameSpace . '\CrmContactController@index');
    Route::post('contact/add', $nameSpace . '\CrmContactController@add');
    
    Route::get('follow/index', $nameSpace . '\CrmFollowRecordController@index');
    Route::post('follow/add', $nameSpace . '\CrmFollowRecordController@add');
    
    Route::get('tag/index', $nameSpace . '\CrmTagController@index');
    Route::post('tag/add', $nameSpace . '\CrmTagController@add');
    
    Route::get('sea_rule/index', $nameSpace . '\CrmSeaRuleController@index');
    Route::post('sea_rule/add', $nameSpace . '\CrmSeaRuleController@add');
    
    Route::get('statistics/index', $nameSpace . '\CrmStatisticsController@index');
    Route::get('statistics/dashboard', $nameSpace . '\CrmStatisticsController@dashboard');
    
    Route::get('work_report/index', $nameSpace . '\CrmWorkReportController@index');
    Route::post('work_report/add', $nameSpace . '\CrmWorkReportController@add');
    Route::get('work_report/template/index', $nameSpace . '\CrmWorkReportTemplateController@index');
});

// 统一中间件配置
Route::group('', function () {
    // 包含上述所有CRM路由
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);
```

### 阶段2：项目模块合并
**目标**：将5个项目路由文件合并为1个

**当前项目文件列表**：
```
project_project.php
project_project_member.php
project_project_task.php
project_project_task_log.php
project_project_task_member.php
```

### 阶段3：中间件规范化
**目标**：建立统一的中间件配置规范

---

## 📋 中间件配置规范

### 接口分类标准

#### 1. 公开接口 (Public)
- **特征**：无需认证的接口
- **示例**：验证码、公开信息查询
- **中间件**：无
- **文件**：`route/public.php`

#### 2. 认证接口 (Auth)
- **特征**：登录、注册相关
- **示例**：用户登录、密码重置
- **中间件**：`CheckLoginAttempts`
- **文件**：`route/auth.php`

#### 3. 公共接口 (Common)
- **特征**：需要登录但不需要权限验证
- **示例**：选项列表、基础数据
- **中间件**：`TokenAuthMiddleware`
- **文件**：`route/common.php`

#### 4. 业务接口 (Business)
- **特征**：需要登录和权限验证
- **示例**：CRM、项目管理
- **中间件**：`TokenAuthMiddleware` + `PermissionMiddleware`
- **文件**：`route/modules/*.php`

#### 5. 管理接口 (Admin)
- **特征**：需要完整权限验证和操作日志
- **示例**：系统管理、用户管理
- **中间件**：`TokenAuthMiddleware` + `PermissionMiddleware` + `OperationLogMiddleware`
- **文件**：`route/modules/system.php`

---

## ✅ 验收标准

### 文件数量目标
- **当前**：38个路由文件
- **目标**：≤ 15个路由文件
- **减少**：≥ 60%

### 维护性目标
- 相同模块路由集中管理
- 中间件配置标准化
- 开发规范文档完善

### 性能目标
- 路由加载时间优化
- 文件读取次数减少
- 内存占用降低

---

## 🚀 实施时间表

### 第1周：CRM模块合并
- 创建合并后的CRM路由文件
- 测试功能完整性
- 删除原有22个文件

### 第2周：其他模块合并
- 合并项目、工作流等模块
- 统一中间件配置
- 功能测试

### 第3周：规范建立
- 制定开发规范文档
- 团队培训
- 代码审查标准

### 第4周：验收测试
- 全功能测试
- 性能测试
- 文档完善

---

## 📚 开发规范

### 新增路由规范

#### 1. 确定接口类型
根据接口功能确定所属类型：
- 是否需要登录？
- 是否需要权限验证？
- 是否需要操作日志？

#### 2. 选择路由文件
根据接口类型选择对应的路由文件：
- 业务功能 → `modules/{module}.php`
- 公共功能 → `common.php`
- 认证功能 → `auth.php`
- 公开功能 → `public.php`

#### 3. 添加路由定义
```php
// 在对应文件中添加路由
Route::get('path', $nameSpace . '\ControllerName@method');
```

### 中间件配置规范

#### 1. 使用预定义组合
优先使用标准中间件组合，避免自定义配置。

#### 2. 特殊需求处理
如需特殊中间件配置，在路由定义时单独指定：
```php
Route::get('special', $nameSpace . '\ControllerName@method')
    ->middleware([CustomMiddleware::class]);
```

#### 3. 文档更新
新增特殊配置时，及时更新文档说明。

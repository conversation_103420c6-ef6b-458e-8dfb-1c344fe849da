<?php
/**
 * 最终权限验证脚本
 * 验证权限规范化后的系统功能
 */

require_once 'vendor/autoload.php';

echo "=== 最终权限验证 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 权限数据统计:\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE status = 1 AND deleted_at IS NULL");
    $stmt->execute();
    $totalPermissions = $stmt->fetch()['count'];
    echo "  总权限数: {$totalPermissions} 个\n";
    
    // 按模块统计
    $stmt = $pdo->prepare("
        SELECT 
            SUBSTRING_INDEX(name, ':', 1) as module,
            COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL 
        AND name LIKE '%:%'
        GROUP BY SUBSTRING_INDEX(name, ':', 1)
        ORDER BY count DESC
    ");
    $stmt->execute();
    $moduleStats = $stmt->fetchAll();
    
    echo "  模块分布:\n";
    foreach ($moduleStats as $stat) {
        echo "    {$stat['module']}: {$stat['count']} 个\n";
    }
    
    echo "\n2. 权限命名规范性检查:\n";
    
    // 检查命名格式
    $stmt = $pdo->prepare("
        SELECT 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END as format_type,
            COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END
        ORDER BY count DESC
    ");
    $stmt->execute();
    $formatStats = $stmt->fetchAll();
    
    echo "  命名格式分布:\n";
    foreach ($formatStats as $stat) {
        echo "    {$stat['format_type']}: {$stat['count']} 个\n";
    }
    
    echo "\n3. 测试权限解析逻辑:\n";
    
    // 模拟权限解析
    function testPermissionParsing($ruleName) {
        // 分割类路径和方法名
        [$classPath, $method] = explode('@', $ruleName);
        $parts = explode('\\', $classPath);
        $module = strtolower($parts[1]);
        $controllerClass = $parts[count($parts) - 1];
        $controllerName = str_replace('Controller', '', $controllerClass);
        
        // 生成权限名称
        $permissionName = generatePermissionName($module, $parts, $controllerName, $method);
        
        return "{$module}:{$permissionName}";
    }
    
    function generatePermissionName($module, $parts, $controllerName, $method) {
        switch ($module) {
            case 'crm':
                return generateCrmPermissionName($controllerName, $method);
            case 'system':
                return generateSystemPermissionName($parts, $controllerName, $method);
            case 'project':
                return generateProjectPermissionName($controllerName, $method);
            case 'notice':
                return generateNoticePermissionName($controllerName, $method);
            case 'office':
                return generateOfficePermissionName($controllerName, $method);
            case 'workflow':
                return generateWorkflowPermissionName($controllerName, $method);
            default:
                return strtolower($module . '_' . $controllerName) . ':' . $method;
        }
    }
    
    function generateCrmPermissionName($controllerName, $method) {
        $snakeName = camelToSnake($controllerName);
        if (!str_starts_with($snakeName, 'crm_')) {
            $snakeName = 'crm_' . $snakeName;
        }
        return $snakeName . ':' . $method;
    }
    
    function generateSystemPermissionName($parts, $controllerName, $method) {
        if (count($parts) > 4) {
            $subPath = strtolower($parts[3]);
            $controller = strtolower($controllerName);
            return $subPath . ':' . $controller . ':' . $method;
        } else {
            $controller = strtolower($controllerName);
            $baseControllers = ['article', 'attachment', 'auth', 'config', 'dict', 'log', 'system', 'upload', 'tenant', 'attachmentcat'];
            
            if (in_array($controller, $baseControllers)) {
                return $controller . ':' . $method;
            } else {
                return 'system_' . $controller . ':' . $method;
            }
        }
    }
    
    function generateProjectPermissionName($controllerName, $method) {
        $snakeName = camelToSnake($controllerName);
        if ($controllerName === 'Project') {
            return 'project:' . $method;
        }
        return $snakeName . ':' . $method;
    }
    
    function generateNoticePermissionName($controllerName, $method) {
        $snakeName = camelToSnake($controllerName);
        if (!str_starts_with($snakeName, 'notice_')) {
            $snakeName = 'notice_' . $snakeName;
        }
        return $snakeName . ':' . $method;
    }
    
    function generateOfficePermissionName($controllerName, $method) {
        $snakeName = camelToSnake($controllerName);
        if (!str_starts_with($snakeName, 'office_')) {
            $snakeName = 'office_' . $snakeName;
        }
        return $snakeName . ':' . $method;
    }
    
    function generateWorkflowPermissionName($controllerName, $method) {
        $snakeName = camelToSnake($controllerName);
        if (!str_starts_with($snakeName, 'workflow_')) {
            $snakeName = 'workflow_' . $snakeName;
        }
        return $snakeName . ':' . $method;
    }
    
    function camelToSnake($input) {
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
    }
    
    // 测试用例
    $testCases = [
        'app\\crm\\controller\\CrmCustomerMyController@index' => 'crm:crm_customer_my:index',
        'app\\crm\\controller\\CrmLeadController@add' => 'crm:crm_lead:add',
        'app\\system\\controller\\permission\\AdminController@index' => 'system:permission:admin:index',
        'app\\system\\controller\\ConfigController@index' => 'system:config:index',
        'app\\project\\controller\\ProjectController@index' => 'project:project:index',
        'app\\notice\\controller\\MessageController@delete' => 'notice:notice_message:delete',
        'app\\workflow\\controller\\ApplicationController@create' => 'workflow:workflow_application:create',
    ];
    
    $passedTests = 0;
    $totalTests = count($testCases);
    
    foreach ($testCases as $input => $expected) {
        $result = testPermissionParsing($input);
        
        // 检查权限是否存在于数据库
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE name = ? AND status = 1 AND deleted_at IS NULL");
        $stmt->execute([$expected]);
        $exists = $stmt->fetch()['count'] > 0;
        
        $status = ($result === $expected && $exists) ? '✅' : '❌';
        echo "  {$status} {$input}\n";
        echo "    期望: {$expected}\n";
        echo "    实际: {$result}\n";
        echo "    数据库: " . ($exists ? '存在' : '不存在') . "\n";
        
        if ($result === $expected && $exists) {
            $passedTests++;
        }
        echo "\n";
    }
    
    echo "4. 权限解析测试结果:\n";
    echo "  测试通过: {$passedTests}/{$totalTests}\n";
    echo "  成功率: " . round($passedTests / $totalTests * 100, 1) . "%\n";
    
    echo "\n5. 用户权限验证:\n";
    
    // 检查tenant_admin用户权限
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM system_admin_role ar
        LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
        LEFT JOIN system_menu m ON rm.menu_id = m.id
        WHERE ar.admin_id = 201 AND m.status = 1 AND m.deleted_at IS NULL
    ");
    $stmt->execute();
    $userPermissionCount = $stmt->fetch()['count'];
    
    echo "  tenant_admin权限数: {$userPermissionCount} 个\n";
    echo "  权限覆盖率: " . round($userPermissionCount / $totalPermissions * 100, 1) . "%\n";
    
    echo "\n6. 最终验证结果:\n";
    
    $allChecks = [
        $totalPermissions > 300,
        $passedTests >= 6,
        $userPermissionCount > 300
    ];
    
    $passedChecks = array_sum($allChecks);
    
    if ($passedChecks === count($allChecks)) {
        echo "  ✅ 所有验证检查通过！\n";
        echo "  ✅ 权限规范化成功完成\n";
        echo "  ✅ 系统可以正常使用\n";
    } else {
        echo "  ⚠️ 部分验证检查未通过\n";
        echo "  需要进一步检查和修复\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 权限验证完成 ===\n";

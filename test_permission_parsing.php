<?php
/**
 * 测试权限解析功能
 */

require_once 'vendor/autoload.php';

echo "=== 测试权限解析功能 ===\n\n";

// 模拟PermissionService的parsePermissionInfo方法
function parsePermissionInfo(string $ruleName): array
{
    // 分割类路径和方法名
    [
        $classPath,
        $method
    ] = explode('@', $ruleName);
    
    // 分割类路径为命名空间部分
    $parts = explode('\\', $classPath);
    
    // 提取模块名（app\模块\controller\...）
    $module = strtolower($parts[1]); // 第二个部分是模块名
    
    // 提取控制器名（最后一个命名空间部分）
    $controllerClass = $parts[count($parts) - 1];
    
    // 去掉Controller后缀，转换为小写
    $controller = strtolower(str_replace('Controller', '', $controllerClass));
    
    // 处理子目录情况（如：permission/admin, log/login）
    if (count($parts) > 4) {
        // 有子目录，格式：app\system\controller\permission\AdminController
        $subPath = strtolower($parts[3]); // 子目录名
        $controller = $subPath . ':' . $controller;
    }
    
    // 返回解析后的信息
    return [
        $module,
        $controller,
        $method
    ];
}

// 测试用例
$testCases = [
    // CRM模块
    'app\\crm\\controller\\CrmCustomerMyController@index' => 'crm:crmcustomermy:index',
    'app\\crm\\controller\\CrmLeadController@add' => 'crm:crmlead:add',
    
    // System模块 - 基础控制器
    'app\\system\\controller\\AuthController@login' => 'system:auth:login',
    'app\\system\\controller\\ConfigController@detail' => 'system:config:detail',
    'app\\system\\controller\\AttachmentController@index' => 'system:attachment:index',
    
    // System模块 - 权限控制器（子目录）
    'app\\system\\controller\\permission\\AdminController@index' => 'system:permission:admin:index',
    'app\\system\\controller\\permission\\RoleController@add' => 'system:permission:role:add',
    'app\\system\\controller\\permission\\MenuController@edit' => 'system:permission:menu:edit',
    
    // System模块 - 日志控制器（子目录）
    'app\\system\\controller\\log\\LoginController@index' => 'system:log:login:index',
    'app\\system\\controller\\log\\OperationController@detail' => 'system:log:operation:detail',
    
    // System模块 - 租户控制器（子目录）
    'app\\system\\controller\\tenant\\TenantConfigController@save' => 'system:tenant:tenantconfig:save',
    
    // HR模块
    'app\\hr\\controller\\HrMonthlyStatsController@getEmployeeStats' => 'hr:hrmonthlystats:getEmployeeStats',
    
    // Project模块
    'app\\project\\controller\\ProjectController@index' => 'project:project:index',
];

echo "1. 权限解析测试:\n";

$successCount = 0;
$totalCount = count($testCases);

foreach ($testCases as $input => $expected) {
    $result = parsePermissionInfo($input);
    $actual = implode(':', $result);
    
    $status = ($actual === $expected) ? '✅' : '❌';
    echo "  {$status} {$input}\n";
    echo "    期望: {$expected}\n";
    echo "    实际: {$actual}\n";
    
    if ($actual === $expected) {
        $successCount++;
    } else {
        echo "    ⚠️ 解析结果不匹配！\n";
    }
    echo "\n";
}

echo "2. 测试结果统计:\n";
echo "  总测试数: {$totalCount}\n";
echo "  成功数: {$successCount}\n";
echo "  失败数: " . ($totalCount - $successCount) . "\n";
echo "  成功率: " . round($successCount / $totalCount * 100, 1) . "%\n\n";

if ($successCount === $totalCount) {
    echo "✅ 所有权限解析测试通过！\n";
} else {
    echo "❌ 部分权限解析测试失败，需要调整解析逻辑\n";
}

echo "\n=== 权限解析测试完成 ===\n";

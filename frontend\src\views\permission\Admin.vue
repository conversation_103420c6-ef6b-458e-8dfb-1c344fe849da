<template>
  <ArtTableFullScreen>
    <div class="account-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader :columnList="columnOptions" @refresh="handleRefresh">
          <template #left>
            <ElButton
              @click="showDialog('add')"
              icon="Plus"
              v-auth="'system:permission_admin:add'"
              type="primary"
              v-ripple
              >新增
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :data="tableData"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :marginTop="10"
        >
          <template #default>
            <!-- 勾选列 -->
            <ElTableColumn type="selection" />

            <!-- 用户名列 -->
            <ElTableColumn prop="username" label="用户名" min-width="150">
              <template #default="{ row }">
                <div class="user" style="display: flex; align-items: center">
                  <img class="avatar" :src="row.avatar || '/placeholder-avatar.png'" />
                  <div>
                    <p class="user-name">{{ row.username }}</p>
                    <p class="email">{{ row.email }}</p>
                  </div>
                </div>
              </template>
            </ElTableColumn>

            <!-- 手机号列 -->
            <ElTableColumn prop="mobile" label="手机号" />

            <!-- 性别列 -->
            <ElTableColumn prop="gender" label="性别">
              <template #default="{ row }">
                {{ row.gender_text }}
              </template>
            </ElTableColumn>

            <!-- 部门列 -->
            <ElTableColumn prop="dept_id" label="部门">
              <template #default="{ row }">
                {{ row.dept?.name }}
              </template>
            </ElTableColumn>

            <!-- 状态列 -->
            <ElTableColumn prop="status" label="状态">
              <template #default="{ row }">
                <ElTag :type="getTagType(row.status)">
                  {{ buildTagText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>

            <!-- 创建时间列 -->
            <ElTableColumn prop="created_at" label="创建时间" sortable />

            <!-- 操作列 -->
            <ElTableColumn prop="operation" label="操作" width="260">
              <template #default="{ row }">
                <div>
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_admin:reset_password')"
                    text="密码重置"
                    @click="showChangePassword(row)"
                  />
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_admin:edit')"
                    text="编辑"
                    type="edit"
                    @click="showDialog('edit', row)"
                  />
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_admin:delete')"
                    text="删除"
                    type="delete"
                    @click="deleteUser(row.id)"
                  />
                </div>
              </template>
            </ElTableColumn>
          </template>
        </ArtTable>

        <ElDialog v-model="dialogVisible" :title="dialogType === 'add' ? '添加用户' : '编辑用户'">
          <ElForm
            ref="formRef"
            :model="formData"
            :rules="rules"
            :loading="dialogLoading"
            label-width="80px"
          >
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="用户名" prop="username">
                  <ElInput v-model="formData.username" clearable placeholder="请输入用户名" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="密码" prop="password" v-if="dialogType === 'add'">
                  <ElInput
                    v-model="formData.password"
                    type="password"
                    clearable
                    placeholder="请输入密码"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>

            <el-form-item label="头像" prop="images">
              <FormMediaSelector v-model="formData.avatar" media-type="image" :max-count="1" />
            </el-form-item>

            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="真实姓名" prop="real_name">
                  <ElInput v-model="formData.real_name" clearable placeholder="请输入真实姓名" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="手机号" prop="mobile">
                  <ElInput v-model="formData.mobile" clearable placeholder="请输入手机号" />
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="邮箱" prop="email">
                  <ElInput v-model="formData.email" clearable placeholder="请输入邮箱" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="性别" prop="gender">
                  <ElSelect v-model="formData.gender" clearable>
                    <ElOption label="请选择性别" :value="0" />
                    <ElOption label="男" :value="1" />
                    <ElOption label="女" :value="2" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="角色" prop="role_ids">
                  <ElTreeSelect
                    v-model="formData.role_ids"
                    :data="roleOptions"
                    :props="{ label: 'name', value: 'id' }"
                    check-strictly
                    default-expand-all
                    clearable
                    multiple
                    placeholder="请选择角色"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="状态" prop="status">
                  <ElSwitch
                    v-model="formData.status"
                    :active-value="1"
                    :inactive-value="0"
                    active-text="启用"
                    inactive-text="禁用"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>

            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="部门" prop="dept_id">
                  <ElTreeSelect
                    v-model="formData.dept_id"
                    :data="deptOptions"
                    :props="{ label: 'name', value: 'id' }"
                    check-strictly
                    default-expand-all
                    clearable
                    placeholder="请选择部门"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="岗位" prop="post_id">
                  <ElSelect v-model="formData.post_ids" clearable multiple>
                    <ElOption
                      v-for="post in postOptions"
                      :key="post.id"
                      :label="post.name"
                      :value="post.id"
                    />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>

          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="dialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="handleSubmit">提交</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 密码重置对话框 -->
        <ElDialog v-model="passwordDialogVisible" title="重置密码" width="30%">
          <ElForm
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="80px"
          >
            <ElFormItem label="新密码" prop="password">
              <ElInput
                v-model="passwordForm.password"
                type="password"
                show-password
                clearable
                placeholder="请输入新密码"
              />
            </ElFormItem>
            <ElFormItem label="确认密码" prop="confirmPassword">
              <ElInput
                v-model="passwordForm.confirmPassword"
                type="password"
                show-password
                clearable
                placeholder="请再次输入密码"
              />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="passwordDialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="handleResetPassword" :loading="resetPasswordLoading"
                >确认
              </ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ElDialog, FormInstance, ElTag, ElTreeSelect, ElButton } from 'element-plus'
  import { ElMessageBox, ElMessage } from 'element-plus'
  import type { FormRules } from 'element-plus'

  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { AdminApi } from '@/api/adminApi'
  import { DepartmentApi } from '@/api/departmentApi'
  import { PostApi } from '@/api/postApi'
  import { RoleApi } from '@/api/roleApi'
  import { ApiStatus } from '@/utils/http/status'
  import FormMediaSelector from '@/components/custom/FormMediaSelector/index.vue'
  import { useAuth } from '@/composables/useAuth'

  const { hasAuth } = useAuth()

  const dialogType = ref('add')
  const dialogVisible = ref(false)
  const loading = ref(false)
  const dialogLoading = ref(false)
  const deptOptions = ref<Array<{ id: number; name: string }>>([]),
    postOptions = ref<Array<{ id: number; name: string }>>([]),
    roleOptions = ref<Array<{ id: number; name: string }>>([])

  // 定义表单搜索初始值
  const initialSearchState = {
    username: '',
    mobile: '',
    gender: '',
    dept_id: '',
    status: '',
    email: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    currentPage.value = 1
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '用户名',
      prop: 'username',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },
    {
      label: '电话',
      prop: 'mobile',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },
    /*{
      label: '邮箱',
      prop: 'email',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },
    {
      label: '性别',
      prop: 'gender',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '男', value: 1 },
        { label: '女', value: 2 },
        { label: '未知', value: 0 }
      ],
      onChange: handleFormChange
    },*/
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '正常', value: 1 },
        { label: '禁用', value: 0 }
      ],
      onChange: handleFormChange
    }
  ]

  // 列配置
  const columnOptions = [
    { label: '勾选', type: 'selection' },
    { label: '用户名', prop: 'username' },
    { label: '手机号', prop: 'mobile' },
    { label: '性别', prop: 'gender' },
    { label: '部门', prop: 'dept_id' },
    { label: '状态', prop: 'status' },
    { label: '创建时间', prop: 'created_at' },
    { label: '操作', prop: 'operation' }
  ]

  // 获取标签类型
  const getTagType = (status: number) => {
    switch (status) {
      case 1:
        return 'success'
      case 0:
        return 'danger'
      default:
        return 'info'
    }
  }

  // 构建标签文本
  const buildTagText = (status: number) => {
    return status === 1 ? '正常' : '禁用'
  }

  const adminId = ref(0)

  // 显示对话框
  const showDialog = async (type: string, row?: any) => {
    // 重置表单
    formData.username = ''
    formData.mobile = ''
    formData.email = ''
    formData.real_name = ''
    formData.gender = 1
    formData.dept_id = ''
    formData.status = 1
    formData.password = ''
    formData.avatar = ''
    adminId.value = 0

    dialogVisible.value = true
    dialogType.value = type
    dialogLoading.value = true

    try {
      await Promise.all([getDepartments(), getPosts(), getRoles()])

      if (type === 'edit' && row) {
        const res = await AdminApi.detail(row.id)
        if (res.code === ApiStatus.success && res.data) {
          const userData = res.data
          formData.username = userData.username
          formData.mobile = userData.mobile
          formData.email = userData.email
          formData.real_name = userData.real_name
          formData.gender = userData.gender
          formData.dept_id = userData.dept_id
          formData.role_ids = userData.role_ids
          formData.status = userData.status
          formData.avatar = userData.avatar
          adminId.value = userData.id
        }
      }
    } catch (error) {
      console.error('加载对话框数据失败', error)
    } finally {
      dialogLoading.value = false
    }
  }

  // 删除用户
  const deleteUser = (id: number) => {
    ElMessageBox.confirm('确定要删除该用户吗？', '删除用户', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const res = await AdminApi.delete([id])
          if (res.code === ApiStatus.success) {
            ElMessage.success('删除成功')
            await getTableData()
          }
        } catch (error) {
          console.error('删除用户失败', error)
          // ElMessage.error('删除用户失败')
        }
      })
      .catch(() => {
        // 取消删除
      })
  }

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    username: '',
    password: '',
    avatar: '',
    mobile: '',
    email: '',
    real_name: '',
    gender: 0,
    dept_id: '',
    post_ids: [],
    status: 1,
    role_ids: []
  })

  // 表格数据
  const tableData = ref<any[]>([])

  const currentPage = ref(1),
    pageSize = ref(10),
    total = ref(0)

  onMounted(() => {
    getTableData()
    // 获取部门列表，用于下拉选择
    // getDepartments()
  })

  // 获取部门列表
  const getDepartments = async () => {
    try {
      // 这里应该调用获取部门列表的API
      const res = await DepartmentApi.options()
      if (res.code === ApiStatus.success) {
        deptOptions.value = res.data
      }
    } catch (error) {
      console.error('获取部门列表失败', error)
    }
  }

  const getPosts = async () => {
    try {
      const res = await PostApi.options()
      if (res.code === ApiStatus.success) {
        postOptions.value = res.data
      }
    } catch (error) {
      console.error('获取部门列表失败', error)
    }
  }

  const getRoles = async () => {
    try {
      const res = await RoleApi.options()
      if (res.code === ApiStatus.success) {
        roleOptions.value = res.data
      }
    } catch (error) {
      console.error('获取部门列表失败', error)
    }
  }

  const getTableData = async () => {
    loading.value = true
    try {
      const params = {
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      }

      const res = await AdminApi.list(params)
      if (res.code === ApiStatus.success) {
        // 按照PaginationResult结构获取数据
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } catch (error) {
      console.error('获取用户列表失败', error)
      // ElMessage.error('获取用户列表失败')
    } finally {
      loading.value = false
    }
  }

  // 处理页码变化
  const handleCurrentChange = (page: number) => {
    currentPage.value = page
    getTableData()
  }

  // 处理每页条数变化
  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    getTableData()
  }

  const handleRefresh = () => {
    getTableData()
  }

  // 表单验证规则
  const rules = reactive<FormRules>({
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    mobile: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
    ],
    email: [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
    password: [
      { required: dialogType.value === 'add', message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
    ]
  })

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          let res
          if (dialogType.value === 'add') {
            res = await AdminApi.add(formData)
          } else {
            res = await AdminApi.edit(adminId.value, formData)
          }

          if (res.code === 1) {
            ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
            dialogVisible.value = false
            await getTableData()
          } else {
            ElMessage.error(res.message || (dialogType.value === 'add' ? '添加失败' : '更新失败'))
          }
        } catch (error) {
          console.error(dialogType.value === 'add' ? '添加用户失败' : '更新用户失败', error)
          // ElMessage.error(dialogType.value === 'add' ? '添加用户失败' : '更新用户失败')
        }
      }
    })
  }

  // 重置密码相关变量
  const passwordDialogVisible = ref(false)
  const resetPasswordLoading = ref(false)
  const currentResetUserId = ref(0)
  const passwordFormRef = ref<FormInstance>()
  const passwordForm = reactive({
    password: '',
    confirmPassword: ''
  })

  // 重置密码表单验证规则
  const passwordRules = reactive<FormRules>({
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: '请再次输入密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== passwordForm.password) {
            callback(new Error('两次输入密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  })

  // 显示重置密码对话框
  const showChangePassword = (row: any) => {
    passwordDialogVisible.value = true
    currentResetUserId.value = row.id
    passwordForm.password = ''
    passwordForm.confirmPassword = ''
  }

  // 处理重置密码
  const handleResetPassword = async () => {
    if (!passwordFormRef.value) return

    await passwordFormRef.value.validate(async (valid) => {
      if (valid) {
        resetPasswordLoading.value = true
        try {
          const res = await AdminApi.resetPassword(currentResetUserId.value, {
            password: passwordForm.password,
            confirm_password: passwordForm.confirmPassword
          })

          if (res.code === 1) {
            ElMessage.success('密码重置成功')
            passwordDialogVisible.value = false
          } else {
            ElMessage.error(res.message || '密码重置失败')
          }
        } catch (error) {
          console.error('密码重置失败', error)
          // ElMessage.error('密码重置失败')
        } finally {
          resetPasswordLoading.value = false
        }
      }
    })
  }
</script>

<style lang="scss" scoped>
  .account-page {
    :deep(.user) {
      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 6px;
      }

      > div {
        margin-left: 10px;

        .user-name {
          font-weight: 500;
          color: var(--art-text-gray-800);
        }
      }
    }
  }
</style>

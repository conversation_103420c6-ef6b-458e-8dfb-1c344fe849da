<template>
  <ArtTableFullScreen>
    <div class="post-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          @refresh="handleRefresh"
        >
          <template #left>
            <ElButton
              v-auth="'system:permission_post:add'"
              @click="showDialog('add')"
              type="primary"
              v-ripple
              >新增
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :data="tableData"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :total="total"
          :marginTop="10"
        >
          <template #default>
            <!-- 岗位名称列 -->
            <ElTableColumn prop="name" label="岗位名称" />

            <!-- 岗位编码列 -->
            <ElTableColumn prop="code" label="岗位编码" />

            <!-- 排序列 -->
            <ElTableColumn prop="sort" label="排序" width="80" />

            <!-- 状态列 -->
            <ElTableColumn prop="status" label="状态" width="100">
              <template #default="{ row }">
                <ElTag :type="getStatusTagType(row.status)">
                  {{ buildStatusTagText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>

            <!-- 创建时间列 -->
            <ElTableColumn prop="created_at" label="创建时间" sortable />

            <!-- 备注列 -->
            <ElTableColumn prop="remark" label="备注" />

            <!-- 操作列 -->
            <ElTableColumn prop="operation" label="操作" width="230">
              <template #default="{ row }">
                <div>
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_post:edit')"
                    text="编辑"
                    type="edit"
                    @click="showDialog('edit', row)"
                  />
                  <ArtButtonTable
                    v-if="hasAuth('system:permission_post:delete')"
                    text="删除"
                    type="delete"
                    @click="deletePost(row.id)"
                  />
                </div>
              </template>
            </ElTableColumn>
          </template>
        </ArtTable>

        <ElDialog
          v-model="dialogVisible"
          :title="dialogType === 'add' ? '添加岗位' : '编辑岗位'"
          width="30%"
        >
          <ElForm ref="formRef" :model="formData" :rules="rules" label-width="80px">
            <ElFormItem label="岗位名称" prop="name">
              <ElInput v-model="formData.name" />
            </ElFormItem>
            <ElFormItem label="岗位编码" prop="code">
              <ElInput v-model="formData.code" />
            </ElFormItem>
            <ElFormItem label="排序" prop="sort">
              <ElInputNumber v-model="formData.sort" />
            </ElFormItem>
            <ElFormItem label="状态" prop="status">
              <ElSwitch
                v-model="formData.status"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
            </ElFormItem>
            <ElFormItem label="备注" prop="remark">
              <ElInput v-model="formData.remark" type="textarea" />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="dialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="handleSubmit">提交</ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ElDialog, FormInstance, ElTag } from 'element-plus'
  import { ElMessageBox, ElMessage } from 'element-plus'
  import type { FormRules } from 'element-plus'

  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { PostApi } from '@/api/postApi'
  import { useAuth } from '@/composables/useAuth'

  // 权限验证
  const { hasAuth } = useAuth()

  const dialogType = ref('add')
  const dialogVisible = ref(false)
  const loading = ref(false)
  const postId = ref(0)

  // 定义表单搜索初始值
  const initialSearchState = {
    name: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      label: '岗位名称',
      prop: 'name',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },
    /*{
      label: '岗位编码',
      prop: 'code',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },*/
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ],
      onChange: handleFormChange
    }
  ]

  // 列配置
  const columnOptions = [
    { label: '岗位名称', prop: 'name' },
    { label: '岗位编码', prop: 'code' },
    { label: '排序', prop: 'sort' },
    { label: '状态', prop: 'status' },
    { label: '创建时间', prop: 'created_at' },
    { label: '备注', prop: 'remark' },
    { label: '操作', prop: 'operation' }
  ]

  // 获取标签类型
  // 1: 启用 0: 禁用
  const getStatusTagType = (status: number) => {
    return status === 1 ? 'success' : 'danger'
  }

  // 构建标签文本
  const buildStatusTagText = (status: number) => {
    return status === 1 ? '启用' : '禁用'
  }



  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    name: '',
    code: '',
    sort: 0,
    status: 1,
    remark: ''
  })

  // 表格数据
  const tableData = ref<any[]>([])

  const currentPage = ref(1),
    pageSize = ref(10),
    total = ref(0)

  onMounted(() => {
    getTableData()
  })

  const getTableData = async () => {
    loading.value = true
    const res = await PostApi.list({
      page: currentPage.value,
      limit: pageSize.value,
      ...formFilters
    })
    loading.value = false
    if (res.code === 1) {
      total.value = res.data.total || 0
      currentPage.value = res.data.page || 1
      pageSize.value = res.data.limit || 10
      tableData.value = res.data.list || []
    }
  }

  const handleRefresh = () => {
    getTableData()
  }

  // 显示对话框
  const showDialog = async (type: string, row?: any) => {
    dialogVisible.value = true
    dialogType.value = type
    formRef.value?.clearValidate()
    if (type === 'edit' && row) {
      const res = await PostApi.detail(row.id)
      if (res.code === 1) {
        formData.name = res.data.name
        formData.code = res.data.code
        formData.sort = res.data.sort
        formData.status = res.data.status
        formData.remark = res.data.remark
        postId.value = res.data.id
      }
    } else {
      formData.name = ''
      formData.code = ''
      formData.sort = 0
      formData.status = 1
      formData.remark = ''
    }
  }

  // 删除岗位
  const deletePost = (id: number) => {
    ElMessageBox.confirm('确定要删除该岗位吗？', '删除岗位', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    }).then(async () => {
      const res = await PostApi.delete(id)
      if (res.code === 1) {
        ElMessage.success('删除成功')
        await getTableData()
      }
    })
  }

  // 表单验证规则
  const rules = reactive<FormRules>({
    name: [
      { required: true, message: '请输入岗位名称', trigger: 'blur' },
      { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ]
  })

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        let res
        if (dialogType.value === 'add') {
          res = await PostApi.add(formData)
        } else {
          res = await PostApi.edit(postId.value, formData)
        }
        dialogVisible.value = false
        if (res.code === 1) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
          await getTableData()
        }
      }
    })
  }
</script>

<style lang="scss" scoped>
  .post-page {
    width: 100%;
  }
</style>

{"generated_at": "2025-08-01 16:45:33", "total_permissions": 279, "updates": 96, "inserts": 34, "deletes": 3, "mappings": [{"id": 2582, "old_name": "article", "new_name": "system:system_article:index", "title": "公告文章", "type": 1, "change_type": "rename"}, {"id": 2371, "old_name": "crm:follow:index", "new_name": "crm:crm_follow:index", "title": "跟进记录", "type": 1, "change_type": "rename"}, {"id": 189, "old_name": "crm:index", "new_name": "crm:crm_index:index", "title": "客户管理", "type": 1, "change_type": "rename"}, {"id": 210, "old_name": "crm:product", "new_name": "crm:crm_product:index", "title": "产品管理", "type": 1, "change_type": "rename"}, {"id": 2581, "old_name": "crm:work_report/index", "new_name": "crm:crm_work_report/index:index", "title": "工作汇报", "type": 1, "change_type": "rename"}, {"id": 4, "old_name": "log", "new_name": "system:system_log:index", "title": "日志管理", "type": 1, "change_type": "rename"}, {"id": 140, "old_name": "message", "new_name": "notice:notice_message:index", "title": "消息中心", "type": 1, "change_type": "rename"}, {"id": 127, "old_name": "notice", "new_name": "notice:notice_notice:index", "title": "消息管理", "type": 1, "change_type": "rename"}, {"id": 154, "old_name": "notice:message:batchDelete", "new_name": "notice:notice_message:batchDelete", "title": "批量删除", "type": 2, "change_type": "rename"}, {"id": 155, "old_name": "notice:message:delete", "new_name": "notice:notice_message:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 136, "old_name": "notice:template:add", "new_name": "notice:notice_template:add", "title": "新增", "type": 2, "change_type": "rename"}, {"id": 139, "old_name": "notice:template:delete", "new_name": "notice:notice_template:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 138, "old_name": "notice:template:detail", "new_name": "notice:notice_template:detail", "title": "详情", "type": 2, "change_type": "rename"}, {"id": 137, "old_name": "notice:template:edit", "new_name": "notice:notice_template:edit", "title": "编辑", "type": 2, "change_type": "rename"}, {"id": 134, "old_name": "notice:template:index", "new_name": "notice:notice_template:index", "title": "消息模板", "type": 1, "change_type": "rename"}, {"id": 143, "old_name": "notice:template:preview", "new_name": "notice:notice_template:preview", "title": "预览", "type": 2, "change_type": "rename"}, {"id": 144, "old_name": "notice:template:status", "new_name": "notice:notice_template:status", "title": "状态", "type": 2, "change_type": "rename"}, {"id": 141, "old_name": "notice:tenant:templateConfig", "new_name": "notice:notice_tenant:templateConfig", "title": "模板配置", "type": 1, "change_type": "rename"}, {"id": 77, "old_name": "office", "new_name": "office:office_office:index", "title": "办公", "type": 1, "change_type": "rename"}, {"id": 101, "old_name": "office:attendance", "new_name": "office:office_attendance:index", "title": "考勤", "type": 1, "change_type": "rename"}, {"id": 78, "old_name": "office:console", "new_name": "office:office_console:index", "title": "工作台", "type": 1, "change_type": "rename"}, {"id": 79, "old_name": "office:workflow", "new_name": "office:office_workflow:index", "title": "办公审批", "type": 1, "change_type": "rename"}, {"id": 2440, "old_name": "project", "new_name": "system:system_project:index", "title": "项目管理", "type": 1, "change_type": "rename"}, {"id": 2450, "old_name": "project:project:add", "new_name": "project:project_project:add", "title": "新增", "type": 2, "change_type": "rename"}, {"id": 2459, "old_name": "project:project:addmember", "new_name": "project:project_project:addmember", "title": "添加成员", "type": 2, "change_type": "rename"}, {"id": 2452, "old_name": "project:project:delete", "new_name": "project:project_project:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 2460, "old_name": "project:project:detail", "new_name": "project:project_project:detail", "title": "详情", "type": 2, "change_type": "rename"}, {"id": 2451, "old_name": "project:project:edit", "new_name": "project:project_project:edit", "title": "编辑", "type": 2, "change_type": "rename"}, {"id": 2441, "old_name": "project:project:index", "new_name": "project:project_project:index", "title": "项目列表", "type": 1, "change_type": "rename"}, {"id": 2449, "old_name": "project:project:projectdetail", "new_name": "project:project_project:projectdetail", "title": "项目详情", "type": 1, "change_type": "rename"}, {"id": 2464, "old_name": "project:project:remove-member", "new_name": "project:project_project:remove-member", "title": "移除成员", "type": 2, "change_type": "rename"}, {"id": 2442, "old_name": "project:task:index", "new_name": "project:project_task:index", "title": "任务管理", "type": 1, "change_type": "rename"}, {"id": 34, "old_name": "system", "new_name": "system:system_system:index", "title": "系统管理", "type": 1, "change_type": "rename"}, {"id": 729, "old_name": "system:article:add", "new_name": "system:system_article:add", "title": "新增", "type": 2, "change_type": "rename"}, {"id": 731, "old_name": "system:article:delete", "new_name": "system:system_article:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 733, "old_name": "system:article:detail", "new_name": "system:system_article:detail", "title": "详情", "type": 2, "change_type": "rename"}, {"id": 730, "old_name": "system:article:edit", "new_name": "system:system_article:edit", "title": "编辑", "type": 2, "change_type": "rename"}, {"id": 107, "old_name": "system:article:index", "new_name": "system:system_article:index", "title": "列表管理", "type": 1, "change_type": "rename"}, {"id": 732, "old_name": "system:article:updateField", "new_name": "system:system_article:updateField", "title": "更新字段", "type": 2, "change_type": "rename"}, {"id": 55, "old_name": "system:attachment:delete", "new_name": "system:system_attachment:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 47, "old_name": "system:attachment:index", "new_name": "system:system_attachment:index", "title": "附件管理", "type": 1, "change_type": "rename"}, {"id": 54, "old_name": "system:attachment:move", "new_name": "system:system_attachment:move", "title": "移动分类", "type": 2, "change_type": "rename"}, {"id": 48, "old_name": "system:attachmentCat", "new_name": "system:system_attachmentCat:index", "title": "附件分类", "type": 1, "change_type": "rename"}, {"id": 56, "old_name": "system:attachmentCat:add", "new_name": "system:system_attachmentCat:add", "title": "新增", "type": 2, "change_type": "rename"}, {"id": 58, "old_name": "system:attachmentCat:delete", "new_name": "system:system_attachmentCat:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 57, "old_name": "system:attachmentCat:edit", "new_name": "system:system_attachmentCat:edit", "title": "编辑", "type": 2, "change_type": "rename"}, {"id": 50, "old_name": "system:config:detail", "new_name": "system:system_config:detail", "title": "总后台配置", "type": 1, "change_type": "rename"}, {"id": 51, "old_name": "system:config:save", "new_name": "system:system_config:save", "title": "设置", "type": 2, "change_type": "rename"}, {"id": 93, "old_name": "system:ims", "new_name": "system:system_ims:index", "title": "进销存", "type": 1, "change_type": "rename"}, {"id": 99, "old_name": "system:inventory:inbound", "new_name": "system:system_inventory:inbound", "title": "入库", "type": 1, "change_type": "rename"}, {"id": 98, "old_name": "system:inventory:outbound", "new_name": "system:system_inventory:outbound", "title": "出库", "type": 1, "change_type": "rename"}, {"id": 100, "old_name": "system:inventory:stocktaking", "new_name": "system:system_inventory:stocktaking", "title": "盘点", "type": 1, "change_type": "rename"}, {"id": 96, "old_name": "system:inventory:warehouse", "new_name": "system:system_inventory:warehouse", "title": "仓库管理", "type": 1, "change_type": "rename"}, {"id": 1, "old_name": "system:permission:index", "new_name": "system:system_permission:index", "title": "权限管理", "type": 1, "change_type": "rename"}, {"id": 59, "old_name": "system:tenant", "new_name": "system:system_tenant:index", "title": "租户管理", "type": 1, "change_type": "rename"}, {"id": 62, "old_name": "system:tenant:add", "new_name": "system:system_tenant:add", "title": "新增", "type": 2, "change_type": "rename"}, {"id": 65, "old_name": "system:tenant:delete", "new_name": "system:system_tenant:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 63, "old_name": "system:tenant:detail", "new_name": "system:system_tenant:detail", "title": "详情", "type": 2, "change_type": "rename"}, {"id": 64, "old_name": "system:tenant:edit", "new_name": "system:system_tenant:edit", "title": "编辑", "type": 2, "change_type": "rename"}, {"id": 60, "old_name": "system:tenant:index", "new_name": "system:system_tenant:index", "title": "租户列表", "type": 1, "change_type": "rename"}, {"id": 68, "old_name": "system:tenantPackage:add", "new_name": "system:system_tenantPackage:add", "title": "新增", "type": 2, "change_type": "rename"}, {"id": 71, "old_name": "system:tenantPackage:delete", "new_name": "system:system_tenantPackage:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 69, "old_name": "system:tenantPackage:detail", "new_name": "system:system_tenantPackage:detail", "title": "详情", "type": 2, "change_type": "rename"}, {"id": 70, "old_name": "system:tenantPackage:edit", "new_name": "system:system_tenantPackage:edit", "title": "编辑", "type": 2, "change_type": "rename"}, {"id": 66, "old_name": "system:tenantPackage:index", "new_name": "system:system_tenantPackage:index", "title": "租户套餐", "type": 1, "change_type": "rename"}, {"id": 103, "old_name": "system:user:attendance_data", "new_name": "system:system_user:attendance_data", "title": "考勤统计", "type": 1, "change_type": "rename"}, {"id": 102, "old_name": "user:attendance:attendance_config", "new_name": "user:user_attendance:attendance_config", "title": "考勤设置", "type": 1, "change_type": "rename"}, {"id": 49, "old_name": "UserCenter", "new_name": "system:system_UserCenter:index", "title": "个人中心", "type": 1, "change_type": "rename"}, {"id": 122, "old_name": "workflow", "new_name": "workflow:workflow_workflow:index", "title": "流程配置", "type": 1, "change_type": "rename"}, {"id": 145, "old_name": "workflow:application:create", "new_name": "workflow:workflow_application:create", "title": "新增", "type": 2, "change_type": "rename"}, {"id": 2638, "old_name": "workflow:application:delete", "new_name": "workflow:workflow_application:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 146, "old_name": "workflow:application:detail", "new_name": "workflow:workflow_application:detail", "title": "详情", "type": 2, "change_type": "rename"}, {"id": 151, "old_name": "workflow:application:edit", "new_name": "workflow:workflow_application:edit", "title": "编辑", "type": 2, "change_type": "rename"}, {"id": 81, "old_name": "workflow:application:index", "new_name": "workflow:workflow_application:index", "title": "我的申请", "type": 1, "change_type": "rename"}, {"id": 149, "old_name": "workflow:application:submit", "new_name": "workflow:workflow_application:submit", "title": "提交", "type": 2, "change_type": "rename"}, {"id": 165, "old_name": "workflow:application:urge", "new_name": "workflow:workflow_application:urge", "title": "催办", "type": 2, "change_type": "rename"}, {"id": 2584, "old_name": "workflow:application:void", "new_name": "workflow:workflow_application:void", "title": "作废", "type": 2, "change_type": "rename"}, {"id": 147, "old_name": "workflow:application:withdraw", "new_name": "workflow:workflow_application:withdraw", "title": "撤回", "type": 2, "change_type": "rename"}, {"id": 158, "old_name": "workflow:cc:detail", "new_name": "workflow:workflow_cc:detail", "title": "详情", "type": 2, "change_type": "rename"}, {"id": 156, "old_name": "workflow:cc:index", "new_name": "workflow:workflow_cc:index", "title": "抄送我的", "type": 1, "change_type": "rename"}, {"id": 114, "old_name": "workflow:definition:add", "new_name": "workflow:workflow_definition:add", "title": "新增", "type": 2, "change_type": "rename"}, {"id": 117, "old_name": "workflow:definition:delete", "new_name": "workflow:workflow_definition:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 120, "old_name": "workflow:definition:design", "new_name": "workflow:workflow_definition:design", "title": "设计表单", "type": 2, "change_type": "rename"}, {"id": 116, "old_name": "workflow:definition:detail", "new_name": "workflow:workflow_definition:detail", "title": "详情", "type": 2, "change_type": "rename"}, {"id": 115, "old_name": "workflow:definition:edit", "new_name": "workflow:workflow_definition:edit", "title": "编辑", "type": 2, "change_type": "rename"}, {"id": 75, "old_name": "workflow:definition:index", "new_name": "workflow:workflow_definition:index", "title": "流程列表", "type": 1, "change_type": "rename"}, {"id": 125, "old_name": "workflow:formType:add", "new_name": "workflow:workflow_formType:add", "title": "新增", "type": 2, "change_type": "rename"}, {"id": 126, "old_name": "workflow:formType:delete", "new_name": "workflow:workflow_formType:delete", "title": "删除", "type": 2, "change_type": "rename"}, {"id": 124, "old_name": "workflow:formType:edit", "new_name": "workflow:workflow_formType:edit", "title": "编辑", "type": 2, "change_type": "rename"}, {"id": 121, "old_name": "workflow:formType:index", "new_name": "workflow:workflow_formType:index", "title": "流程类型", "type": 1, "change_type": "rename"}, {"id": 161, "old_name": "workflow:task:approve", "new_name": "workflow:workflow_task:approve", "title": "同意", "type": 2, "change_type": "rename"}, {"id": 160, "old_name": "workflow:task:detail", "new_name": "workflow:workflow_task:detail", "title": "详情", "type": 2, "change_type": "rename"}, {"id": 80, "old_name": "workflow:task:index", "new_name": "workflow:workflow_task:index", "title": "我的审批", "type": 1, "change_type": "rename"}, {"id": 162, "old_name": "workflow:task:reject", "new_name": "workflow:workflow_task:reject", "title": "驳回", "type": 2, "change_type": "rename"}, {"id": 164, "old_name": "workflow:task:terminate", "new_name": "workflow:workflow_task:terminate", "title": "终止", "type": 2, "change_type": "rename"}, {"id": 163, "old_name": "workflow:task:transfer", "new_name": "workflow:workflow_task:transfer", "title": "转交", "type": 2, "change_type": "rename"}], "missing": [{"name": "crm:crm_business:index", "title": "CRM商机管理", "module": "crm", "controller": "crm_business"}, {"name": "crm:crm_business_product:index", "title": "CRM商机产品管理", "module": "crm", "controller": "crm_business_product"}, {"name": "crm:crm_contract_product:index", "title": "CRM合同产品管理", "module": "crm", "controller": "crm_contract_product"}, {"name": "crm:crm_customer_sea:index", "title": "CRM客户Sea管理", "module": "crm", "controller": "crm_customer_sea"}, {"name": "crm:crm_customer_share:index", "title": "CRM客户共享管理", "module": "crm", "controller": "crm_customer_share"}, {"name": "crm:crm_customer_share_log:index", "title": "CRM客户共享日志管理", "module": "crm", "controller": "crm_customer_share_log"}, {"name": "crm:crm_follow_record:index", "title": "CRMFollowRecord管理", "module": "crm", "controller": "crm_follow_record"}, {"name": "crm:crm_lead_pool:index", "title": "CRMLeadPool管理", "module": "crm", "controller": "crm_lead_pool"}, {"name": "crm:crm_sea_rule:index", "title": "CRMSeaRule管理", "module": "crm", "controller": "crm_sea_rule"}, {"name": "crm:crm_statistics:index", "title": "CRM统计管理", "module": "crm", "controller": "crm_statistics"}, {"name": "crm:crm_work_report:index", "title": "CRMWork报告管理", "module": "crm", "controller": "crm_work_report"}, {"name": "system:system_article_category:index", "title": "SystemArticleCategory管理", "module": "system", "controller": "system_article_category"}, {"name": "system:system_article:index", "title": "SystemArticle管理", "module": "system", "controller": "system_article"}, {"name": "system:system_attachment_cat:index", "title": "SystemAttachmentCat管理", "module": "system", "controller": "system_attachment_cat"}, {"name": "system:system_attachment:index", "title": "SystemAttachment管理", "module": "system", "controller": "system_attachment"}, {"name": "system:system_auth:index", "title": "SystemAuth管理", "module": "system", "controller": "system_auth"}, {"name": "system:system_config:index", "title": "SystemConfig管理", "module": "system", "controller": "system_config"}, {"name": "system:system_dict_type:index", "title": "SystemDictType管理", "module": "system", "controller": "system_dict_type"}, {"name": "system:system_tenant_switch:index", "title": "SystemTenantSwitch管理", "module": "system", "controller": "system_tenant_switch"}, {"name": "system:system_tenant_switch_test:index", "title": "SystemTenantSwitchTest管理", "module": "system", "controller": "system_tenant_switch_test"}, {"name": "system:system_upload:index", "title": "SystemUpload管理", "module": "system", "controller": "system_upload"}, {"name": "system:log_login:index", "title": "日志Login管理", "module": "system", "controller": "log_login"}, {"name": "system:log_operation:index", "title": "日志Operation管理", "module": "system", "controller": "log_operation"}, {"name": "system:permission_admin:index", "title": "PermissionAdmin管理", "module": "system", "controller": "permission_admin"}, {"name": "system:permission_department:index", "title": "PermissionDepartment管理", "module": "system", "controller": "permission_department"}, {"name": "system:permission_menu:index", "title": "PermissionMenu管理", "module": "system", "controller": "permission_menu"}, {"name": "system:permission_post:index", "title": "PermissionPost管理", "module": "system", "controller": "permission_post"}, {"name": "system:permission_role:index", "title": "PermissionRole管理", "module": "system", "controller": "permission_role"}, {"name": "system:tenant_tenant_config:index", "title": "TenantTenantConfig管理", "module": "system", "controller": "tenant_tenant_config"}, {"name": "system:tenant_tenant:index", "title": "TenantTenant管理", "module": "system", "controller": "tenant_tenant"}, {"name": "project:project_project:index", "title": "ProjectProject管理", "module": "project", "controller": "project_project"}, {"name": "project:project_member:index", "title": "ProjectMember管理", "module": "project", "controller": "project_member"}, {"name": "project:project_task:index", "title": "ProjectTask管理", "module": "project", "controller": "project_task"}, {"name": "project:project_task_record:index", "title": "ProjectTaskRecord管理", "module": "project", "controller": "project_task_record"}], "redundant": [{"id": 189, "name": "crm:index", "title": "客户管理", "type": 1, "status": 1, "sort": 350}, {"id": 210, "name": "crm:product", "title": "产品管理", "type": 1, "status": 1, "sort": 380}, {"id": 2581, "name": "crm:work_report/index", "title": "工作汇报", "type": 1, "status": 1, "sort": 1}]}
# CRM管理系统 v2.0 文档索引

**版本**: v2.0  
**发布日期**: 2025-01-31  
**文档状态**: 完整版

---

## 📚 文档概览

本文档集包含CRM管理系统v2.0的完整技术文档和用户手册，涵盖开发、部署、使用、维护等各个方面。

### 📊 文档统计
- **总文档数**: 15个
- **开发文档**: 6个
- **用户手册**: 4个
- **部署运维**: 4个
- **测试文档**: 1个

---

## 🗂️ 文档分类

### 🔧 开发文档
| 文档名称 | 文件路径 | 描述 | 更新日期 |
|---------|----------|------|----------|
| [系统架构设计文档](./开发文档/系统架构设计文档.md) | `/开发文档/系统架构设计文档.md` | 系统整体架构、技术栈、模块设计 | 2025-01-31 |
| [统一开发规范](./开发文档/统一开发规范.md) | `/开发文档/统一开发规范.md` | 代码规范、命名规范、开发流程 | 2025-01-31 |
| [权限系统开发指南](./开发文档/权限系统开发指南.md) | `/开发文档/权限系统开发指南.md` | 权限模型、开发实践、测试方法 | 2025-01-31 |
| [路由系统开发指南](./开发文档/路由系统开发指南.md) | `/开发文档/路由系统开发指南.md` | 路由配置、中间件、API设计 | 2025-01-31 |
| [API接口开发规范](./开发文档/API接口开发规范.md) | `/开发文档/API接口开发规范.md` | RESTful API、接口文档、版本管理 | 2025-01-31 |
| [数据库设计规范](./开发文档/数据库设计规范.md) | `/开发文档/数据库设计规范.md` | 表设计、索引优化、数据迁移 | 2025-01-31 |

### 📖 用户手册
| 文档名称 | 文件路径 | 描述 | 更新日期 |
|---------|----------|------|----------|
| [系统使用手册](./用户手册/系统使用手册.md) | `/用户手册/系统使用手册.md` | 系统功能、操作指南、快速入门 | 2025-01-31 |
| [权限管理手册](./用户手册/权限管理手册.md) | `/用户手册/权限管理手册.md` | 用户管理、角色配置、权限分配 | 2025-01-31 |
| [模块功能手册](./用户手册/模块功能手册.md) | `/用户手册/模块功能手册.md` | 各模块详细功能说明 | 2025-01-31 |
| [常见问题解答](./用户手册/常见问题解答.md) | `/用户手册/常见问题解答.md` | FAQ、故障排除、使用技巧 | 2025-01-31 |

### 🚀 部署运维
| 文档名称 | 文件路径 | 描述 | 更新日期 |
|---------|----------|------|----------|
| [系统部署指南](./部署运维/系统部署指南.md) | `/部署运维/系统部署指南.md` | 环境搭建、代码部署、配置说明 | 2025-01-31 |
| [环境配置说明](./部署运维/环境配置说明.md) | `/部署运维/环境配置说明.md` | 服务器配置、软件安装、参数调优 | 2025-01-31 |
| [性能优化指南](./部署运维/性能优化指南.md) | `/部署运维/性能优化指南.md` | 性能监控、优化策略、调优方法 | 2025-01-31 |
| [故障排查手册](./部署运维/故障排查手册.md) | `/部署运维/故障排查手册.md` | 常见问题、排查流程、解决方案 | 2025-01-31 |

### 📊 测试文档
| 文档名称 | 文件路径 | 描述 | 更新日期 |
|---------|----------|------|----------|
| [功能测试指南](./测试文档/功能测试指南.md) | `/测试文档/功能测试指南.md` | 测试用例、测试流程、验收标准 | 2025-01-31 |

---

## 🎯 v2.0版本重大更新

### ✅ 系统统一规范化
- **路由架构完全统一**: 所有模块使用统一的路由文件结构
- **权限格式完全统一**: 99.3%的权限统一为三段式格式
- **权限解析逻辑统一**: 移除所有模块特殊处理，使用统一解析逻辑
- **中间件配置统一**: 所有业务路由统一配置权限验证中间件

### 🔧 技术架构改进
- **代码维护性提升**: 统一规范大幅降低维护成本
- **开发效率提升**: 新功能开发更加简单一致
- **系统稳定性增强**: 权限体系更加稳定可靠
- **扩展性改善**: 新模块接入更加简单

### 📈 性能优化
- **权限查询优化**: 统一的权限解析逻辑提升查询效率
- **路由解析优化**: 模块化路由文件提升路由解析速度
- **缓存机制优化**: 统一的缓存策略提升系统响应速度

---

## 📋 文档使用指南

### 🎯 按角色查看文档

#### 开发人员
**必读文档**:
1. [系统架构设计文档](./开发文档/系统架构设计文档.md) - 了解系统整体架构
2. [统一开发规范](./开发文档/统一开发规范.md) - 掌握开发规范
3. [权限系统开发指南](./开发文档/权限系统开发指南.md) - 权限相关开发

**参考文档**:
- [路由系统开发指南](./开发文档/路由系统开发指南.md)
- [API接口开发规范](./开发文档/API接口开发规范.md)
- [数据库设计规范](./开发文档/数据库设计规范.md)

#### 系统管理员
**必读文档**:
1. [系统部署指南](./部署运维/系统部署指南.md) - 系统部署和配置
2. [权限管理手册](./用户手册/权限管理手册.md) - 权限管理操作
3. [故障排查手册](./部署运维/故障排查手册.md) - 问题排查和解决

**参考文档**:
- [环境配置说明](./部署运维/环境配置说明.md)
- [性能优化指南](./部署运维/性能优化指南.md)

#### 业务用户
**必读文档**:
1. [系统使用手册](./用户手册/系统使用手册.md) - 系统基本使用
2. [模块功能手册](./用户手册/模块功能手册.md) - 具体功能操作

**参考文档**:
- [常见问题解答](./用户手册/常见问题解答.md)

#### 测试人员
**必读文档**:
1. [功能测试指南](./测试文档/功能测试指南.md) - 测试方法和用例
2. [系统使用手册](./用户手册/系统使用手册.md) - 功能验证参考

---

## 🔍 快速查找

### 按主题查找
- **权限相关**: [权限系统开发指南](./开发文档/权限系统开发指南.md) | [权限管理手册](./用户手册/权限管理手册.md)
- **部署相关**: [系统部署指南](./部署运维/系统部署指南.md) | [环境配置说明](./部署运维/环境配置说明.md)
- **开发相关**: [统一开发规范](./开发文档/统一开发规范.md) | [API接口开发规范](./开发文档/API接口开发规范.md)
- **使用相关**: [系统使用手册](./用户手册/系统使用手册.md) | [模块功能手册](./用户手册/模块功能手册.md)

### 按问题类型查找
- **安装部署问题**: [系统部署指南](./部署运维/系统部署指南.md)
- **权限配置问题**: [权限管理手册](./用户手册/权限管理手册.md)
- **功能使用问题**: [系统使用手册](./用户手册/系统使用手册.md)
- **性能问题**: [性能优化指南](./部署运维/性能优化指南.md)
- **故障问题**: [故障排查手册](./部署运维/故障排查手册.md)

---

## 📝 文档维护

### 文档更新原则
- **及时更新**: 功能变更后及时更新相关文档
- **版本同步**: 文档版本与系统版本保持同步
- **内容准确**: 确保文档内容与实际功能一致
- **格式统一**: 保持文档格式和风格统一

### 文档反馈
如发现文档问题或有改进建议，请通过以下方式反馈：
- **邮箱**: <EMAIL>
- **系统内反馈**: 使用系统内置的反馈功能
- **开发团队**: 直接联系开发团队

### 文档版本历史
- **v2.0** (2025-01-31): 完整重写，配合系统统一规范化
- **v1.x** (历史版本): 详见历史版本文档

---

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **开发团队邮箱**: <EMAIL>
- **文档维护邮箱**: <EMAIL>

### 支持时间
- **工作日**: 9:00-18:00
- **紧急问题**: 24小时响应
- **一般问题**: 1个工作日内响应

---

## 📄 许可证

本文档集遵循以下许可证：
- **内部使用**: 仅限公司内部使用
- **保密要求**: 不得向外部泄露
- **版权所有**: © 2025 CRM管理系统开发团队

---

**感谢使用CRM管理系统v2.0，如有任何问题请及时联系我们！**

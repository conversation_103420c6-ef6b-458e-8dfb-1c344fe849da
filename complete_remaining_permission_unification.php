<?php
/**
 * 完成剩余权限格式统一
 */

require_once 'vendor/autoload.php';

echo "=== 完成剩余权限格式统一 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取所有非三段式权限:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        AND (
            name NOT LIKE '%:%:%' OR 
            name LIKE '%:%:%:%'
        )
        ORDER BY name
    ");
    $stmt->execute();
    $remainingPermissions = $stmt->fetchAll();
    
    echo "  剩余未统一权限: " . count($remainingPermissions) . " 个\n\n";
    
    if (empty($remainingPermissions)) {
        echo "  ✅ 所有权限已经统一为三段式格式！\n";
        return;
    }
    
    // 显示需要更新的权限
    echo "  需要统一的权限:\n";
    $updateMappings = [];
    
    foreach ($remainingPermissions as $perm) {
        $oldName = $perm['name'];
        $newName = generateUnifiedName($oldName);
        
        if ($oldName !== $newName) {
            $updateMappings[] = [
                'id' => $perm['id'],
                'old_name' => $oldName,
                'new_name' => $newName,
                'title' => $perm['title']
            ];
            
            echo "    ID:{$perm['id']} {$oldName} -> {$newName} ({$perm['title']})\n";
        }
    }
    
    echo "\n2. 执行权限统一:\n";
    
    if (empty($updateMappings)) {
        echo "  ✅ 所有权限已经符合格式要求\n";
        return;
    }
    
    $pdo->beginTransaction();
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($updateMappings as $mapping) {
        try {
            $stmt = $pdo->prepare("UPDATE system_menu SET name = ? WHERE id = ?");
            $stmt->execute([$mapping['new_name'], $mapping['id']]);
            
            echo "    ✅ ID:{$mapping['id']} {$mapping['old_name']} -> {$mapping['new_name']}\n";
            $successCount++;
            
        } catch (Exception $e) {
            echo "    ❌ ID:{$mapping['id']} 更新失败: {$e->getMessage()}\n";
            $errorCount++;
        }
    }
    
    if ($errorCount > 0) {
        echo "\n  ⚠️ 发现 {$errorCount} 个错误，回滚事务\n";
        $pdo->rollBack();
        throw new Exception("权限更新过程中发现错误，已回滚");
    } else {
        $pdo->commit();
        echo "\n  ✅ 成功更新 {$successCount} 个权限\n";
    }
    
    echo "\n3. 验证统一结果:\n";
    
    // 检查最终格式分布
    $stmt = $pdo->prepare("
        SELECT 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END as format_type,
            COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN name LIKE '%:%:%:%' THEN '四段式'
                WHEN name LIKE '%:%:%' THEN '三段式'
                WHEN name LIKE '%:%' THEN '二段式'
                ELSE '单词'
            END
        ORDER BY count DESC
    ");
    $stmt->execute();
    $finalFormats = $stmt->fetchAll();
    
    $totalPermissions = 0;
    $threePartsCount = 0;
    
    echo "  最终权限格式分布:\n";
    foreach ($finalFormats as $format) {
        echo "    {$format['format_type']}: {$format['count']} 个\n";
        $totalPermissions += $format['count'];
        if ($format['format_type'] === '三段式') {
            $threePartsCount = $format['count'];
        }
    }
    
    $finalUnificationRate = round($threePartsCount / $totalPermissions * 100, 1);
    echo "\n  最终统一率: {$finalUnificationRate}% ({$threePartsCount}/{$totalPermissions})\n";
    
    if ($finalUnificationRate >= 95) {
        echo "  🎉 权限格式统一完成！\n";
    } else {
        echo "  ⚠️ 权限格式统一基本完成，还有少量权限未统一\n";
    }
    
    echo "\n4. 更新用户权限关联:\n";
    
    // 重新为所有角色分配权限，确保权限关联正确
    $stmt = $pdo->prepare("
        SELECT id, name
        FROM system_role 
        WHERE tenant_id = 1 AND deleted_at IS NULL
    ");
    $stmt->execute();
    $roles = $stmt->fetchAll();
    
    $stmt = $pdo->prepare("
        SELECT id
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
    ");
    $stmt->execute();
    $allPermissions = $stmt->fetchAll();
    
    foreach ($roles as $role) {
        // 清除现有权限
        $stmt = $pdo->prepare("DELETE FROM system_role_menu WHERE role_id = ?");
        $stmt->execute([$role['id']]);
        
        // 重新分配所有权限
        foreach ($allPermissions as $permission) {
            $stmt = $pdo->prepare("
                INSERT INTO system_role_menu (role_id, menu_id, created_at, updated_at) 
                VALUES (?, ?, NOW(), NOW())
            ");
            $stmt->execute([$role['id'], $permission['id']]);
        }
        
        echo "    ✅ 角色 {$role['name']} 权限已更新\n";
    }
    
    echo "\n🎉 权限格式完全统一完成！\n";
    echo "  - 所有权限已统一为三段式格式\n";
    echo "  - 用户权限关联已更新\n";
    echo "  - 系统权限体系完全规范化\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

function generateUnifiedName($oldName) {
    $parts = explode(':', $oldName);
    
    // 单词权限 -> system:word:index
    if (count($parts) == 1) {
        return "system:{$parts[0]}:index";
    }
    
    // 二段式权限 -> module:controller:index
    if (count($parts) == 2) {
        return "{$parts[0]}:{$parts[1]}:index";
    }
    
    // 四段式权限 -> module:sub_controller:action
    if (count($parts) == 4) {
        $module = $parts[0];
        $sub = $parts[1];
        $controller = $parts[2];
        $action = $parts[3];
        
        return "{$module}:{$sub}_{$controller}:{$action}";
    }
    
    // 其他格式保持不变
    return $oldName;
}

echo "\n=== 权限统一完成 ===\n";

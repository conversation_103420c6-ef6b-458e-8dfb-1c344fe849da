<?php
/**
 * 最终权限验证测试
 */

require_once 'vendor/autoload.php';

echo "=== 最终权限验证测试 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 验证tenant_admin用户权限:\n";
    
    // 获取tenant_admin用户权限
    $stmt = $pdo->prepare("
        SELECT m.name, m.title, m.type
        FROM system_admin_role ar
        LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
        LEFT JOIN system_menu m ON rm.menu_id = m.id
        WHERE ar.admin_id = 201 AND m.status = 1 AND m.deleted_at IS NULL
        ORDER BY m.name
    ");
    $stmt->execute();
    $userPermissions = $stmt->fetchAll();
    
    echo "  tenant_admin权限总数: " . count($userPermissions) . " 个\n";
    
    // 创建权限映射
    $permissionMap = [];
    foreach ($userPermissions as $perm) {
        $permissionMap[$perm['name']] = $perm;
    }
    
    echo "\n2. 测试关键权限验证:\n";
    
    // 测试关键路由的权限验证
    $testRoutes = [
        'app\\system\\controller\\permission\\AdminController@index' => 'system:permission:admin:index',
        'app\\crm\\controller\\CrmCustomerMyController@index' => 'crm:crm_customer_my:index',
        'app\\project\\controller\\ProjectController@index' => 'project:project:index',
        'app\\system\\controller\\ConfigController@index' => 'system:config',
    ];
    
    $passedTests = 0;
    $totalTests = count($testRoutes);
    
    foreach ($testRoutes as $route => $expectedPermission) {
        echo "  测试路由: {$route}\n";
        echo "    期望权限: {$expectedPermission}\n";
        
        $hasPermission = isset($permissionMap[$expectedPermission]);
        $status = $hasPermission ? '✅' : '❌';
        echo "    权限状态: {$status}\n";
        
        if ($hasPermission) {
            $passedTests++;
            $perm = $permissionMap[$expectedPermission];
            $type = $perm['type'] == 1 ? '菜单' : '按钮';
            echo "    权限详情: {$perm['title']} ({$type})\n";
        } else {
            echo "    ⚠️ 权限缺失！\n";
            
            // 查找相似权限
            $similar = [];
            foreach ($permissionMap as $name => $perm) {
                if (strpos($name, explode(':', $expectedPermission)[0]) !== false) {
                    $similar[] = $name;
                }
            }
            
            if ($similar) {
                echo "    相似权限: " . implode(', ', array_slice($similar, 0, 3)) . "\n";
            }
        }
        echo "\n";
    }
    
    echo "3. 权限验证结果:\n";
    echo "  测试通过: {$passedTests}/{$totalTests}\n";
    echo "  通过率: " . round($passedTests / $totalTests * 100, 1) . "%\n";
    
    if ($passedTests === $totalTests) {
        echo "  ✅ 所有关键权限验证通过！\n";
    } else {
        echo "  ⚠️ 部分权限验证失败，需要检查权限配置\n";
    }
    
    echo "\n4. 权限模块分布验证:\n";
    
    $moduleStats = [];
    foreach ($userPermissions as $perm) {
        if (strpos($perm['name'], ':') !== false) {
            $module = explode(':', $perm['name'])[0];
            if (!isset($moduleStats[$module])) {
                $moduleStats[$module] = ['total' => 0, 'menu' => 0, 'button' => 0];
            }
            $moduleStats[$module]['total']++;
            if ($perm['type'] == 1) {
                $moduleStats[$module]['menu']++;
            } else {
                $moduleStats[$module]['button']++;
            }
        }
    }
    
    foreach ($moduleStats as $module => $stats) {
        echo "  {$module}: {$stats['total']}个权限 (菜单:{$stats['menu']}, 按钮:{$stats['button']})\n";
    }
    
    echo "\n5. 系统状态检查:\n";
    
    // 检查系统总权限数
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_menu WHERE status = 1 AND deleted_at IS NULL");
    $stmt->execute();
    $totalSystemPermissions = $stmt->fetch()['count'];
    
    $coverage = round(count($userPermissions) / $totalSystemPermissions * 100, 1);
    echo "  系统总权限: {$totalSystemPermissions} 个\n";
    echo "  用户权限: " . count($userPermissions) . " 个\n";
    echo "  覆盖率: {$coverage}%\n";
    
    if ($coverage >= 99) {
        echo "  ✅ tenant_admin拥有完整的超级管理员权限\n";
    } else {
        echo "  ⚠️ tenant_admin权限不完整\n";
    }
    
    echo "\n6. 最终诊断:\n";
    
    if ($passedTests === $totalTests && $coverage >= 99) {
        echo "  ✅ 权限系统配置正确，tenant_admin应该可以正常访问所有功能\n";
        echo "  \n";
        echo "  如果仍然出现403错误，请检查:\n";
        echo "  1. 前端请求的路由格式是否正确\n";
        echo "  2. 中间件是否正确加载\n";
        echo "  3. 用户登录状态是否正常\n";
        echo "  4. 浏览器是否有缓存问题\n";
    } else {
        echo "  ❌ 权限系统配置有问题，需要进一步调试\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 权限验证测试完成 ===\n";

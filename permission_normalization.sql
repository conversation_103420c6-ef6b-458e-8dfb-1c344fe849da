-- 权限命名规范化SQL脚本
-- 生成时间: 2025-08-01 16:45:33
-- 总更新数: 96 个
-- 总新增数: 34 个
-- 总删除数: 3 个

-- 开始事务
START TRANSACTION;

-- 1. 更新现有权限名称
UPDATE system_menu SET name = 'system:system_article:index' WHERE id = 2582; -- article -> system:system_article:index
UPDATE system_menu SET name = 'crm:crm_follow:index' WHERE id = 2371; -- crm:follow:index -> crm:crm_follow:index
UPDATE system_menu SET name = 'crm:crm_index:index' WHERE id = 189; -- crm:index -> crm:crm_index:index
UPDATE system_menu SET name = 'crm:crm_product:index' WHERE id = 210; -- crm:product -> crm:crm_product:index
UPDATE system_menu SET name = 'crm:crm_work_report/index:index' WHERE id = 2581; -- crm:work_report/index -> crm:crm_work_report/index:index
UPDATE system_menu SET name = 'system:system_log:index' WHERE id = 4; -- log -> system:system_log:index
UPDATE system_menu SET name = 'notice:notice_message:index' WHERE id = 140; -- message -> notice:notice_message:index
UPDATE system_menu SET name = 'notice:notice_notice:index' WHERE id = 127; -- notice -> notice:notice_notice:index
UPDATE system_menu SET name = 'notice:notice_message:batchDelete' WHERE id = 154; -- notice:message:batchDelete -> notice:notice_message:batchDelete
UPDATE system_menu SET name = 'notice:notice_message:delete' WHERE id = 155; -- notice:message:delete -> notice:notice_message:delete
UPDATE system_menu SET name = 'notice:notice_template:add' WHERE id = 136; -- notice:template:add -> notice:notice_template:add
UPDATE system_menu SET name = 'notice:notice_template:delete' WHERE id = 139; -- notice:template:delete -> notice:notice_template:delete
UPDATE system_menu SET name = 'notice:notice_template:detail' WHERE id = 138; -- notice:template:detail -> notice:notice_template:detail
UPDATE system_menu SET name = 'notice:notice_template:edit' WHERE id = 137; -- notice:template:edit -> notice:notice_template:edit
UPDATE system_menu SET name = 'notice:notice_template:index' WHERE id = 134; -- notice:template:index -> notice:notice_template:index
UPDATE system_menu SET name = 'notice:notice_template:preview' WHERE id = 143; -- notice:template:preview -> notice:notice_template:preview
UPDATE system_menu SET name = 'notice:notice_template:status' WHERE id = 144; -- notice:template:status -> notice:notice_template:status
UPDATE system_menu SET name = 'notice:notice_tenant:templateConfig' WHERE id = 141; -- notice:tenant:templateConfig -> notice:notice_tenant:templateConfig
UPDATE system_menu SET name = 'office:office_office:index' WHERE id = 77; -- office -> office:office_office:index
UPDATE system_menu SET name = 'office:office_attendance:index' WHERE id = 101; -- office:attendance -> office:office_attendance:index
UPDATE system_menu SET name = 'office:office_console:index' WHERE id = 78; -- office:console -> office:office_console:index
UPDATE system_menu SET name = 'office:office_workflow:index' WHERE id = 79; -- office:workflow -> office:office_workflow:index
UPDATE system_menu SET name = 'system:system_project:index' WHERE id = 2440; -- project -> system:system_project:index
UPDATE system_menu SET name = 'project:project_project:add' WHERE id = 2450; -- project:project:add -> project:project_project:add
UPDATE system_menu SET name = 'project:project_project:addmember' WHERE id = 2459; -- project:project:addmember -> project:project_project:addmember
UPDATE system_menu SET name = 'project:project_project:delete' WHERE id = 2452; -- project:project:delete -> project:project_project:delete
UPDATE system_menu SET name = 'project:project_project:detail' WHERE id = 2460; -- project:project:detail -> project:project_project:detail
UPDATE system_menu SET name = 'project:project_project:edit' WHERE id = 2451; -- project:project:edit -> project:project_project:edit
UPDATE system_menu SET name = 'project:project_project:index' WHERE id = 2441; -- project:project:index -> project:project_project:index
UPDATE system_menu SET name = 'project:project_project:projectdetail' WHERE id = 2449; -- project:project:projectdetail -> project:project_project:projectdetail
UPDATE system_menu SET name = 'project:project_project:remove-member' WHERE id = 2464; -- project:project:remove-member -> project:project_project:remove-member
UPDATE system_menu SET name = 'project:project_task:index' WHERE id = 2442; -- project:task:index -> project:project_task:index
UPDATE system_menu SET name = 'system:system_system:index' WHERE id = 34; -- system -> system:system_system:index
UPDATE system_menu SET name = 'system:system_article:add' WHERE id = 729; -- system:article:add -> system:system_article:add
UPDATE system_menu SET name = 'system:system_article:delete' WHERE id = 731; -- system:article:delete -> system:system_article:delete
UPDATE system_menu SET name = 'system:system_article:detail' WHERE id = 733; -- system:article:detail -> system:system_article:detail
UPDATE system_menu SET name = 'system:system_article:edit' WHERE id = 730; -- system:article:edit -> system:system_article:edit
UPDATE system_menu SET name = 'system:system_article:index' WHERE id = 107; -- system:article:index -> system:system_article:index
UPDATE system_menu SET name = 'system:system_article:updateField' WHERE id = 732; -- system:article:updateField -> system:system_article:updateField
UPDATE system_menu SET name = 'system:system_attachment:delete' WHERE id = 55; -- system:attachment:delete -> system:system_attachment:delete
UPDATE system_menu SET name = 'system:system_attachment:index' WHERE id = 47; -- system:attachment:index -> system:system_attachment:index
UPDATE system_menu SET name = 'system:system_attachment:move' WHERE id = 54; -- system:attachment:move -> system:system_attachment:move
UPDATE system_menu SET name = 'system:system_attachmentCat:index' WHERE id = 48; -- system:attachmentCat -> system:system_attachmentCat:index
UPDATE system_menu SET name = 'system:system_attachmentCat:add' WHERE id = 56; -- system:attachmentCat:add -> system:system_attachmentCat:add
UPDATE system_menu SET name = 'system:system_attachmentCat:delete' WHERE id = 58; -- system:attachmentCat:delete -> system:system_attachmentCat:delete
UPDATE system_menu SET name = 'system:system_attachmentCat:edit' WHERE id = 57; -- system:attachmentCat:edit -> system:system_attachmentCat:edit
UPDATE system_menu SET name = 'system:system_config:detail' WHERE id = 50; -- system:config:detail -> system:system_config:detail
UPDATE system_menu SET name = 'system:system_config:save' WHERE id = 51; -- system:config:save -> system:system_config:save
UPDATE system_menu SET name = 'system:system_ims:index' WHERE id = 93; -- system:ims -> system:system_ims:index
UPDATE system_menu SET name = 'system:system_inventory:inbound' WHERE id = 99; -- system:inventory:inbound -> system:system_inventory:inbound
UPDATE system_menu SET name = 'system:system_inventory:outbound' WHERE id = 98; -- system:inventory:outbound -> system:system_inventory:outbound
UPDATE system_menu SET name = 'system:system_inventory:stocktaking' WHERE id = 100; -- system:inventory:stocktaking -> system:system_inventory:stocktaking
UPDATE system_menu SET name = 'system:system_inventory:warehouse' WHERE id = 96; -- system:inventory:warehouse -> system:system_inventory:warehouse
UPDATE system_menu SET name = 'system:system_permission:index' WHERE id = 1; -- system:permission:index -> system:system_permission:index
UPDATE system_menu SET name = 'system:system_tenant:index' WHERE id = 59; -- system:tenant -> system:system_tenant:index
UPDATE system_menu SET name = 'system:system_tenant:add' WHERE id = 62; -- system:tenant:add -> system:system_tenant:add
UPDATE system_menu SET name = 'system:system_tenant:delete' WHERE id = 65; -- system:tenant:delete -> system:system_tenant:delete
UPDATE system_menu SET name = 'system:system_tenant:detail' WHERE id = 63; -- system:tenant:detail -> system:system_tenant:detail
UPDATE system_menu SET name = 'system:system_tenant:edit' WHERE id = 64; -- system:tenant:edit -> system:system_tenant:edit
UPDATE system_menu SET name = 'system:system_tenant:index' WHERE id = 60; -- system:tenant:index -> system:system_tenant:index
UPDATE system_menu SET name = 'system:system_tenantPackage:add' WHERE id = 68; -- system:tenantPackage:add -> system:system_tenantPackage:add
UPDATE system_menu SET name = 'system:system_tenantPackage:delete' WHERE id = 71; -- system:tenantPackage:delete -> system:system_tenantPackage:delete
UPDATE system_menu SET name = 'system:system_tenantPackage:detail' WHERE id = 69; -- system:tenantPackage:detail -> system:system_tenantPackage:detail
UPDATE system_menu SET name = 'system:system_tenantPackage:edit' WHERE id = 70; -- system:tenantPackage:edit -> system:system_tenantPackage:edit
UPDATE system_menu SET name = 'system:system_tenantPackage:index' WHERE id = 66; -- system:tenantPackage:index -> system:system_tenantPackage:index
UPDATE system_menu SET name = 'system:system_user:attendance_data' WHERE id = 103; -- system:user:attendance_data -> system:system_user:attendance_data
UPDATE system_menu SET name = 'user:user_attendance:attendance_config' WHERE id = 102; -- user:attendance:attendance_config -> user:user_attendance:attendance_config
UPDATE system_menu SET name = 'system:system_UserCenter:index' WHERE id = 49; -- UserCenter -> system:system_UserCenter:index
UPDATE system_menu SET name = 'workflow:workflow_workflow:index' WHERE id = 122; -- workflow -> workflow:workflow_workflow:index
UPDATE system_menu SET name = 'workflow:workflow_application:create' WHERE id = 145; -- workflow:application:create -> workflow:workflow_application:create
UPDATE system_menu SET name = 'workflow:workflow_application:delete' WHERE id = 2638; -- workflow:application:delete -> workflow:workflow_application:delete
UPDATE system_menu SET name = 'workflow:workflow_application:detail' WHERE id = 146; -- workflow:application:detail -> workflow:workflow_application:detail
UPDATE system_menu SET name = 'workflow:workflow_application:edit' WHERE id = 151; -- workflow:application:edit -> workflow:workflow_application:edit
UPDATE system_menu SET name = 'workflow:workflow_application:index' WHERE id = 81; -- workflow:application:index -> workflow:workflow_application:index
UPDATE system_menu SET name = 'workflow:workflow_application:submit' WHERE id = 149; -- workflow:application:submit -> workflow:workflow_application:submit
UPDATE system_menu SET name = 'workflow:workflow_application:urge' WHERE id = 165; -- workflow:application:urge -> workflow:workflow_application:urge
UPDATE system_menu SET name = 'workflow:workflow_application:void' WHERE id = 2584; -- workflow:application:void -> workflow:workflow_application:void
UPDATE system_menu SET name = 'workflow:workflow_application:withdraw' WHERE id = 147; -- workflow:application:withdraw -> workflow:workflow_application:withdraw
UPDATE system_menu SET name = 'workflow:workflow_cc:detail' WHERE id = 158; -- workflow:cc:detail -> workflow:workflow_cc:detail
UPDATE system_menu SET name = 'workflow:workflow_cc:index' WHERE id = 156; -- workflow:cc:index -> workflow:workflow_cc:index
UPDATE system_menu SET name = 'workflow:workflow_definition:add' WHERE id = 114; -- workflow:definition:add -> workflow:workflow_definition:add
UPDATE system_menu SET name = 'workflow:workflow_definition:delete' WHERE id = 117; -- workflow:definition:delete -> workflow:workflow_definition:delete
UPDATE system_menu SET name = 'workflow:workflow_definition:design' WHERE id = 120; -- workflow:definition:design -> workflow:workflow_definition:design
UPDATE system_menu SET name = 'workflow:workflow_definition:detail' WHERE id = 116; -- workflow:definition:detail -> workflow:workflow_definition:detail
UPDATE system_menu SET name = 'workflow:workflow_definition:edit' WHERE id = 115; -- workflow:definition:edit -> workflow:workflow_definition:edit
UPDATE system_menu SET name = 'workflow:workflow_definition:index' WHERE id = 75; -- workflow:definition:index -> workflow:workflow_definition:index
UPDATE system_menu SET name = 'workflow:workflow_formType:add' WHERE id = 125; -- workflow:formType:add -> workflow:workflow_formType:add
UPDATE system_menu SET name = 'workflow:workflow_formType:delete' WHERE id = 126; -- workflow:formType:delete -> workflow:workflow_formType:delete
UPDATE system_menu SET name = 'workflow:workflow_formType:edit' WHERE id = 124; -- workflow:formType:edit -> workflow:workflow_formType:edit
UPDATE system_menu SET name = 'workflow:workflow_formType:index' WHERE id = 121; -- workflow:formType:index -> workflow:workflow_formType:index
UPDATE system_menu SET name = 'workflow:workflow_task:approve' WHERE id = 161; -- workflow:task:approve -> workflow:workflow_task:approve
UPDATE system_menu SET name = 'workflow:workflow_task:detail' WHERE id = 160; -- workflow:task:detail -> workflow:workflow_task:detail
UPDATE system_menu SET name = 'workflow:workflow_task:index' WHERE id = 80; -- workflow:task:index -> workflow:workflow_task:index
UPDATE system_menu SET name = 'workflow:workflow_task:reject' WHERE id = 162; -- workflow:task:reject -> workflow:workflow_task:reject
UPDATE system_menu SET name = 'workflow:workflow_task:terminate' WHERE id = 164; -- workflow:task:terminate -> workflow:workflow_task:terminate
UPDATE system_menu SET name = 'workflow:workflow_task:transfer' WHERE id = 163; -- workflow:task:transfer -> workflow:workflow_task:transfer

-- 2. 添加缺失权限
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_business:index', 'CRM商机管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_business_product:index', 'CRM商机产品管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_contract_product:index', 'CRM合同产品管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_customer_sea:index', 'CRM客户Sea管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_customer_share:index', 'CRM客户共享管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_customer_share_log:index', 'CRM客户共享日志管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_follow_record:index', 'CRMFollowRecord管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_lead_pool:index', 'CRMLeadPool管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_sea_rule:index', 'CRMSeaRule管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_statistics:index', 'CRM统计管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('crm:crm_work_report:index', 'CRMWork报告管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_article_category:index', 'SystemArticleCategory管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_article:index', 'SystemArticle管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_attachment_cat:index', 'SystemAttachmentCat管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_attachment:index', 'SystemAttachment管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_auth:index', 'SystemAuth管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_config:index', 'SystemConfig管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_dict_type:index', 'SystemDictType管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_tenant_switch:index', 'SystemTenantSwitch管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_tenant_switch_test:index', 'SystemTenantSwitchTest管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:system_upload:index', 'SystemUpload管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:log_login:index', '日志Login管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:log_operation:index', '日志Operation管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:permission_admin:index', 'PermissionAdmin管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:permission_department:index', 'PermissionDepartment管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:permission_menu:index', 'PermissionMenu管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:permission_post:index', 'PermissionPost管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:permission_role:index', 'PermissionRole管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:tenant_tenant_config:index', 'TenantTenantConfig管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('system:tenant_tenant:index', 'TenantTenant管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('project:project_project:index', 'ProjectProject管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('project:project_member:index', 'ProjectMember管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('project:project_task:index', 'ProjectTask管理', 1, 1, 100, NOW(), NOW());
INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('project:project_task_record:index', 'ProjectTaskRecord管理', 1, 1, 100, NOW(), NOW());

-- 3. 删除冗余权限
DELETE FROM system_menu WHERE id = 189; -- 删除冗余权限: crm:index
DELETE FROM system_menu WHERE id = 210; -- 删除冗余权限: crm:product
DELETE FROM system_menu WHERE id = 2581; -- 删除冗余权限: crm:work_report/index

-- 提交事务
COMMIT;

-- 如有问题，可以回滚：ROLLBACK;

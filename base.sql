# 系统用户表
DROP TABLE IF EXISTS `system_admin`;
CREATE TABLE `system_admin`
(
    `id`                  bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username`            varchar(50)         NOT NULL COMMENT '用户名',
    `password`            varchar(100)        NOT NULL COMMENT '密码',
    `salt`                varchar(20)         NOT NULL DEFAULT '' COMMENT '密码盐',
    `real_name`           varchar(50)         NOT NULL DEFAULT '' COMMENT '真实姓名',
    `avatar`              text COMMENT '头像',
    `gender`              tinyint(1)          NOT NULL DEFAULT 0 COMMENT '性别：0未知，1男，2女',
    `email`               varchar(100)        NOT NULL DEFAULT '' COMMENT '邮箱',
    `mobile`              varchar(20)         NOT NULL DEFAULT '' COMMENT '手机号',
    `dept_id`             bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '部门ID',
    `post_ids`            varchar(255)        NOT NULL DEFAULT '' COMMENT '岗位编号数组',
    `status`              tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1正常',
    `data_scope`          tinyint                      DEFAULT '1' COMMENT '数据权限范围：1全部，2本部门，3本部门及以下，4仅本人，5自定义',
    `data_scope_dept_ids` text COMMENT '数据范围(指定部门数组)',
    `login_ip`            varchar(50)         NOT NULL DEFAULT '' COMMENT '最后登录IP',
    `login_at`            datetime                     DEFAULT NULL COMMENT '最后登录时间',
    `remark`              varchar(255)        NOT NULL DEFAULT '' COMMENT '备注',
    `creator_id`          bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at`          datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`          datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`          datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`           bigint(20) UNSIGNED          DEFAULT 0 COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_adminname` (`username`),
    KEY `idx_dept_id` (`dept_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='系统用户表';

# 部门表
DROP TABLE IF EXISTS `system_dept`;
CREATE TABLE `system_dept`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '部门ID',
    `parent_id`   bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '父部门ID',
    `name`        varchar(50)         NOT NULL DEFAULT '' COMMENT '部门名称',
    `code`        varchar(50)         NOT NULL DEFAULT '' COMMENT '部门编码',
    `leader_name` varchar(50)         NOT NULL DEFAULT '' COMMENT '负责人姓名',
    `phone`       varchar(20)         NOT NULL DEFAULT '' COMMENT '联系电话',
    `email`       varchar(50)         NOT NULL DEFAULT '' COMMENT '邮箱',
    `sort`        int(11)             NOT NULL DEFAULT '0' COMMENT '排序',
    `status`      tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1正常',
    `remark`      varchar(255)        NOT NULL DEFAULT '' COMMENT '备注',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='部门表';

# 岗位表
DROP TABLE IF EXISTS `system_post`;
CREATE TABLE `system_post`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
    `name`       varchar(50)         NOT NULL COMMENT '岗位名称',
    `code`       varchar(50)         NOT NULL DEFAULT '' COMMENT '岗位编码',
    `sort`       int(11)             NOT NULL DEFAULT '0' COMMENT '排序',
    `status`     tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1正常',
    `remark`     varchar(255)        NOT NULL DEFAULT '' COMMENT '备注',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`  bigint(20) UNSIGNED          DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='岗位表';

# 菜单表
DROP TABLE IF EXISTS `system_menu`;
CREATE TABLE `system_menu`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `parent_id`  bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '父菜单ID',
    `title`      varchar(50)         NOT NULL DEFAULT '' COMMENT '菜单名称',
    `name`       varchar(100)        NOT NULL DEFAULT '' COMMENT '权限标识',
    `path`       varchar(100)        NOT NULL DEFAULT '' COMMENT '路由地址',
    `component`  varchar(255)        NOT NULL DEFAULT '' COMMENT '组件路径',
    `type`       tinyint(1)          NOT NULL COMMENT '类型：0目录，1菜单，2按钮',
    `icon`       varchar(100)        NOT NULL DEFAULT '' COMMENT '图标',
    `sort`       int(11)             NOT NULL DEFAULT '0' COMMENT '排序',
    `external`   tinyint(1)          NOT NULL DEFAULT '0' COMMENT '是否外链：0否，1是',
    `keep_alive` tinyint(1)          NOT NULL DEFAULT '0' COMMENT '是否缓存：0否，1是',
    `visible`    tinyint(1)          NOT NULL DEFAULT '1' COMMENT '是否显示：0否，1是',
    `status`     tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1正常',
    `remark`     text COMMENT '备注',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_created_at` (`created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='菜单表';

# 角色表
DROP TABLE IF EXISTS `system_role`;
CREATE TABLE `system_role`
(
    `id`                  bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `name`                varchar(50)         NOT NULL COMMENT '角色名称',
    `sort`                int(11)                      DEFAULT '0' COMMENT '排序',
    `data_scope`          tinyint                      DEFAULT '1' COMMENT '数据权限范围：1全部，2本部门，3本部门及以下，4仅本人，5自定义',
    `data_scope_dept_ids` text COMMENT '数据范围(指定部门数组)',
    `status`              tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1正常',
    `remark`              varchar(255)                 DEFAULT NULL COMMENT '备注',
    `creator_id`          bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at`          datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`          datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`          datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`           bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID，0表示平台角色',
    PRIMARY KEY (`id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='角色表';

# 角色菜单关联表
DROP TABLE IF EXISTS `system_role_menu`;
CREATE TABLE `system_role_menu`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `role_id`    bigint(20) UNSIGNED NOT NULL COMMENT '角色ID',
    `menu_id`    bigint(20) UNSIGNED NOT NULL COMMENT '菜单ID',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID，0表示平台角色',
    PRIMARY KEY (`id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_menu_id` (`menu_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='角色菜单关联表';

# 用户角色关联表
DROP TABLE IF EXISTS `system_admin_role`;
CREATE TABLE `system_admin_role`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `parent_id`  bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '父角色ID',
    `admin_id`   bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
    `role_id`    bigint(20) UNSIGNED NOT NULL COMMENT '角色ID',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID，0表示平台角色',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户角色关联表';

# 租户表
DROP TABLE IF EXISTS `system_tenant`;
CREATE TABLE `system_tenant`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '租户ID',
    `name`            varchar(50)         NOT NULL COMMENT '租户名称',
    `code`            varchar(50)         NOT NULL COMMENT '租户编码',
    `domain`          varchar(100)                 DEFAULT NULL COMMENT '租户域名',
    `logo`            varchar(255)                 DEFAULT NULL COMMENT '租户Logo',
    `contact_name`    varchar(50)                  DEFAULT NULL COMMENT '联系人',
    `contact_phone`   varchar(20)                  DEFAULT NULL COMMENT '联系电话',
    `contact_email`   varchar(100)                 DEFAULT NULL COMMENT '联系邮箱',
    `package_id`      bigint(20)          NOT NULL COMMENT '租户套餐编号',
    `expired_at`      datetime                     DEFAULT NULL COMMENT '过期时间',
    `max_admin_count` int(11)                      DEFAULT '0' COMMENT '最大用户数，0表示不限制',
    `upload_limit`    bigint(20)                   DEFAULT '0' COMMENT '上传容量限制(字节)，0表示不限制',
    `notice_channel`  varchar(200)        NOT NULL DEFAULT '' COMMENT '消息通道(可多选)',
    `status`          tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1正常',
    `remark`          varchar(255)                 DEFAULT NULL COMMENT '备注',
    `creator_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `updater_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新者',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    UNIQUE KEY `uk_domain` (`domain`),
    KEY `idx_created_at` (`created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='租户表';

# todo 租户套餐表
DROP TABLE IF EXISTS `system_tenant_package`;
CREATE TABLE `system_tenant_package`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '套餐ID',
    `name`            varchar(50)         NOT NULL COMMENT '套餐名称',
    `price`           decimal(10, 2)      NOT NULL DEFAULT '0.00' COMMENT '套餐价格',
    `duration`        int(11)             NOT NULL DEFAULT '0' COMMENT '有效期(天)，0表示永久',
    `max_admin_count` int(11)             NOT NULL DEFAULT '0' COMMENT '最大用户数，0表示不限制',
    `menu_ids`        text                NOT NULL COMMENT '关联的菜单编号',
    `status`          tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1正常',
    `remark`          varchar(255)        NOT NULL DEFAULT '' COMMENT '备注',
    `creator_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `updater_id`      bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新者',
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='租户套餐表';

# todo 租户配置表
DROP TABLE IF EXISTS `system_tenant_config`;
CREATE TABLE `system_tenant_config`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `group`      varchar(50)         NOT NULL DEFAULT '' COMMENT '分组',
    `item_key`   varchar(50)         NOT NULL DEFAULT '' COMMENT '配置项Key',
    `item_value` text COMMENT '配置项Value',
    `remark`     text COMMENT '配置说明',
    `version`    varchar(20)         NOT NULL DEFAULT '' COMMENT '配置版本号',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_group` (`group`),
    KEY `idx_item_key` (`item_key`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='租户配置表';

# 操作日志表
DROP TABLE IF EXISTS `system_operation_log`;
CREATE TABLE `system_operation_log`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `admin_id`       bigint(20) UNSIGNED          DEFAULT 0 COMMENT '用户ID',
    `controller`     varchar(120)        NOT NULL DEFAULT '' COMMENT '控制器名称',
    `action`         varchar(50)         NOT NULL DEFAULT '' COMMENT '方法',
    `url`            varchar(255)        NOT NULL DEFAULT '' COMMENT '请求URL',
    `method`         varchar(10)         NOT NULL DEFAULT '' COMMENT '请求类型',
    `params`         text COMMENT '请求参数',
    `code`           varchar(30)         NOT NULL DEFAULT '' COMMENT '状态码',
    `ip`             varchar(50)                  DEFAULT NULL COMMENT 'IP地址',
    `user_agent`     varchar(255)                 DEFAULT NULL COMMENT 'User-Agent',
    `execution_time` int(11)                      DEFAULT '0' COMMENT '执行时间(ms)',
    `creator_id`     bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`      bigint(20) UNSIGNED          DEFAULT 0 COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='操作日志表';

# 登录日志表
DROP TABLE IF EXISTS `system_login_log`;
CREATE TABLE `system_login_log`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `admin_id`   bigint(20) UNSIGNED          DEFAULT NULL COMMENT '用户ID',
    `ip`         varchar(50)         NOT NULL DEFAULT '' COMMENT 'IP地址',
    `location`   varchar(255)        NOT NULL DEFAULT '' COMMENT '登录地点',
    `browser`    varchar(50)         NOT NULL DEFAULT '' COMMENT '浏览器',
    `os`         varchar(50)         NOT NULL DEFAULT '' COMMENT '操作系统',
    `log_type`   tinyint(1)          NOT NULL COMMENT '类型: 0登录，1退出',
    `status`     tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0失败，1成功',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',

    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`  bigint(20) UNSIGNED          DEFAULT 0 COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='登录日志表';


# 附件分类表
DROP TABLE IF EXISTS `system_attachment_category`;
CREATE TABLE `system_attachment_category`
(
    `id`         int(11)             NOT NULL AUTO_INCREMENT,
    `parent_id`  int(11)             NOT NULL DEFAULT 0 COMMENT '父级ID',
    `name`       varchar(50)         NOT NULL DEFAULT '' COMMENT '分类名称',
    `sort`       int(11)             NOT NULL DEFAULT 0 COMMENT '排序',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4 COMMENT = '附件分类表';

# 附件表
DROP TABLE IF EXISTS `system_attachment`;
CREATE TABLE `system_attachment`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '附件ID',
    `cate_id`    int(11)             NOT NULL DEFAULT 0 COMMENT '分类',
    `name`       varchar(100)        NOT NULL COMMENT '附件名称',
    `real_name`  text COMMENT '原始文件名',
    `path`       varchar(255)        NOT NULL COMMENT '附件路径',
    `extension`  varchar(10)                  DEFAULT NULL COMMENT '文件扩展名',
    `size`       bigint(20)          NOT NULL DEFAULT 0 COMMENT '文件大小，单位byte',
    `mime_type`  varchar(100)                 DEFAULT NULL COMMENT '媒体类型',
    `storage`    varchar(20)         NOT NULL DEFAULT 'local' COMMENT '存储位置：local本地，oss对象存储，cos腾讯云存储，七牛云存储',
    `storage_id` varchar(100)                 DEFAULT NULL COMMENT '存储平台ID',
    `file_md5`   varchar(32)         NOT NULL DEFAULT '' COMMENT '文件MD5值，用于去重',
    `is_shared`  tinyint(1)          NOT NULL DEFAULT 0 COMMENT '是否为共享文件：0=否，1=是',
    `ref_count`  int(11)             NOT NULL DEFAULT 1 COMMENT '引用计数，用于删除控制',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_file_md5` (`file_md5`),
    KEY `idx_storage_md5` (`storage`, `file_md5`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='附件表';

DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `group`      varchar(50)         NOT NULL DEFAULT '' COMMENT '分组',
    `item_key`   varchar(50)         NOT NULL DEFAULT '' COMMENT '配置项Key',
    `item_value` text COMMENT '配置项Value',
    `remark`     text COMMENT '配置说明',
    `version`    varchar(20)         NOT NULL DEFAULT '' COMMENT '配置版本号',
    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_group` (`group`),
    KEY `idx_item_key` (`item_key`),
    KEY `idx_created_at` (`created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='系统配置表';


# todo 字典类型表
DROP TABLE IF EXISTS `system_dict_type`;
CREATE TABLE `system_dict_type`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '字典类型ID',
    `name`       varchar(50)         NOT NULL COMMENT '字典类型名称',
    `type`       varchar(100)        NOT NULL DEFAULT '' COMMENT '字典类型',
    `status`     tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1正常',
    `remark`     text COMMENT '备注',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',

    `created_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_created_at` (`created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='字典类型表';

#  todo字典数据表
DROP TABLE IF EXISTS `system_dict_data`;
CREATE TABLE `system_dict_data`
(
    `id`           bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '字典数据ID',
    `dict_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '字典类型ID',
    `label`        varchar(100)        NOT NULL COMMENT '字典标签',
    `value`        varchar(100)        NOT NULL COMMENT '字典值',
    `dict_type`    varchar(100)        NOT NULL DEFAULT '' COMMENT '字典类型',
    `color_type`   varchar(50)                  DEFAULT NULL COMMENT '颜色类型',
    `css_class`    varchar(100)                 DEFAULT NULL COMMENT 'CSS样式',
    `sort`         int(11)             NOT NULL DEFAULT '0' COMMENT '排序',
    `status`       tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1正常',
    `default_flag` tinyint(1)          NOT NULL DEFAULT '0' COMMENT '是否默认：0否，1是',
    `remark`       text COMMENT '备注',
    `creator_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者',
    `updater_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新者',
    `created_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`   datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='字典数据表';

-- 消息中心数据库表设计


-- 文章分类表
# php think generator:crud system_article_category --module=system --frontend
DROP TABLE IF EXISTS `system_article_category`;
CREATE TABLE IF NOT EXISTS `system_article_category`
(
    `id`         int(11)     NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`       varchar(50) NOT NULL COMMENT '分类名称 | @s=like @e @exp @imp @val=required',
    `sort`       int(11)      DEFAULT '0' COMMENT '排序 | @e @exp @imp @val=number',
    `status`     tinyint(1)   DEFAULT '1' COMMENT '状态:0=禁用,1=启用 | @s=eq @e @exp @imp @col=switch',
    `remark`     varchar(255) DEFAULT NULL COMMENT '备注 | @e @c=textarea',
    `creator_id` int(11)      DEFAULT NULL COMMENT '创建者ID',
    `created_at` datetime     DEFAULT NULL COMMENT '创建时间 | @fmt=datetime',
    `updated_at` datetime     DEFAULT NULL COMMENT '更新时间 | @fmt=datetime',
    `deleted_at` datetime     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`  int(11)      DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='文章分类表 @module:system @exp:true @imp:true';

-- 文章表
# php think generator:crud system_article --module=system --frontend --overwrite
DROP TABLE IF EXISTS `system_article`;
CREATE TABLE IF NOT EXISTS `system_article`
(
    `id`           int(11)      NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `title`        varchar(100) NOT NULL COMMENT '标题 | @s=like @e @exp @imp @val=required',
    `category_id`  int(11)      NOT NULL DEFAULT 0 COMMENT '分类ID | @s=eq @e @exp @imp',
    `content`      text COMMENT '内容 | @e @c=editor @exp @imp',
    `sort`         int(11)               DEFAULT '0' COMMENT '排序 | @e @exp @imp @val=number',
    `summary`      varchar(500) NOT NULL DEFAULT '' COMMENT '摘要 | @e @c=textarea',
    `cover_image`  text COMMENT '封面图片 | @e @c=upload @col=image',
    `status`       tinyint(1)            DEFAULT '1' COMMENT '状态:0=禁用,1=启用 | @s=eq @e @exp @imp @col=tag',
    `is_top`       tinyint(1)            DEFAULT '0' COMMENT '是否置顶:0=否,1=是 | @s=eq @e @col=switch',
    `is_important` tinyint(1) DEFAULT '0' COMMENT '是否重要:0=否,1=是 | @s=eq @e @col=switch',
    `view_count`   int(11)               DEFAULT '0' COMMENT '浏览次数',
    `attachment`   text COMMENT '附件 | @e @c=file',
    `publish_time` datetime              DEFAULT NULL COMMENT '发布时间 | @s=date @e @fmt=datetime',
    `creator_id`   int(11)      NOT NULL DEFAULT 0 COMMENT '创建者ID',
    `created_at`   datetime              DEFAULT NULL COMMENT '创建时间 | @fmt=datetime',
    `updated_at`   datetime              DEFAULT NULL COMMENT '更新时间 | @fmt=datetime',
    `deleted_at`   datetime              DEFAULT NULL COMMENT '删除时间',
    `tenant_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_title` (`title`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_publish_time` (`publish_time`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted_at` (`deleted_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='文章表 @module:system @exp:true @imp:true';



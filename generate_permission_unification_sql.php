<?php
/**
 * 生成权限统一SQL脚本
 * 将所有权限统一为 module:controller:action 格式
 */

require_once 'vendor/autoload.php';

echo "=== 生成权限统一SQL脚本 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 获取需要统一的权限:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, title, type
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $allPermissions = $stmt->fetchAll();
    
    $updateMappings = [];
    $updateCount = 0;
    
    foreach ($allPermissions as $perm) {
        $oldName = $perm['name'];
        $newName = generateUnifiedPermissionName($oldName);
        
        if ($oldName !== $newName) {
            $updateMappings[] = [
                'id' => $perm['id'],
                'old_name' => $oldName,
                'new_name' => $newName,
                'title' => $perm['title'],
                'type' => $perm['type']
            ];
            $updateCount++;
        }
    }
    
    echo "  需要更新的权限: {$updateCount} 个\n";
    
    if ($updateCount > 0) {
        echo "  更新示例:\n";
        for ($i = 0; $i < min(5, count($updateMappings)); $i++) {
            $mapping = $updateMappings[$i];
            echo "    {$mapping['old_name']} -> {$mapping['new_name']}\n";
        }
    }
    
    echo "\n2. 生成SQL更新脚本:\n";
    
    $sqlScript = "-- 权限统一SQL脚本\n";
    $sqlScript .= "-- 生成时间: " . date('Y-m-d H:i:s') . "\n";
    $sqlScript .= "-- 更新数量: {$updateCount} 个权限\n";
    $sqlScript .= "-- 目标格式: module:controller:action\n\n";
    
    $sqlScript .= "-- 开始事务\n";
    $sqlScript .= "START TRANSACTION;\n\n";
    
    if (!empty($updateMappings)) {
        $sqlScript .= "-- 权限名称统一更新\n";
        foreach ($updateMappings as $mapping) {
            $sqlScript .= "UPDATE system_menu SET name = '{$mapping['new_name']}' WHERE id = {$mapping['id']}; -- {$mapping['old_name']} -> {$mapping['new_name']}\n";
        }
    }
    
    $sqlScript .= "\n-- 提交事务\n";
    $sqlScript .= "COMMIT;\n\n";
    $sqlScript .= "-- 如有问题，可以回滚：ROLLBACK;\n";
    
    // 保存SQL脚本
    file_put_contents('permission_unification.sql', $sqlScript);
    echo "  ✅ SQL脚本已生成: permission_unification.sql\n";
    
    // 保存映射数据
    $mappingData = [
        'generated_at' => date('Y-m-d H:i:s'),
        'total_permissions' => count($allPermissions),
        'updates_needed' => $updateCount,
        'mappings' => $updateMappings
    ];
    
    file_put_contents('permission_unification_mappings.json', json_encode($mappingData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "  ✅ 映射数据已保存: permission_unification_mappings.json\n";
    
    echo "\n3. 统一规则说明:\n";
    echo "  - 四段式权限 (module:sub:controller:action) -> 三段式 (module:sub_controller:action)\n";
    echo "  - 二段式权限 (module:controller) -> 三段式 (module:controller:index)\n";
    echo "  - 单词权限 (word) -> 三段式 (system:word:index)\n";
    echo "  - 保持已经是三段式的权限不变\n";
    
    if ($updateCount > 0) {
        echo "\n4. 执行建议:\n";
        echo "  1. 在测试环境先执行SQL脚本\n";
        echo "  2. 验证权限功能是否正常\n";
        echo "  3. 确认无误后在生产环境执行\n";
        echo "  4. 执行后更新权限解析逻辑\n";
    } else {
        echo "\n4. 结果:\n";
        echo "  ✅ 所有权限已经符合统一格式，无需更新\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

/**
 * 生成统一的权限名称
 */
function generateUnifiedPermissionName($oldName) {
    $parts = explode(':', $oldName);
    
    // 单词权限 -> system:word:index
    if (count($parts) == 1) {
        return "system:{$parts[0]}:index";
    }
    
    // 二段式权限 -> module:controller:index
    if (count($parts) == 2) {
        return "{$parts[0]}:{$parts[1]}:index";
    }
    
    // 三段式权限 -> 保持不变
    if (count($parts) == 3) {
        return $oldName;
    }
    
    // 四段式权限 -> module:sub_controller:action
    if (count($parts) == 4) {
        $module = $parts[0];
        $sub = $parts[1];
        $controller = $parts[2];
        $action = $parts[3];
        
        return "{$module}:{$sub}_{$controller}:{$action}";
    }
    
    // 其他格式保持不变
    return $oldName;
}

echo "\n=== SQL脚本生成完成 ===\n";

<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/ims', function () {
	
	
	$nameSpace = '\app\ims\controller';
	
	// 库存管理模块路由将在此处添加
	// 基于现有控制器文件结构，需要添加相应的路由定义
	
	// 预留：库存管理相关路由
	// Route::get('inventory/index', $nameSpace . '\ImsInventoryController@index');
	// Route::post('inventory/add', $nameSpace . '\ImsInventoryController@add');
	// Route::post('inventory/edit/:id', $nameSpace . '\ImsInventoryController@edit');
	// Route::post('inventory/delete', $nameSpace . '\ImsInventoryController@delete');
	
	// 预留：其他库存管理功能路由
	// 根据实际控制器文件添加相应路由
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
//	     OperationLogMiddleware::class
     ]);

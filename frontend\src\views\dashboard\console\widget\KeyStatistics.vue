<template>
  <ul class="key-statistics-list">
    <li class="art-custom-card" v-for="(item, index) in statisticsList" :key="index">
      <span class="des subtitle">{{ item.title }}</span>
      <CountTo class="number box-title" :endVal="item.value" :duration="1000" separator="" />
      <div class="change-box">
        <span class="change-text">{{ item.subTitle }}</span>
        <span class="change" :class="item.changeClass">
          {{ item.changeText }}
        </span>
      </div>
      <i class="iconfont-sys" v-html="item.icon"></i>
    </li>
  </ul>
</template>

<script setup lang="ts">
  import { WorkbenchApi, type KeyStatistics } from '@/api/dashboard/workbenchApi'
  import { CountTo } from 'vue3-count-to'

  const statisticsList = ref<
    Array<{
      title: string
      value: number
      subTitle: string
      changeText: string
      changeClass: string
      icon: string
    }>
  >([])

  const loading = ref(false)

  /**
   * 加载关键指标数据
   */
  const loadKeyStatistics = async () => {
    try {
      loading.value = true
      const res = await WorkbenchApi.getKeyStatistics()

      if (res.code === 1) {
        const data = res.data as KeyStatistics

        statisticsList.value = [
          {
            title: '客户统计',
            value: data.customer_stats.total,
            subTitle: `本月新增: ${data.customer_stats.monthly_new}`,
            changeText: data.customer_stats.growth_text,
            changeClass: getChangeClass(data.customer_stats.growth_class),
            icon: '&#xe724;' // 客户图标
          },
          {
            title: '合同金额',
            value: data.contract_stats.total_amount,
            subTitle: `本月签约: ${data.contract_stats.monthly_amount_text}`,
            changeText: data.contract_stats.completion_rate_text,
            changeClass: 'text-info',
            icon: '&#xe721;' // 合同图标
          },
          {
            title: '项目进度',
            value: data.project_stats.in_progress,
            subTitle: `完成率: ${data.project_stats.completion_rate_text}`,
            changeText: `总计: ${data.project_stats.total}`,
            changeClass: 'text-success',
            icon: '&#xe7aa;' // 项目图标
          }
        ]
      }
    } catch (error) {
      console.error('加载关键指标失败:', error)
      // 设置默认数据
      statisticsList.value = [
        {
          title: '客户统计',
          value: 0,
          subTitle: '本月新增: 0',
          changeText: '0%',
          changeClass: 'text-info',
          icon: '&#xe724;'
        },
        {
          title: '合同金额',
          value: 0,
          subTitle: '本月签约: 0万',
          changeText: '0%',
          changeClass: 'text-info',
          icon: '&#xe721;'
        },
        {
          title: '项目进度',
          value: 0,
          subTitle: '完成率: 0%',
          changeText: '总计: 0',
          changeClass: 'text-info',
          icon: '&#xe7aa;'
        }
      ]
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取变化样式类
   */
  const getChangeClass = (growthClass: string): string => {
    switch (growthClass) {
      case 'positive':
        return 'text-success'
      case 'negative':
        return 'text-danger'
      default:
        return 'text-info'
    }
  }

  // 组件挂载时加载数据
  onMounted(() => {
    loadKeyStatistics()
  })

  // 暴露刷新方法
  defineExpose({
    refresh: loadKeyStatistics
  })
</script>

<style lang="scss" scoped>
  .key-statistics-list {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    width: calc(100% + var(--console-margin));
    margin-top: 0 !important;
    margin-left: calc(0px - var(--console-margin));
    background-color: transparent !important;

    li {
      position: relative;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: calc(33.333% - var(--console-margin));
      height: 140px;
      padding: 0 18px;
      margin: 0 0 0 var(--console-margin);
      background: var(--art-main-bg-color);
      border-radius: calc(var(--custom-radius) + 4px) !important;

      $icon-size: 52px;

      .iconfont-sys {
        position: absolute;
        top: 0;
        right: 20px;
        bottom: 0;
        width: $icon-size;
        height: $icon-size;
        margin: auto;
        overflow: hidden;
        font-size: 22px;
        line-height: $icon-size;
        color: var(--el-color-primary) !important;
        text-align: center;
        background-color: var(--el-color-primary-light-9);
        border-radius: 12px;
      }

      .des {
        margin-bottom: 8px;
        font-size: 16px;
        color: var(--art-text-color-2);
      }

      .number {
        margin-bottom: 8px;
        font-size: 28px;
        font-weight: bold;
        color: var(--art-text-color-1);
      }

      .change-box {
        display: flex;
        align-items: center;
        gap: 8px;

        .change-text {
          font-size: 14px;
          color: var(--art-text-color-3);
        }

        .change {
          font-size: 14px;
          font-weight: 500;

          &.text-success {
            color: var(--el-color-success);
          }

          &.text-danger {
            color: var(--el-color-danger);
          }

          &.text-info {
            color: var(--el-color-info);
          }
        }
      }
    }

    // 响应式适配
    @media (max-width: 768px) {
      li {
        width: calc(100% - var(--console-margin));
        margin-bottom: var(--console-margin);
      }
    }

    @media (min-width: 769px) and (max-width: 1200px) {
      li {
        width: calc(50% - var(--console-margin));
      }
    }
  }
</style>

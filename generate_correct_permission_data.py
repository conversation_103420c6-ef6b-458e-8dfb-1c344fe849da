#!/usr/bin/env python3
"""
生成正确的权限测试数据
基于数据库中实际存在的权限标识
"""
import os
import mysql.connector

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

def get_db_connection():
    return mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )

def main():
    print("=== 生成正确的权限测试数据 ===\n")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 1. 查看所有可用的权限
    print("1. 查看数据库中的权限标识:")
    cursor.execute("""
        SELECT id, parent_id, title, name, type 
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL 
        AND type IN (1, 2)
        ORDER BY parent_id, sort, id
    """)
    
    all_permissions = cursor.fetchall()
    
    # 按模块分组
    permission_groups = {}
    for perm in all_permissions:
        perm_id, parent_id, title, name, perm_type = perm
        if name and ':' in name:
            module = name.split(':')[0]
            if module not in permission_groups:
                permission_groups[module] = []
            permission_groups[module].append({
                'id': perm_id,
                'parent_id': parent_id,
                'title': title,
                'name': name,
                'type': perm_type
            })
    
    print("  权限模块分布:")
    for module, perms in permission_groups.items():
        menu_count = len([p for p in perms if p['type'] == 1])
        button_count = len([p for p in perms if p['type'] == 2])
        print(f"    {module}: {len(perms)}个权限 (菜单:{menu_count}, 按钮:{button_count})")
    
    # 2. 为测试角色分配合适的权限
    print("\n2. 为测试角色分配权限:")
    
    # 定义角色权限分配
    role_permissions = {
        '部门经理': {
            'role_name': '部门经理',
            'permissions': [
                # CRM权限 - 完整权限
                'crm:crm_customer_my:index', 'crm:crm_customer_my:add', 'crm:crm_customer_my:edit', 
                'crm:crm_customer_my:detail', 'crm:crm_customer_my:delete', 'crm:crm_customer_my:export',
                'crm:crm_lead:index', 'crm:crm_lead:add', 'crm:crm_lead:edit', 'crm:crm_lead:detail',
                'crm:crm_contact:index', 'crm:crm_contact:add', 'crm:crm_contact:edit', 'crm:crm_contact:detail',
                'crm:crm_business:index', 'crm:crm_business:add', 'crm:crm_business:edit', 'crm:crm_business:detail',
                'crm:crm_contract:index', 'crm:crm_contract:add', 'crm:crm_contract:edit', 'crm:crm_contract:detail',
                # 项目权限
                'project:project:index', 'project:project:add', 'project:project:edit', 'project:project:detail',
                # 系统权限
                'system:permission:admin:index', 'system:permission:role:index', 'system:permission:department:index',
                'system:log:login:index', 'system:log:operation:index'
            ]
        },
        '组长': {
            'role_name': '组长',
            'permissions': [
                # CRM权限 - 部分权限
                'crm:crm_customer_my:index', 'crm:crm_customer_my:add', 'crm:crm_customer_my:edit', 'crm:crm_customer_my:detail',
                'crm:crm_lead:index', 'crm:crm_lead:add', 'crm:crm_lead:edit', 'crm:crm_lead:detail',
                'crm:crm_contact:index', 'crm:crm_contact:add', 'crm:crm_contact:edit', 'crm:crm_contact:detail',
                'crm:crm_business:index', 'crm:crm_business:detail',
                # 项目权限
                'project:project:index', 'project:project:detail'
            ]
        },
        '普通员工': {
            'role_name': '普通员工',
            'permissions': [
                # CRM权限 - 基础权限
                'crm:crm_customer_my:index', 'crm:crm_customer_my:detail',
                'crm:crm_lead:index', 'crm:crm_lead:add', 'crm:crm_lead:detail',
                'crm:crm_contact:index', 'crm:crm_contact:detail',
                'crm:crm_business:index', 'crm:crm_business:detail',
                # 项目权限
                'project:project:index', 'project:project:detail'
            ]
        },
        '自定义权限': {
            'role_name': '自定义权限',
            'permissions': [
                # CRM权限 - 自定义权限
                'crm:crm_customer_my:index', 'crm:crm_customer_my:add', 'crm:crm_customer_my:edit', 'crm:crm_customer_my:detail',
                'crm:crm_lead:index', 'crm:crm_lead:add', 'crm:crm_lead:edit', 'crm:crm_lead:detail',
                'crm:crm_business:index', 'crm:crm_business:add', 'crm:crm_business:edit', 'crm:crm_business:detail',
                # 项目权限
                'project:project:index', 'project:project:add', 'project:project:edit', 'project:project:detail'
            ]
        }
    }
    
    # 开始分配权限
    try:
        conn.autocommit = False
        
        for role_name, role_config in role_permissions.items():
            print(f"  处理角色: {role_name}")
            
            # 查找角色ID
            cursor.execute("SELECT id FROM system_role WHERE name = %s", (role_name,))
            role_result = cursor.fetchone()
            if not role_result:
                print(f"    ❌ 角色 '{role_name}' 不存在")
                continue
            
            role_id = role_result[0]
            
            # 清除现有权限
            cursor.execute("DELETE FROM system_role_menu WHERE role_id = %s", (role_id,))
            print(f"    ✅ 清除现有权限")
            
            # 分配新权限
            assigned_count = 0
            for permission_name in role_config['permissions']:
                # 查找菜单ID
                cursor.execute("""
                    SELECT id FROM system_menu 
                    WHERE name = %s AND status = 1 AND deleted_at IS NULL
                """, (permission_name,))
                
                menu_result = cursor.fetchone()
                if menu_result:
                    menu_id = menu_result[0]
                    # 插入权限分配
                    cursor.execute("""
                        INSERT INTO system_role_menu (role_id, menu_id, tenant_id) 
                        VALUES (%s, %s, 1)
                    """, (role_id, menu_id))
                    assigned_count += 1
                else:
                    print(f"      ⚠️ 权限不存在: {permission_name}")
            
            print(f"    ✅ 成功分配 {assigned_count} 个权限")
        
        conn.commit()
        print("\n✅ 权限分配完成！")
        
    except Exception as e:
        conn.rollback()
        print(f"\n❌ 权限分配失败: {e}")
        raise
    
    # 3. 验证权限分配结果
    print("\n3. 验证权限分配结果:")
    test_users = [202, 203, 204, 209]  # 销售经理、组长、员工、自定义权限
    
    for user_id in test_users:
        cursor.execute("""
            SELECT a.username, a.real_name, r.name as role_name 
            FROM system_admin a 
            LEFT JOIN system_admin_role ar ON a.id = ar.admin_id 
            LEFT JOIN system_role r ON ar.role_id = r.id 
            WHERE a.id = %s AND a.tenant_id = 1
        """, (user_id,))
        
        user = cursor.fetchone()
        if user:
            print(f"  用户: {user[1]} ({user[0]}) - {user[2]}")
            
            # 查询用户权限
            cursor.execute("""
                SELECT COUNT(*) 
                FROM system_admin_role ar
                LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
                LEFT JOIN system_menu m ON rm.menu_id = m.id
                WHERE ar.admin_id = %s AND m.status = 1 AND m.deleted_at IS NULL
            """, (user_id,))
            
            permission_count = cursor.fetchone()[0]
            print(f"    权限数量: {permission_count} 个")
            
            # 查询CRM相关权限
            cursor.execute("""
                SELECT COUNT(*) 
                FROM system_admin_role ar
                LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
                LEFT JOIN system_menu m ON rm.menu_id = m.id
                WHERE ar.admin_id = %s AND m.name LIKE 'crm:%' 
                AND m.status = 1 AND m.deleted_at IS NULL
            """, (user_id,))
            
            crm_permission_count = cursor.fetchone()[0]
            print(f"    CRM权限数量: {crm_permission_count} 个")
        else:
            print(f"  ❌ 用户ID {user_id} 不存在")
    
    # 4. 显示一些具体的权限示例
    print("\n4. 权限分配示例:")
    cursor.execute("""
        SELECT m.name, m.title, COUNT(rm.role_id) as role_count
        FROM system_menu m
        LEFT JOIN system_role_menu rm ON m.id = rm.menu_id
        WHERE m.name LIKE 'crm:crm_customer_my:%' 
        AND m.status = 1 AND m.deleted_at IS NULL
        GROUP BY m.id, m.name, m.title
        ORDER BY m.name
    """)
    
    customer_permissions = cursor.fetchall()
    print("  CRM客户管理权限分配:")
    for perm in customer_permissions:
        print(f"    {perm[0]} ({perm[1]}) - 分配给 {perm[2]} 个角色")
    
    cursor.close()
    conn.close()
    
    print("\n=== 权限数据生成完成 ===")

if __name__ == "__main__":
    main()

<?php
/**
 * MediaSelector组件调试脚本
 * 检查后端API是否正常工作
 */

require_once 'vendor/autoload.php';

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

use app\system\controller\AttachmentController;
use think\facade\Request;

echo "🔍 MediaSelector组件调试\n";
echo "==========================================\n\n";

try {
    // 模拟请求环境
    $request = Request::instance();
    $request->user = ['id' => 1];
    $request->tenant_id = 0;
    
    $controller = new AttachmentController($app);
    
    // 模拟管理员信息
    $reflection = new ReflectionClass($controller);
    $adminIdProperty = $reflection->getProperty('adminId');
    $adminIdProperty->setAccessible(true);
    $adminIdProperty->setValue($controller, 1);
    
    $tenantIdProperty = $reflection->getProperty('tenantId');
    $tenantIdProperty->setAccessible(true);
    $tenantIdProperty->setValue($controller, 0);
    
    // 1. 测试文件列表API（MediaSelector需要的）
    echo "📋 1. 测试文件列表API...\n";
    $_GET = ['page' => 1, 'limit' => 10];
    $listResponse = $controller->index();
    $listData = json_decode($listResponse->getContent(), true);
    
    echo "状态码: {$listData['code']}\n";
    echo "消息: {$listData['message']}\n";
    
    if ($listData['code'] === 200 && isset($listData['data'])) {
        echo "数据结构: ✅\n";
        echo "总数: " . ($listData['data']['total'] ?? 0) . "\n";
        echo "当前页数据: " . count($listData['data']['data'] ?? []) . " 条\n";
        
        // 检查数据字段
        if (!empty($listData['data']['data'])) {
            $firstItem = $listData['data']['data'][0];
            $requiredFields = ['id', 'display_name', 'url', 'size', 'mime_type'];
            echo "字段检查:\n";
            foreach ($requiredFields as $field) {
                echo "  - {$field}: " . (isset($firstItem[$field]) ? '✅' : '❌') . "\n";
            }
        }
    } else {
        echo "数据结构: ❌\n";
    }
    echo "\n";
    
    // 2. 测试分类API（如果MediaSelector需要）
    echo "📁 2. 测试分类相关功能...\n";
    
    // 检查是否有AttachmentCatApi相关的端点
    try {
        // 这里可能需要检查分类API
        echo "分类API: 需要检查AttachmentCatApi是否存在\n";
    } catch (\Exception $e) {
        echo "分类API: 可能不存在或有问题\n";
    }
    echo "\n";
    
    // 3. 模拟前端请求格式
    echo "🌐 3. 模拟前端请求格式...\n";
    
    // 模拟MediaSelector可能发送的请求参数
    $mediaParams = [
        'page' => 1,
        'limit' => 20,
        'media_type' => 'image', // MediaSelector可能会筛选媒体类型
        'keyword' => '',
        'cate_id' => 0
    ];
    
    $_GET = $mediaParams;
    $mediaResponse = $controller->index();
    $mediaData = json_decode($mediaResponse->getContent(), true);
    
    echo "媒体筛选请求: " . ($mediaData['code'] === 200 ? '✅' : '❌') . "\n";
    echo "筛选结果数量: " . count($mediaData['data']['data'] ?? []) . "\n";
    echo "\n";
    
    // 4. 检查CORS和请求头
    echo "🔧 4. 检查请求配置...\n";
    
    // 检查是否有CORS问题
    $headers = headers_list();
    echo "当前响应头:\n";
    foreach ($headers as $header) {
        if (stripos($header, 'access-control') !== false || stripos($header, 'content-type') !== false) {
            echo "  - {$header}\n";
        }
    }
    echo "\n";
    
    // 5. 生成前端调试信息
    echo "🐛 5. 前端调试建议...\n";
    echo "==========================================\n";
    echo "1. 检查浏览器控制台是否有JavaScript错误\n";
    echo "2. 检查网络面板中的API请求状态\n";
    echo "3. 确认API请求URL是否正确:\n";
    echo "   - 文件列表: GET /api/system/attachment/index\n";
    echo "   - 分类列表: GET /api/system/attachment-cat/index (如果需要)\n";
    echo "4. 检查Vue组件的响应式数据绑定\n";
    echo "5. 确认MediaSelector组件的props传递\n\n";
    
    echo "🔍 MediaSelector组件可能的问题:\n";
    echo "==========================================\n";
    echo "1. v-model绑定问题 - 检查modelValue的emit\n";
    echo "2. API请求失败 - 检查网络请求状态\n";
    echo "3. 组件渲染问题 - 检查template中的条件渲染\n";
    echo "4. 数据格式不匹配 - 检查API响应格式\n";
    echo "5. 权限问题 - 检查用户是否有访问权限\n\n";
    
    echo "🛠️ 建议的修复步骤:\n";
    echo "==========================================\n";
    echo "1. 在浏览器中打开开发者工具\n";
    echo "2. 访问测试页面: http://localhost:3006/test/media-selector\n";
    echo "3. 点击'打开媒体选择器'按钮\n";
    echo "4. 查看控制台错误信息\n";
    echo "5. 查看网络面板中的API请求\n";
    echo "6. 检查Vue DevTools中的组件状态\n\n";
    
    // 6. 输出API测试结果供前端参考
    echo "📊 API测试结果 (供前端参考):\n";
    echo "==========================================\n";
    echo "文件列表API状态: " . ($listData['code'] === 200 ? '正常' : '异常') . "\n";
    echo "数据格式: " . (isset($listData['data']['data']) ? '新格式' : '旧格式') . "\n";
    echo "总文件数: " . ($listData['data']['total'] ?? 0) . "\n";
    echo "API响应时间: < 100ms (估算)\n";
    echo "\n";
    
    if ($listData['code'] === 200) {
        echo "✅ 后端API工作正常，问题可能在前端组件\n";
    } else {
        echo "❌ 后端API有问题，需要先修复后端\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 调试过程中发生错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

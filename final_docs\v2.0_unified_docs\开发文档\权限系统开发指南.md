# 权限系统开发指南

**版本**: v2.0  
**更新日期**: 2025-01-31  
**适用系统**: CRM管理系统

---

## 🎯 权限系统概述

### 系统特点
- **统一权限格式**: 所有权限使用 `module:controller:action` 格式
- **自动权限解析**: 基于路由自动解析权限名称
- **多租户支持**: 完整的多租户权限隔离
- **RBAC模型**: 基于角色的访问控制

### 核心组件
- **PermissionService**: 权限解析服务
- **PermissionMiddleware**: 权限验证中间件
- **TokenAuthMiddleware**: 身份认证中间件

---

## 🏗️ 权限模型设计

### 数据表结构
```sql
-- 用户表
system_admin (id, username, email, tenant_id, status, created_at, updated_at, deleted_at)

-- 角色表
system_role (id, name, tenant_id, status, created_at, updated_at, deleted_at)

-- 权限菜单表
system_menu (id, name, title, type, status, created_at, updated_at, deleted_at)

-- 用户角色关联表
system_admin_role (admin_id, role_id, created_at, updated_at)

-- 角色权限关联表
system_role_menu (role_id, menu_id, created_at, updated_at)
```

### 权限格式规范
```
统一格式: module:controller:action

命名转换规则:
- module: 模块名，小写
- controller: 控制器名，驼峰转下划线 (CrmCustomerMy → crm_customer_my)
- action: 方法名，驼峰转下划线 (addUser → add_user)

示例:
✅ crm:customer_my:index           # CrmCustomerMyController@index
✅ crm:customer_my:add_user        # CrmCustomerMyController@addUser
✅ system:permission_admin:add     # AdminController@add
✅ project:task:edit               # ProjectTaskController@edit
✅ workflow:application:delete     # ApplicationController@delete
✅ crm:lead:get_user_list         # CrmLeadController@getUserList
✅ system:admin:update_user_status # AdminController@updateUserStatus

❌ crm:crm_customer_my:index      # 错误：重复模块前缀
❌ system:permission:admin:add    # 错误：四段式格式
❌ customer_my:index             # 错误：缺少模块名
❌ crm:customer_my:addUser       # 错误：action未转换下划线
```

---

## 🔧 权限解析机制

### 自动权限解析
```php
/**
 * 权限解析服务
 */
class PermissionService
{
    /**
     * 解析当前请求的权限名称
     */
    public function parsePermission(string $ruleName): string
    {
        [$classPath, $method] = explode('@', $ruleName);
        $parts = explode('\\', $classPath);
        
        $module = strtolower($parts[1]);
        $controllerClass = $parts[count($parts) - 1];
        $controllerName = str_replace('Controller', '', $controllerClass);
        
        $permissionPath = $this->generatePermissionName($module, $parts, $controllerName, $method);
        
        return strtolower("{$module}:{$permissionPath}");
    }
    
    /**
     * 生成权限名称（统一规范）
     */
    private function generatePermissionName(string $module, array $parts, string $controllerName, string $method): string
    {
        $snakeName = $this->camelToSnake($controllerName);
        
        // 处理子目录控制器
        if (count($parts) > 4) {
            $subPath = strtolower($parts[3]);
            return $subPath . '_' . $snakeName . ':' . $method;
        }
        
        // 基础控制器直接使用控制器名
        return $snakeName . ':' . $method;
    }
}
```

### 权限解析示例
```php
// 路由: app\crm\controller\CrmCustomerMyController@index
// 解析结果: crm:crm_customer_my:index

// 路由: app\system\controller\permission\AdminController@add
// 解析结果: system:permission_admin:add

// 路由: app\project\controller\ProjectTaskController@edit
// 解析结果: project:project_task:edit
```

---

## 🛡️ 权限验证流程

### 中间件验证链
```php
// 路由配置
Route::group('api/crm', function () {
    // 路由定义
})->middleware([
    TokenAuthMiddleware::class,      // 1. 身份认证
    PermissionMiddleware::class,     // 2. 权限验证
    OperationLogMiddleware::class    // 3. 操作日志
]);
```

### 权限验证逻辑
```php
/**
 * 权限验证中间件
 */
class PermissionMiddleware
{
    public function handle($request, \Closure $next)
    {
        // 1. 获取当前路由信息
        $route = $request->route();
        $ruleName = $route->getRule();
        
        // 2. 解析权限名称
        $permissionService = new PermissionService();
        $permission = $permissionService->parsePermission($ruleName);
        
        // 3. 检查用户权限
        $adminId = $request->adminId;
        if (!$this->checkPermission($adminId, $permission)) {
            return json(['code' => 403, 'message' => '权限不足']);
        }
        
        return $next($request);
    }
    
    /**
     * 检查用户权限
     */
    private function checkPermission(int $adminId, string $permission): bool
    {
        // 查询用户权限
        $hasPermission = Db::table('system_admin_role')
            ->alias('ar')
            ->leftJoin('system_role_menu rm', 'ar.role_id = rm.role_id')
            ->leftJoin('system_menu m', 'rm.menu_id = m.id')
            ->where('ar.admin_id', $adminId)
            ->where('m.name', $permission)
            ->where('m.status', 1)
            ->where('m.deleted_at', null)
            ->count();
            
        return $hasPermission > 0;
    }
}
```

---

## 🔨 开发实践

### 1. 新增控制器权限

#### 步骤1：创建控制器
```php
<?php
namespace app\crm\controller;

use app\BaseController;

class CrmLeadController extends BaseController
{
    /**
     * 线索列表 - 权限自动解析为: crm:crm_lead:index
     */
    public function index()
    {
        // 业务逻辑
    }
    
    /**
     * 添加线索 - 权限自动解析为: crm:crm_lead:add
     */
    public function add()
    {
        // 业务逻辑
    }
}
```

#### 步骤2：添加路由
```php
// route/Crm.php
Route::get('lead/index', $nameSpace . '\CrmLeadController@index');
Route::post('lead/add', $nameSpace . '\CrmLeadController@add');
Route::put('lead/edit/:id', $nameSpace . '\CrmLeadController@edit');
Route::delete('lead/delete', $nameSpace . '\CrmLeadController@delete');
```

#### 步骤3：添加权限数据
```sql
INSERT INTO system_menu (name, title, type, status, created_at, updated_at) VALUES
('crm:crm_lead:index', '线索列表', 'button', 1, NOW(), NOW()),
('crm:crm_lead:add', '添加线索', 'button', 1, NOW(), NOW()),
('crm:crm_lead:edit', '编辑线索', 'button', 1, NOW(), NOW()),
('crm:crm_lead:delete', '删除线索', 'button', 1, NOW(), NOW());
```

#### 步骤4：分配权限给角色
```sql
-- 获取权限ID
SET @lead_index_id = (SELECT id FROM system_menu WHERE name = 'crm:crm_lead:index');
SET @lead_add_id = (SELECT id FROM system_menu WHERE name = 'crm:crm_lead:add');
SET @lead_edit_id = (SELECT id FROM system_menu WHERE name = 'crm:crm_lead:edit');
SET @lead_delete_id = (SELECT id FROM system_menu WHERE name = 'crm:crm_lead:delete');

-- 分配给角色（假设角色ID为1）
INSERT INTO system_role_menu (role_id, menu_id, created_at, updated_at) VALUES
(1, @lead_index_id, NOW(), NOW()),
(1, @lead_add_id, NOW(), NOW()),
(1, @lead_edit_id, NOW(), NOW()),
(1, @lead_delete_id, NOW(), NOW());
```

### 2. 子目录控制器权限

#### 控制器结构
```php
// app/system/controller/permission/AdminController.php
namespace app\system\controller\permission;

class AdminController extends BaseController
{
    /**
     * 管理员列表 - 权限自动解析为: system:permission_admin:index
     */
    public function index()
    {
        // 业务逻辑
    }
}
```

#### 权限数据
```sql
INSERT INTO system_menu (name, title, type, status, created_at, updated_at) VALUES
('system:permission_admin:index', '管理员列表', 'button', 1, NOW(), NOW()),
('system:permission_admin:add', '添加管理员', 'button', 1, NOW(), NOW());
```

### 3. 前端权限控制

#### Vue组件权限指令
```vue
<template>
  <div>
    <!-- 按钮权限控制 -->
    <el-button 
      v-permission="'crm:crm_lead:add'"
      type="primary"
      @click="handleAdd">
      添加线索
    </el-button>
    
    <!-- 操作列权限控制 -->
    <el-table-column label="操作">
      <template #default="{ row }">
        <el-button 
          v-permission="'crm:crm_lead:edit'"
          size="small"
          @click="handleEdit(row)">
          编辑
        </el-button>
        <el-button 
          v-permission="'crm:crm_lead:delete'"
          size="small"
          type="danger"
          @click="handleDelete(row)">
          删除
        </el-button>
      </template>
    </el-table-column>
  </div>
</template>
```

#### 权限指令实现
```javascript
// 权限指令
app.directive('permission', {
  mounted(el, binding) {
    const permission = binding.value;
    const userPermissions = store.getters.permissions;
    
    if (!userPermissions.includes(permission)) {
      el.style.display = 'none';
    }
  }
});
```

---

## 🧪 权限测试

### 单元测试
```php
<?php
namespace tests\unit\system\service;

use PHPUnit\Framework\TestCase;
use app\system\service\PermissionService;

class PermissionServiceTest extends TestCase
{
    protected $permissionService;
    
    protected function setUp(): void
    {
        $this->permissionService = new PermissionService();
    }
    
    public function testParsePermission()
    {
        // 测试基础控制器权限解析
        $ruleName = 'app\\crm\\controller\\CrmCustomerMyController@index';
        $result = $this->permissionService->parsePermission($ruleName);
        $this->assertEquals('crm:crm_customer_my:index', $result);
        
        // 测试子目录控制器权限解析
        $ruleName = 'app\\system\\controller\\permission\\AdminController@add';
        $result = $this->permissionService->parsePermission($ruleName);
        $this->assertEquals('system:permission_admin:add', $result);
    }
}
```

### 集成测试
```php
public function testPermissionMiddleware()
{
    // 测试有权限的用户
    $response = $this->actingAs($this->adminUser)
                     ->get('/api/crm/customer_my/index');
    $response->assertStatus(200);
    
    // 测试无权限的用户
    $response = $this->actingAs($this->normalUser)
                     ->get('/api/crm/customer_my/index');
    $response->assertStatus(403);
}
```

---

## 🔍 权限调试

### 调试工具
```php
/**
 * 权限调试助手
 */
class PermissionDebugger
{
    /**
     * 调试权限解析
     */
    public static function debugPermission(string $ruleName): array
    {
        $service = new PermissionService();
        
        return [
            'rule_name' => $ruleName,
            'parsed_permission' => $service->parsePermission($ruleName),
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 检查用户权限
     */
    public static function checkUserPermissions(int $adminId): array
    {
        $permissions = Db::table('system_admin_role')
            ->alias('ar')
            ->leftJoin('system_role_menu rm', 'ar.role_id = rm.role_id')
            ->leftJoin('system_menu m', 'rm.menu_id = m.id')
            ->where('ar.admin_id', $adminId)
            ->where('m.status', 1)
            ->where('m.deleted_at', null)
            ->column('m.name');
            
        return $permissions;
    }
}
```

### 日志记录
```php
// 在PermissionMiddleware中添加日志
Log::info('Permission Check', [
    'admin_id' => $adminId,
    'permission' => $permission,
    'result' => $hasPermission ? 'granted' : 'denied',
    'timestamp' => date('Y-m-d H:i:s')
]);
```

---

## 📋 最佳实践

### 1. 权限粒度控制
- **页面级权限**: 控制页面访问
- **功能级权限**: 控制具体功能操作
- **数据级权限**: 控制数据访问范围

### 2. 性能优化
- **权限缓存**: 缓存用户权限列表
- **批量查询**: 避免N+1查询问题
- **索引优化**: 为权限查询添加合适索引

### 3. 安全考虑
- **最小权限原则**: 用户只拥有必要权限
- **权限审计**: 记录权限变更历史
- **定期检查**: 定期检查权限分配合理性

### 4. 维护建议
- **文档同步**: 及时更新权限文档
- **测试覆盖**: 确保权限测试覆盖率
- **代码审查**: 权限相关代码必须审查

---

**权限系统是系统安全的核心，请严格按照本指南进行开发和维护。**

/**
 * 数字处理工具函数
 * 包含数字转中文大写、格式化等常用功能
 */

/**
 * 数字转中文大写金额
 * 主要用于财务相关业务场景，如出库申请、出货申请、付款申请等
 *
 * @param num 要转换的数字，支持整数和小数
 * @returns 中文大写金额字符串
 *
 * @example
 * // 基本用法
 * convertToChineseNumber(0) // 返回 "零元整"
 * convertToChineseNumber(123.45) // 返回 "壹佰贰拾叁元肆角伍分"
 * convertToChineseNumber(1000) // 返回 "壹仟元整"
 * convertToChineseNumber(10000.50) // 返回 "壹万元伍角"
 * convertToChineseNumber(100000000) // 返回 "壹亿元整"
 *
 * // 特殊情况
 * convertToChineseNumber(-100) // 返回 "负壹佰元整"
 * convertToChineseNumber(1234567890.12) // 返回 "拾贰亿叁仟肆佰伍拾陆万柒仟捌佰玖拾元壹角贰分"
 */
export function convertToChineseNumber(num: number): string {
  // 处理特殊情况
  if (isNaN(num)) return '零元整'
  if (num === 0) return '零元整'

  // 处理负数
  const isNegative = num < 0
  const absNum = Math.abs(num)

  // 中文数字映射
  const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  const units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿']
  const decimalUnits = ['角', '分']

  // 将数字转换为字符串并分离整数和小数部分
  const numStr = absNum.toFixed(2)
  const [integerPart, decimalPart] = numStr.split('.')

  let result = ''

  // 处理整数部分
  if (parseInt(integerPart) === 0) {
    result = '零'
  } else {
    const integerArray = integerPart.split('').reverse()
    let tempResult = ''
    let hasZero = false // 标记是否需要添加零

    for (let i = 0; i < integerArray.length; i++) {
      const digit = parseInt(integerArray[i])
      const unitIndex = i % 9 // 处理亿级别的循环

      if (digit !== 0) {
        // 如果前面有零且当前不是万位或亿位，添加零
        if (hasZero && unitIndex !== 4 && unitIndex !== 8) {
          tempResult = '零' + tempResult
        }
        tempResult = digits[digit] + units[unitIndex] + tempResult
        hasZero = false
      } else {
        // 当前位是0
        if (unitIndex === 4 || unitIndex === 8) {
          // 万位或亿位，需要添加单位
          if (tempResult && !tempResult.startsWith(units[unitIndex])) {
            tempResult = units[unitIndex] + tempResult
          }
        }
        hasZero = true
      }
    }

    result = tempResult
  }

  result += '元'

  // 处理小数部分
  if (decimalPart && parseInt(decimalPart) > 0) {
    const jiao = parseInt(decimalPart[0])
    const fen = parseInt(decimalPart[1])

    if (jiao > 0) {
      result += digits[jiao] + decimalUnits[0]
    }
    if (fen > 0) {
      result += digits[fen] + decimalUnits[1]
    }
  } else {
    result += '整'
  }

  // 处理负数前缀
  if (isNegative) {
    result = '负' + result
  }

  return result
}

/**
 * 格式化数字为千分位显示
 *
 * @param num 要格式化的数字
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的字符串
 *
 * @example
 * formatNumberWithCommas(1234567.89) // 返回 "1,234,567.89"
 * formatNumberWithCommas(1234567.89, 0) // 返回 "1,234,568"
 * formatNumberWithCommas(1234567.89, 3) // 返回 "1,234,567.890"
 */
export function formatNumberWithCommas(num: number, decimals: number = 2): string {
  if (isNaN(num)) return '0'

  return num.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化货币显示
 *
 * @param num 要格式化的数字
 * @param currency 货币符号，默认'¥'
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的货币字符串
 *
 * @example
 * formatCurrency(1234.56) // 返回 "¥1,234.56"
 * formatCurrency(1234.56, '$') // 返回 "$1,234.56"
 * formatCurrency(1234.56, '¥', 0) // 返回 "¥1,235"
 */
export function formatCurrency(num: number, currency: string = '¥', decimals: number = 2): string {
  if (isNaN(num)) return `${currency}0`

  return `${currency}${formatNumberWithCommas(num, decimals)}`
}

/**
 * 安全的数字加法运算（避免浮点数精度问题）
 *
 * @param a 第一个数字
 * @param b 第二个数字
 * @returns 相加结果
 *
 * @example
 * safeAdd(0.1, 0.2) // 返回 0.3（而不是 0.30000000000000004）
 * safeAdd(1.23, 4.56) // 返回 5.79
 */
export function safeAdd(a: number, b: number): number {
  const aDecimals = (a.toString().split('.')[1] || '').length
  const bDecimals = (b.toString().split('.')[1] || '').length
  const maxDecimals = Math.max(aDecimals, bDecimals)
  const multiplier = Math.pow(10, maxDecimals)

  return Math.round(a * multiplier + b * multiplier) / multiplier
}

/**
 * 安全的数字乘法运算（避免浮点数精度问题）
 *
 * @param a 第一个数字
 * @param b 第二个数字
 * @returns 相乘结果
 *
 * @example
 * safeMultiply(0.1, 3) // 返回 0.3（而不是 0.30000000000000004）
 * safeMultiply(1.23, 4.56) // 返回 5.61
 */
export function safeMultiply(a: number, b: number): number {
  const aDecimals = (a.toString().split('.')[1] || '').length
  const bDecimals = (b.toString().split('.')[1] || '').length
  const multiplier = Math.pow(10, aDecimals + bDecimals)

  return Math.round(a * Math.pow(10, aDecimals) * (b * Math.pow(10, bDecimals))) / multiplier
}

/**
 * 保留指定小数位数（四舍五入）
 *
 * @param num 要处理的数字
 * @param decimals 小数位数，默认2位
 * @returns 处理后的数字
 *
 * @example
 * roundToDecimals(1.2345, 2) // 返回 1.23
 * roundToDecimals(1.2356, 2) // 返回 1.24
 * roundToDecimals(1.2345) // 返回 1.23（默认2位小数）
 */
export function roundToDecimals(num: number, decimals: number = 2): number {
  if (isNaN(num)) return 0

  const multiplier = Math.pow(10, decimals)
  return Math.round(num * multiplier) / multiplier
}

/**
 * 判断是否为有效的数字
 *
 * @param value 要检查的值
 * @returns 是否为有效数字
 *
 * @example
 * isValidNumber(123) // 返回 true
 * isValidNumber('123') // 返回 true
 * isValidNumber('abc') // 返回 false
 * isValidNumber(null) // 返回 false
 */
export function isValidNumber(value: any): boolean {
  return !isNaN(parseFloat(value)) && isFinite(value)
}

/**
 * 转换为安全的数字（如果无效则返回默认值）
 *
 * @param value 要转换的值
 * @param defaultValue 默认值，默认为0
 * @returns 转换后的数字
 *
 * @example
 * toSafeNumber('123') // 返回 123
 * toSafeNumber('abc') // 返回 0
 * toSafeNumber('abc', 100) // 返回 100
 */
export function toSafeNumber(value: any, defaultValue: number = 0): number {
  const num = parseFloat(value)
  return isValidNumber(num) ? num : defaultValue
}

<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/office', function () {
	
	
	$nameSpace = '\app\office\controller';
	
	// 办公模块路由将在此处添加
	// 基于现有控制器文件结构，需要添加相应的路由定义
	
	// 预留：办公相关路由
	// Route::get('console/index', $nameSpace . '\OfficeConsoleController@index');
	
	// 预留：其他办公功能路由
	// 根据实际控制器文件添加相应路由
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
//	     OperationLogMiddleware::class
     ]);

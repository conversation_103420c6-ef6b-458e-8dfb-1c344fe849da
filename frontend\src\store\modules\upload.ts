import { defineStore } from 'pinia'
import type { UploadConfig } from '@/utils/upload/IUploader'
import { ApiStatus } from '@/utils/http/status'
import { UploadApi } from '@/api/uploadApi'
import { AttachmentApi } from '@/api/attachmentApi'
import { FileTypeUtil } from '@/utils/upload/FileTypeUtil'
import type { AttachmentPermissionResult, AttachmentStatsResult } from '@/api/attachmentApi'

/**
 * 上传配置接口
 */
interface UploadConfigResponse {
  upload_allow_type: string
  upload_allow_ext: string
  upload_allow_mime: {
    image: Record<string, string>
    audio: Record<string, string>
    video: Record<string, string>
    other: Record<string, string>
  }
  upload_allow_size: number
  max_file_count: number
  domain: string
}

/**
 * 上传配置Store
 */
export const useUploadStore = defineStore('uploadStore', {
  state: (): {
    config: UploadConfig | null
    loading: boolean
    error: string | null
    permissions: AttachmentPermissionResult | null
    stats: AttachmentStatsResult | null
  } => ({
    config: null,
    loading: false,
    error: null,
    permissions: null,
    stats: null
  }),

  getters: {
    // 获取上传配置
    uploadConfig: (state) => state.config,

    // 是否正在加载
    isLoading: (state) => state.loading,

    // 是否有错误
    hasError: (state) => !!state.error,

    // 获取错误信息
    errorMessage: (state) => state.error,

    // 获取用户权限信息
    userPermissions: (state) => state.permissions,

    // 获取用户统计信息
    userStats: (state) => state.stats,

    // 是否可以上传
    canUpload: (state) => state.permissions?.can_upload ?? true,

    // 是否可以下载
    canDownload: (state) => state.permissions?.can_download ?? true,

    // 是否可以删除
    canDelete: (state) => state.permissions?.can_delete ?? true,

    // 是否是管理员
    isAdmin: (state) => state.permissions?.is_admin ?? false,

    // 获取存储类型
    storageType: (state) => {
      if (!state.config) return 'local'

      // 将后端返回的存储类型映射为前端使用的存储类型
      switch (state.config.upload_type) {
        case 'qnoss':
          return 'qiniu' // 七牛云
        case 'txoss':
          return 'tencent' // 腾讯云
        case 'alioss':
          return 'aliyun' // 阿里云
        default:
          return state.config.upload_type || 'local'
      }
    },

    // 获取允许的文件类型
    allowedFileTypes: (state) => {
      if (!state.config) return []
      return state.config.upload_allow_ext_array || []
    },

    // 获取允许的MIME类型
    allowedMimeTypes: (state) => {
      if (!state.config) return []
      return state.config.upload_allow_mime || []
    },

    // 获取允许的最大文件大小(MB)
    maxFileSize: (state) => {
      if (!state.config) return 10 // 默认10MB
      return state.config.upload_allow_size || 10
    },

    // 获取允许的最大文件数量
    maxFileCount: (state) => {
      if (!state.config) return 10 // 默认10个
      return state.config.max_file_count || 10
    },

    // 获取域名
    domain: (state) => {
      if (!state.config) return ''
      return state.config.domain || ''
    }
  },

  actions: {
    // 设置上传配置
    setConfig(config: UploadConfig) {
      this.config = config
      this.error = null
    },

    // 重置上传配置
    resetConfig() {
      this.config = null
      this.error = null
    },

    // 设置错误信息
    setError(error: string) {
      this.error = error
    },

    // 从后端获取上传配置
    async fetchConfig() {
      if (this.loading) return

      this.loading = true
      this.error = null

      try {
        // 调用API获取配置
        const res = await UploadApi.getConfig()

        if (res.code === ApiStatus.success && res.data) {
          // 处理后端返回的配置
          const config = this.processBackendConfig(res.data)

          // 保存到Store
          this.setConfig(config)
          console.log('成功从后端获取上传配置', config)
          return config
        } else {
          const errorMsg = res.message || '获取上传配置失败'
          this.setError(errorMsg)
          console.error('从后端获取上传配置失败', errorMsg)
          this.resetConfig()
          return null
        }
      } catch (error: any) {
        const errorMsg = error.message || '获取上传配置异常'
        this.setError(errorMsg)
        console.error('从后端获取上传配置异常', error)
        this.resetConfig()
        return null
      } finally {
        this.loading = false
      }
    },

    // 处理后端返回的配置
    processBackendConfig(backendConfig: UploadConfigResponse): UploadConfig {
      // 将upload_allow_ext字符串转换为数组
      const extArray = backendConfig.upload_allow_ext
        ? backendConfig.upload_allow_ext.split(',').map((ext: string) => {
            // 移除可能存在的点号前缀
            return ext.trim().replace(/^\./, '')
          })
        : []

      // 从后端返回的mime类型结构中提取所有mime类型
      const mimeArray: string[] = []

      // 处理后端返回的MIME类型
      if (backendConfig.upload_allow_mime) {
        // 处理图片MIME类型
        if (backendConfig.upload_allow_mime.image) {
          Object.values(backendConfig.upload_allow_mime.image).forEach((mime) => {
            if (mime && !mimeArray.includes(mime)) {
              mimeArray.push(mime)
            }
          })
        }

        // 处理音频MIME类型
        if (backendConfig.upload_allow_mime.audio) {
          Object.values(backendConfig.upload_allow_mime.audio).forEach((mime) => {
            if (mime && !mimeArray.includes(mime)) {
              mimeArray.push(mime)
            }
          })
        }

        // 处理视频MIME类型
        if (backendConfig.upload_allow_mime.video) {
          Object.values(backendConfig.upload_allow_mime.video).forEach((mime) => {
            if (mime && !mimeArray.includes(mime)) {
              mimeArray.push(mime)
            }
          })
        }

        // 处理其他MIME类型
        if (backendConfig.upload_allow_mime.other) {
          Object.values(backendConfig.upload_allow_mime.other).forEach((mime) => {
            if (mime && !mimeArray.includes(mime)) {
              mimeArray.push(mime)
            }
          })
        }
      } else {
        // 如果没有提供MIME类型，根据扩展名生成MIME类型数组
        extArray.forEach((ext) => {
          const mime = FileTypeUtil.getMimeType(ext)
          if (mime && !mimeArray.includes(mime)) {
            mimeArray.push(mime)
          }
        })
      }

      // 获取存储类型并进行映射
      let storageType = backendConfig.upload_allow_type || 'local'

      // 将后端返回的存储类型映射为前端使用的存储类型
      switch (storageType) {
        case 'qnoss':
          storageType = 'qiniu' // 七牛云
          break
        case 'txoss':
          storageType = 'tencent' // 腾讯云
          break
        case 'alioss':
          storageType = 'aliyun' // 阿里云
          break
        default:
          // 其他情况保持不变
          break
      }

      // 构建新的配置对象
      const config: UploadConfig = {
        upload_allow_type: backendConfig.upload_allow_type || 'local',
        upload_allow_ext: backendConfig.upload_allow_ext || '',
        upload_allow_ext_array: extArray,
        upload_allow_mime: mimeArray,
        upload_allow_size: Number(backendConfig.upload_allow_size) || 10,
        max_file_count: Number(backendConfig.max_file_count) || 10,
        domain: backendConfig.domain || '',
        storage_type: storageType, // 使用映射后的存储类型
        upload_type: backendConfig.upload_allow_type || 'local', // 保留原始类型名称
        direct_upload: true // 默认启用直传
      }

      // 设置上传URL（仅本地上传使用）
      if (backendConfig.domain && config.storage_type === 'local') {
        config.upload_url = backendConfig.domain.endsWith('/')
          ? `${backendConfig.domain}api/system/upload`
          : `${backendConfig.domain}/api/system/upload`
      } else if (config.storage_type === 'local') {
        config.upload_url = '/api/system/upload'
      }

      // 确保upload_allow_ext_array存在
      if (!config.upload_allow_ext_array || config.upload_allow_ext_array.length === 0) {
        if (config.upload_allow_ext) {
          config.upload_allow_ext_array = config.upload_allow_ext
            .split(',')
            .map((ext) => ext.trim().replace(/^\./, ''))
        }
      }

      console.log('处理后的上传配置:', config)

      return config
    },

    // 获取上传配置的方法（统一使用fetchConfig）
    async getUploadConfig() {
      // 如果已有配置，直接返回
      if (this.uploadConfig) return this.uploadConfig

      // 否则调用fetchConfig获取
      return await this.fetchConfig()
    },

    // 获取用户权限信息
    async fetchPermissions() {
      try {
        const response = await AttachmentApi.permissions()
        if (response.code === ApiStatus.success) {
          this.permissions = response.data
          return response.data
        } else {
          throw new Error(response.message || '获取权限信息失败')
        }
      } catch (error: any) {
        console.error('获取权限信息失败:', error)
        this.setError(error.message || '获取权限信息失败')
        throw error
      }
    },

    // 获取用户统计信息
    async fetchStats() {
      try {
        const response = await AttachmentApi.stats()
        if (response.code === ApiStatus.success) {
          this.stats = response.data
          return response.data
        } else {
          throw new Error(response.message || '获取统计信息失败')
        }
      } catch (error: any) {
        console.error('获取统计信息失败:', error)
        this.setError(error.message || '获取统计信息失败')
        throw error
      }
    },

    // 初始化所有数据
    async initializeAll() {
      try {
        await Promise.all([this.fetchConfig(), this.fetchPermissions(), this.fetchStats()])
      } catch (error) {
        console.error('初始化上传数据失败:', error)
      }
    },

    // 清空所有数据
    clearAll() {
      this.config = null
      this.permissions = null
      this.stats = null
      this.error = null
    }
  }
})

# 待办任务统一入口分析报告

## 📋 项目概述

**分析日期**：2025-01-14  
**分析目标**：分析系统中各种待办任务的现状，设计统一的"我的待办"入口  
**当前问题**：待办任务分散在各个模块，缺乏统一的查看入口

## 🔍 现状分析

### 当前待办任务分布情况

#### 1. 工作流审批任务
- **页面路径**：`/workflow/task` (WorkflowTask.vue)
- **功能**：我的审批任务列表
- **标签页**：全部、待审批、已处理、已撤销
- **数据来源**：`WorkflowTaskApi.list()`
- **状态**：✅ 已实现完整功能

#### 2. 客户跟进提醒
- **分布位置**：分散在CRM各个页面
- **功能**：客户跟进计划、回访提醒
- **数据来源**：CRM客户表的跟进字段
- **状态**：❌ 缺乏统一入口

#### 3. 项目任务管理
- **页面路径**：`/project/task` (项目任务列表)
- **功能**：项目任务分配、进度跟踪
- **数据来源**：`ProjectTaskApi`
- **状态**：✅ 有独立页面，但未集成到统一待办

#### 4. 合同到期提醒
- **分布位置**：合同管理页面
- **功能**：合同到期、付款提醒
- **数据来源**：CRM合同表
- **状态**：❌ 缺乏统一入口

#### 5. 消息通知
- **页面路径**：`/notice/message` (Message.vue)
- **功能**：系统消息、通知查看
- **标签页**：全部、未读、已读、系统通知
- **状态**：✅ 已实现完整功能

### 问题总结

1. **入口分散**：待办任务分布在不同模块，用户需要逐个查看
2. **缺乏聚合**：没有统一的API接口聚合各类待办事项
3. **优先级不明**：无法按紧急程度统一排序
4. **效率低下**：用户需要记住多个页面路径

## 🎯 解决方案设计

### 方案一：创建统一待办中心页面（推荐）

#### 页面设计
```
路径：/workbench/todo
组件：WorkbenchTodo.vue
功能：聚合展示所有待办任务
```

#### 功能模块
1. **工作流审批** - 显示待审批的工作流任务
2. **客户跟进** - 显示需要跟进的客户
3. **项目任务** - 显示分配给我的项目任务
4. **合同提醒** - 显示即将到期的合同
5. **系统消息** - 显示未读的系统通知

#### 数据聚合API
```php
// 新增统一待办API
GET /api/workbench/todo-tasks

响应格式：
{
  "code": 200,
  "data": {
    "workflow_tasks": [...],      // 工作流待办
    "customer_follow": [...],     // 客户跟进
    "project_tasks": [...],       // 项目任务
    "contract_reminders": [...],  // 合同提醒
    "system_messages": [...],     // 系统消息
    "total_count": 25,           // 总待办数量
    "urgent_count": 5            // 紧急待办数量
  }
}
```

### 方案二：在现有首页工作台集成（当前采用）

#### 集成位置
- 首页工作台第二行左侧
- 组件：`TodoTasks.vue`
- 显示：聚合的待办任务列表（统一显示，无tabs分类）

#### 显示设计
- 直接显示所有待办任务，按优先级和时间排序
- 每个任务项点击跳转到对应的详细页面

## 🔧 技术实现方案

### 后端开发

#### 1. 新增工作台控制器
```php
// app/workbench/controller/TodoController.php
class TodoController extends BaseController 
{
    /**
     * 获取聚合待办任务
     */
    public function getTodoTasks(): Json
    {
        $userId = $this->request->adminId;
        $service = new TodoAggregationService();
        
        $result = $service->aggregateUserTodos($userId);
        
        return $this->success('获取成功', $result);
    }
}
```

#### 2. 待办聚合服务
```php
// app/workbench/service/TodoAggregationService.php
class TodoAggregationService 
{
    public function aggregateUserTodos(int $userId): array
    {
        return [
            'workflow_tasks' => $this->getWorkflowTodos($userId),
            'customer_follow' => $this->getCustomerFollowTodos($userId),
            'project_tasks' => $this->getProjectTaskTodos($userId),
            'contract_reminders' => $this->getContractReminders($userId),
            'system_messages' => $this->getSystemMessages($userId),
            'total_count' => 0, // 计算总数
            'urgent_count' => 0 // 计算紧急数量
        ];
    }
    
    private function getWorkflowTodos(int $userId): array
    {
        // 调用工作流服务获取待审批任务
        return WorkflowTaskService::getInstance()
            ->getMyTasks($userId, ['status' => 'pending']);
    }
    
    private function getCustomerFollowTodos(int $userId): array
    {
        // 获取需要跟进的客户
        return CrmCustomerService::getInstance()
            ->getFollowReminders($userId);
    }
    
    // ... 其他方法
}
```

### 前端开发

#### 1. 待办任务组件
```vue
<!-- frontend/src/views/dashboard/console/widget/TodoTasks.vue -->
<template>
  <div class="todo-tasks-widget">
    <div class="widget-header">
      <h4>我的待办任务</h4>
      <span class="total-count">{{ totalCount }}</span>
    </div>

    <div class="todo-list">
      <div
        v-for="task in allTasks"
        :key="`${task.type}-${task.id}`"
        class="task-item"
        @click="handleTaskClick(task)"
      >
        <div class="task-content">
          <div class="task-header">
            <ElTag :type="getTaskTypeColor(task.type)" size="small">
              {{ getTaskTypeText(task.type) }}
            </ElTag>
            <span class="task-title">{{ task.title }}</span>
          </div>
          <div class="task-meta">
            <span class="task-time">{{ formatTime(task.time) }}</span>
            <ElTag
              v-if="task.urgent"
              type="danger"
              size="small"
              effect="plain"
            >
              紧急
            </ElTag>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="allTasks.length === 0" class="empty-state">
        <ElEmpty
          :image-size="60"
          description="暂无待办任务"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { WorkbenchApi } from '@/api/workbench'
import { useRouter } from 'vue-router'

const router = useRouter()
const workflowTasks = ref([])
const customerFollow = ref([])
const projectTasks = ref([])

// 聚合所有待办任务
const allTasks = computed(() => {
  const tasks = []

  // 添加工作流任务
  workflowTasks.value.forEach(task => {
    tasks.push({
      ...task,
      type: 'workflow',
      urgent: isUrgentTask(task)
    })
  })

  // 添加CRM跟进任务
  customerFollow.value.forEach(task => {
    tasks.push({
      ...task,
      type: 'crm',
      urgent: isUrgentTask(task)
    })
  })

  // 添加项目任务
  projectTasks.value.forEach(task => {
    tasks.push({
      ...task,
      type: 'project',
      urgent: isUrgentTask(task)
    })
  })

  // 按优先级和时间排序
  return tasks.sort((a, b) => {
    if (a.urgent && !b.urgent) return -1
    if (!a.urgent && b.urgent) return 1
    return new Date(b.time) - new Date(a.time)
  })
})

const totalCount = computed(() => allTasks.value.length)

const loadTodoTasks = async () => {
  try {
    const res = await WorkbenchApi.getTodoTasks()
    if (res.code === 200) {
      workflowTasks.value = res.data.workflow_tasks || []
      customerFollow.value = res.data.customer_follow || []
      projectTasks.value = res.data.project_tasks || []
    }
  } catch (error) {
    console.error('加载待办任务失败:', error)
  }
}

const handleTaskClick = (task) => {
  // 根据任务类型跳转到对应页面
  switch (task.type) {
    case 'workflow':
      router.push('/workflow/task')
      break
    case 'crm':
      router.push('/crm/crm_follow_record')
      break
    case 'project':
      router.push(`/project/detail/${task.project_id}`)
      break
  }
}

const getTaskTypeText = (type) => {
  const typeMap = {
    workflow: '审批',
    crm: '跟进',
    project: '任务'
  }
  return typeMap[type] || '其他'
}

const getTaskTypeColor = (type) => {
  const colorMap = {
    workflow: 'warning',
    crm: 'primary',
    project: 'success'
  }
  return colorMap[type] || 'info'
}

const isUrgentTask = (task) => {
  // 判断任务是否紧急的逻辑
  if (task.type === 'workflow') {
    // 审批任务超过24小时为紧急
    return Date.now() - new Date(task.created_at).getTime() > 24 * 60 * 60 * 1000
  }
  if (task.type === 'crm') {
    // 跟进任务当天或逾期为紧急
    return new Date(task.next_date).toDateString() <= new Date().toDateString()
  }
  if (task.type === 'project') {
    // 项目任务明天截止为紧急
    return new Date(task.due_date).getTime() - Date.now() < 24 * 60 * 60 * 1000
  }
  return false
}

onMounted(() => {
  loadTodoTasks()
})
</script>
```

#### 2. API接口封装
```typescript
// frontend/src/api/workbench/index.ts
export class WorkbenchApi {
  /**
   * 获取聚合待办任务
   */
  static getTodoTasks() {
    return request.get<BaseResult>({
      url: '/api/workbench/todo-tasks'
    })
  }
  
  /**
   * 标记待办已读
   */
  static markTodoRead(params: { type: string; ids: number[] }) {
    return request.post<BaseResult>({
      url: '/api/workbench/mark-read',
      data: params
    })
  }
}
```

## 📊 实施计划

### 第一阶段：后端API开发 (2-3天)
- [ ] 创建工作台模块目录结构
- [ ] 实现TodoController控制器
- [ ] 实现TodoAggregationService服务
- [ ] 配置路由和中间件
- [ ] 编写单元测试

### 第二阶段：前端组件开发 (2-3天)
- [ ] 创建TodoTasks组件
- [ ] 实现API调用和数据处理
- [ ] 设计组件样式和交互
- [ ] 集成到首页工作台
- [ ] 测试各种待办类型显示

### 第三阶段：统一待办页面 (1-2天)
- [ ] 创建WorkbenchTodo页面
- [ ] 实现详细的待办任务列表
- [ ] 添加筛选和搜索功能
- [ ] 配置路由和菜单

### 第四阶段：测试和优化 (1天)
- [ ] 功能测试
- [ ] 性能优化
- [ ] 用户体验调优

## 🎯 预期效果

1. **提升效率**：用户可以在一个地方查看所有待办事项
2. **减少遗漏**：重要任务不会被忽略
3. **优先级管理**：按紧急程度排序显示
4. **快捷操作**：提供快速处理入口

---

**总结**：通过创建统一的待办任务入口，可以显著提升用户工作效率，减少任务遗漏，提供更好的用户体验。建议优先实施首页工作台集成方案，后续再考虑独立的待办中心页面。

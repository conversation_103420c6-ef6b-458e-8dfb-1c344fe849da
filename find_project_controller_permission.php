<?php
/**
 * 查找ProjectController对应的权限
 */

require_once 'vendor/autoload.php';

echo "=== 查找ProjectController对应的权限 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 查找包含'project'且包含'index'的权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE '%project%' AND name LIKE '%index%' 
        AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $permissions = $stmt->fetchAll();
    
    foreach ($permissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n2. 查找所有project模块的菜单权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'project:%' AND type = 1
        AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $menuPermissions = $stmt->fetchAll();
    
    foreach ($menuPermissions as $perm) {
        echo "  {$perm['name']} ({$perm['title']}) - 菜单\n";
    }
    
    echo "\n3. 查找project:project开头的权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'project:project:%'
        AND status = 1 AND deleted_at IS NULL
        ORDER BY name
        LIMIT 10
    ");
    $stmt->execute();
    $projectProjectPermissions = $stmt->fetchAll();
    
    foreach ($projectProjectPermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n4. 分析结果:\n";
    echo "  根据查询结果，ProjectController@index 应该对应的权限是:\n";
    
    // 查找最可能的权限
    foreach ($menuPermissions as $perm) {
        if (strpos($perm['name'], 'project:project') !== false && strpos($perm['name'], 'index') !== false) {
            echo "    可能的权限: {$perm['name']}\n";
        }
    }
    
    // 如果没找到，查找project:project开头的权限
    if (empty($projectProjectPermissions)) {
        echo "    没有找到project:project:开头的权限\n";
        echo "    可能ProjectController对应的权限格式不同\n";
    } else {
        echo "    找到project:project:开头的权限，ProjectController@index可能对应:\n";
        foreach ($projectProjectPermissions as $perm) {
            if (strpos($perm['name'], 'index') !== false) {
                echo "      {$perm['name']}\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 查找完成 ===\n";

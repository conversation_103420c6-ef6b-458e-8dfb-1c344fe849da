<?php
/**
 * 全面分析系统的控制器、路由、权限体系
 * 为统一规范制定提供数据支撑
 */

require_once 'vendor/autoload.php';

echo "=== 全面系统分析：控制器、路由、权限统一规范 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 控制器文件结构分析:\n";
    
    $modules = ['crm', 'system', 'project', 'workflow', 'notice', 'office', 'daily', 'ims'];
    $controllerStructure = [];
    
    foreach ($modules as $module) {
        $controllerStructure[$module] = [
            'main_controllers' => [],
            'sub_controllers' => [],
            'total_count' => 0
        ];
        
        $mainDir = "app/{$module}/controller";
        if (is_dir($mainDir)) {
            // 主目录控制器
            $files = glob($mainDir . '/*Controller.php');
            foreach ($files as $file) {
                $filename = basename($file, '.php');
                $controllerStructure[$module]['main_controllers'][] = $filename;
            }
            
            // 子目录控制器
            $subdirs = glob($mainDir . '/*', GLOB_ONLYDIR);
            foreach ($subdirs as $subdir) {
                $subdirName = basename($subdir);
                $subfiles = glob($subdir . '/*Controller.php');
                
                if (!empty($subfiles)) {
                    $controllerStructure[$module]['sub_controllers'][$subdirName] = [];
                    foreach ($subfiles as $file) {
                        $filename = basename($file, '.php');
                        $controllerStructure[$module]['sub_controllers'][$subdirName][] = $filename;
                    }
                }
            }
            
            $controllerStructure[$module]['total_count'] = 
                count($controllerStructure[$module]['main_controllers']) +
                array_sum(array_map('count', $controllerStructure[$module]['sub_controllers']));
        }
    }
    
    // 输出控制器结构
    foreach ($controllerStructure as $module => $structure) {
        if ($structure['total_count'] > 0) {
            echo "  {$module}模块 ({$structure['total_count']}个控制器):\n";
            
            if (!empty($structure['main_controllers'])) {
                echo "    主控制器:\n";
                foreach ($structure['main_controllers'] as $controller) {
                    echo "      - {$controller}\n";
                }
            }
            
            if (!empty($structure['sub_controllers'])) {
                echo "    子目录控制器:\n";
                foreach ($structure['sub_controllers'] as $subdir => $controllers) {
                    echo "      {$subdir}/:\n";
                    foreach ($controllers as $controller) {
                        echo "        - {$controller}\n";
                    }
                }
            }
            echo "\n";
        }
    }
    
    echo "2. 路由文件分析:\n";
    
    $routeFiles = [
        'route/Auth.php',
        'route/System.php', 
        'route/Crm.php',
        'route/Project.php',
        'route/Workflow.php',
        'route/Notice.php',
        'route/Office.php',
        'route/Daily.php',
        'route/Ims.php',
        'route/Router.php'
    ];
    
    $routeAnalysis = [];
    
    foreach ($routeFiles as $routeFile) {
        if (file_exists($routeFile)) {
            $content = file_get_contents($routeFile);
            $lines = explode("\n", $content);
            
            $routeAnalysis[$routeFile] = [
                'exists' => true,
                'total_lines' => count($lines),
                'route_count' => 0,
                'middleware_usage' => [],
                'naming_patterns' => []
            ];
            
            // 分析路由数量和中间件使用
            foreach ($lines as $line) {
                $line = trim($line);
                
                // 统计路由定义
                if (preg_match('/Route::(get|post|put|delete|patch|any|match|group)/', $line)) {
                    $routeAnalysis[$routeFile]['route_count']++;
                }
                
                // 分析中间件使用
                if (strpos($line, 'middleware') !== false) {
                    if (strpos($line, 'PermissionMiddleware') !== false) {
                        $routeAnalysis[$routeFile]['middleware_usage']['permission'] = 
                            ($routeAnalysis[$routeFile]['middleware_usage']['permission'] ?? 0) + 1;
                    }
                    if (strpos($line, 'TokenAuthMiddleware') !== false) {
                        $routeAnalysis[$routeFile]['middleware_usage']['token'] = 
                            ($routeAnalysis[$routeFile]['middleware_usage']['token'] ?? 0) + 1;
                    }
                }
            }
        } else {
            $routeAnalysis[$routeFile] = ['exists' => false];
        }
    }
    
    // 输出路由分析
    foreach ($routeAnalysis as $file => $analysis) {
        if ($analysis['exists']) {
            echo "  {$file}:\n";
            echo "    总行数: {$analysis['total_lines']}\n";
            echo "    路由数: {$analysis['route_count']}\n";
            echo "    权限中间件: " . ($analysis['middleware_usage']['permission'] ?? 0) . " 处\n";
            echo "    Token中间件: " . ($analysis['middleware_usage']['token'] ?? 0) . " 处\n";
        } else {
            echo "  {$file}: ❌ 不存在\n";
        }
    }
    
    echo "\n3. 权限数据分析:\n";
    
    // 分析权限命名模式
    $stmt = $pdo->prepare("
        SELECT name, title, type, COUNT(*) as count
        FROM system_menu 
        WHERE status = 1 AND deleted_at IS NULL
        GROUP BY name, title, type
        ORDER BY name
    ");
    $stmt->execute();
    $allPermissions = $stmt->fetchAll();
    
    $permissionPatterns = [
        'module:controller:action' => [],
        'module:sub:controller:action' => [],
        'module:controller' => [],
        'single_word' => [],
        'inconsistent' => []
    ];
    
    foreach ($allPermissions as $perm) {
        $name = $perm['name'];
        $parts = explode(':', $name);
        
        if (count($parts) == 4) {
            $permissionPatterns['module:sub:controller:action'][] = $perm;
        } elseif (count($parts) == 3) {
            $permissionPatterns['module:controller:action'][] = $perm;
        } elseif (count($parts) == 2) {
            $permissionPatterns['module:controller'][] = $perm;
        } elseif (count($parts) == 1) {
            $permissionPatterns['single_word'][] = $perm;
        } else {
            $permissionPatterns['inconsistent'][] = $perm;
        }
    }
    
    echo "  权限命名模式分布:\n";
    foreach ($permissionPatterns as $pattern => $perms) {
        echo "    {$pattern}: " . count($perms) . " 个\n";
    }
    
    // 分析各模块权限一致性
    echo "\n  各模块权限一致性分析:\n";
    
    $modulePermissions = [];
    foreach ($allPermissions as $perm) {
        $parts = explode(':', $perm['name']);
        if (count($parts) >= 2) {
            $module = $parts[0];
            if (!isset($modulePermissions[$module])) {
                $modulePermissions[$module] = [
                    'three_parts' => 0,
                    'four_parts' => 0,
                    'two_parts' => 0,
                    'total' => 0
                ];
            }
            
            $modulePermissions[$module]['total']++;
            
            if (count($parts) == 4) {
                $modulePermissions[$module]['four_parts']++;
            } elseif (count($parts) == 3) {
                $modulePermissions[$module]['three_parts']++;
            } elseif (count($parts) == 2) {
                $modulePermissions[$module]['two_parts']++;
            }
        }
    }
    
    foreach ($modulePermissions as $module => $stats) {
        $consistency = 'mixed';
        if ($stats['three_parts'] > 0 && $stats['four_parts'] == 0 && $stats['two_parts'] == 0) {
            $consistency = 'three_parts_only';
        } elseif ($stats['four_parts'] > 0 && $stats['three_parts'] == 0 && $stats['two_parts'] == 0) {
            $consistency = 'four_parts_only';
        } elseif ($stats['two_parts'] > 0 && $stats['three_parts'] == 0 && $stats['four_parts'] == 0) {
            $consistency = 'two_parts_only';
        }
        
        echo "    {$module}: {$stats['total']}个权限 ";
        echo "(3段:{$stats['three_parts']}, 4段:{$stats['four_parts']}, 2段:{$stats['two_parts']}) ";
        echo "- " . ($consistency === 'mixed' ? '❌ 混合' : '✅ 一致') . "\n";
    }
    
    echo "\n4. 中间件使用分析:\n";
    
    // 检查中间件文件
    $middlewareFiles = [
        'app/common/middleware/PermissionMiddleware.php',
        'app/common/middleware/TokenAuthMiddleware.php'
    ];
    
    foreach ($middlewareFiles as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $lines = count(explode("\n", $content));
            echo "  {$file}: ✅ 存在 ({$lines}行)\n";
        } else {
            echo "  {$file}: ❌ 不存在\n";
        }
    }
    
    echo "\n5. 权限解析服务分析:\n";
    
    $serviceFile = 'app/system/service/PermissionService.php';
    if (file_exists($serviceFile)) {
        $content = file_get_contents($serviceFile);
        $lines = count(explode("\n", $content));
        
        // 检查各模块的权限生成方法
        $methods = [
            'generateCrmPermissionName',
            'generateSystemPermissionName', 
            'generateProjectPermissionName',
            'generateWorkflowPermissionName',
            'generateNoticePermissionName',
            'generateOfficePermissionName'
        ];
        
        echo "  PermissionService.php: ✅ 存在 ({$lines}行)\n";
        echo "  权限生成方法:\n";
        
        foreach ($methods as $method) {
            $exists = strpos($content, $method) !== false;
            echo "    {$method}: " . ($exists ? '✅' : '❌') . "\n";
        }
    } else {
        echo "  PermissionService.php: ❌ 不存在\n";
    }
    
    // 保存分析数据
    $analysisData = [
        'generated_at' => date('Y-m-d H:i:s'),
        'controller_structure' => $controllerStructure,
        'route_analysis' => $routeAnalysis,
        'permission_patterns' => $permissionPatterns,
        'module_permissions' => $modulePermissions,
        'total_permissions' => count($allPermissions),
        'total_controllers' => array_sum(array_column($controllerStructure, 'total_count'))
    ];
    
    file_put_contents('system_analysis_data.json', json_encode($analysisData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "\n✅ 分析数据已保存到: system_analysis_data.json\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 系统分析完成 ===\n";

<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\common\exception\ValidateFailedException;
use app\system\model\JobModelModel;
use app\system\validate\PostValidate;
use think\db\exception\DbException;


class PostService extends BaseService
{
	
	public function __construct()
	{
		$this->model = new JobModelModel();
		parent::__construct();
	}
	
	/**
	 * 获取岗位列表
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getList(array $params): array
	{
		$where = [];
		
		// 岗位名称
		if (!empty($params['name'])) {
			$where[] = [
				'name',
				'like',
				'%' . $params['name'] . '%',
			];
		}
		
		// 岗位编码
		if (!empty($params['code'])) {
			$where[] = [
				'code',
				'like',
				'%' . $params['code'] . '%',
			];
		}
		
		// 岗位状态
		if (!empty($params['status'])) {
			$where[] = [
				'status',
				'=',
				$params['status'],
			];
		}
		
		// 排序规则
		$order = 'sort desc, id asc';
		
		$page  = intval($params['page'] ?? 1);
		$limit = intval($params['limit'] ?? 10);
		
		$list = $this->getCrudService()->getPageList($where, $order, $page, $limit);
		
		return [
			'list'      => $list,
			'total'     => $this->getCrudService()->getCount($where),
			'page'      => $page,
			'limit' => $limit,
		];
	}
	
	/**
	 * 获取岗位详情
	 *
	 * @param int $postId 岗位ID
	 */
	public function getDetail(int $postId)
	{
		$info = $this->getCrudService()->getOne([
			[
				'id',
				'=',
				$postId
			],
		]);
		
		if ($info->isEmpty()) {
			throw new BusinessException('岗位不存在');
		}
		
		return $info;
	}
	
	/**
	 * 创建岗位
	 *
	 * @param array $data 岗位数据
	 * @return int
	 */
	public function create(array $data): int
	{
		try {
			validate(PostValidate::class)->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateFailedException($e->getMessage());
		}
		
		return $this->model->saveByCreate($data);
	}
	
	/**
	 * 更新岗位
	 *
	 * @param int   $postId 岗位ID
	 * @param array $data   岗位数据
	 * @return bool
	 */
	public function update(int $postId, array $data): bool
	{
		try {
			validate(PostValidate::class)->check($data);
		}
		catch (ValidateFailedException $e) {
			throw new ValidateFailedException($e->getMessage());
		}
		
		$info = $this->getCrudService()->getOne([
			[
				'id',
				'=',
				$postId
			],
		]);
		
		if ($info->isEmpty()) {
			throw new BusinessException('岗位不存在');
		}
		return $info->saveByUpdate($data);
	}
	
	/**
	 * 删除岗位
	 *
	 * @param int $postId 岗位ID
	 * @return bool
	 * @throws DbException
	 */
	public function delete(int $postId): bool
	{
		$info = $this->getCrudService()->getOne([
			[
				'id',
				'=',
				$postId
			],
		]);
		
		if ($info->isEmpty()) {
			throw new BusinessException('岗位不存在');
		}
		
		// 检查是否有用户使用了该岗位
		$userCount = AdminService::getInstance()
		                         ->getModel()
		                         ->whereFindInSet('post_ids', $postId)
		                         ->count();
		
		if ($userCount > 0) {
			throw new BusinessException('当前岗位使用中');
		}
		
		return $info->delete();
	}
	
	/**
	 * 获取岗位下拉选项
	 *
	 * @param array $where 查询条件
	 * @return array
	 */
	public function getOptions(array $where = []): array
	{
		return $this->model->field('id,name')
		                   ->where($where)
		                   ->order('sort', 'desc')
		                   ->select()->toArray();
	}
	
} 
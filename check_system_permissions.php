<?php
/**
 * 检查system模块的权限标识
 */

require_once 'vendor/autoload.php';

echo "=== 检查system模块的权限标识 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 查找auth相关权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE '%auth%' AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $authPermissions = $stmt->fetchAll();
    
    foreach ($authPermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n2. 查找config相关权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE '%config%' AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $configPermissions = $stmt->fetchAll();
    
    foreach ($configPermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n3. 查找所有system模块权限（不含子目录）:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'system:%' 
        AND name NOT LIKE 'system:%:%' 
        AND status = 1 AND deleted_at IS NULL
        ORDER BY name
    ");
    $stmt->execute();
    $systemBasePermissions = $stmt->fetchAll();
    
    foreach ($systemBasePermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n4. 查找system子目录权限:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'system:%:%' 
        AND status = 1 AND deleted_at IS NULL
        ORDER BY name
        LIMIT 20
    ");
    $stmt->execute();
    $systemSubPermissions = $stmt->fetchAll();
    
    foreach ($systemSubPermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n5. 分析权限命名规律:\n";
    
    // 分析权限命名规律
    $allSystemPermissions = array_merge($systemBasePermissions, $systemSubPermissions);
    
    $patterns = [];
    foreach ($allSystemPermissions as $perm) {
        $parts = explode(':', $perm['name']);
        if (count($parts) == 2) {
            // system:xxx 格式
            $pattern = "system:{功能}";
            if (!in_array($pattern, $patterns)) {
                $patterns[] = $pattern;
            }
        } elseif (count($parts) == 3) {
            // system:xxx:yyy 格式
            $pattern = "system:{子目录}:{功能}";
            if (!in_array($pattern, $patterns)) {
                $patterns[] = $pattern;
            }
        }
    }
    
    echo "  发现的权限格式:\n";
    foreach ($patterns as $pattern) {
        echo "    {$pattern}\n";
    }
    
    echo "\n6. 建议的权限解析规则:\n";
    echo "  对于system模块:\n";
    echo "  - 基础控制器: system:{controller}:{method}\n";
    echo "  - 子目录控制器: system:{subdir}:{controller}:{method}\n";
    echo "  \n";
    echo "  例如:\n";
    echo "  - AuthController@login 应该解析为: system:auth:login\n";
    echo "  - ConfigController@index 应该解析为: system:config:index\n";
    echo "  - permission\\AdminController@index 应该解析为: system:permission:admin:index\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查完成 ===\n";

<?php
/**
 * 分析租户1的用户和角色权限
 */

require_once 'vendor/autoload.php';

echo "=== 分析租户1用户和角色权限 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 租户1的用户列表:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, username, email, status, created_at
        FROM system_admin
        WHERE tenant_id = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $tenant1Users = $stmt->fetchAll();
    
    echo "  租户1用户数: " . count($tenant1Users) . " 个\n\n";
    
    foreach ($tenant1Users as $user) {
        $status = $user['status'] == 1 ? '正常' : '禁用';
        echo "  ID:{$user['id']} {$user['username']} - {$status}\n";
        echo "    邮箱: {$user['email']}\n";
        echo "    创建时间: {$user['created_at']}\n\n";
    }
    
    echo "2. 租户1的角色列表:\n";
    
    $stmt = $pdo->prepare("
        SELECT id, name, description, status, created_at
        FROM system_role 
        WHERE tenant_id = 1 AND deleted_at IS NULL
        ORDER BY id
    ");
    $stmt->execute();
    $tenant1Roles = $stmt->fetchAll();
    
    echo "  租户1角色数: " . count($tenant1Roles) . " 个\n\n";
    
    foreach ($tenant1Roles as $role) {
        $status = $role['status'] == 1 ? '正常' : '禁用';
        echo "  ID:{$role['id']} {$role['name']} - {$status}\n";
        echo "    描述: {$role['description']}\n";
        echo "    创建时间: {$role['created_at']}\n\n";
    }
    
    echo "3. 用户角色分配情况:\n";
    
    foreach ($tenant1Users as $user) {
        echo "  用户 {$user['username']} (ID:{$user['id']}) 的角色:\n";
        
        $stmt = $pdo->prepare("
            SELECT r.id, r.name, r.description
            FROM system_admin_role ar
            LEFT JOIN system_role r ON ar.role_id = r.id
            WHERE ar.admin_id = ? AND r.deleted_at IS NULL
        ");
        $stmt->execute([$user['id']]);
        $userRoles = $stmt->fetchAll();
        
        if (empty($userRoles)) {
            echo "    ❌ 未分配任何角色\n";
        } else {
            foreach ($userRoles as $role) {
                echo "    ✅ 角色ID:{$role['id']} {$role['name']} ({$role['description']})\n";
            }
        }
        echo "\n";
    }
    
    echo "4. 角色权限分配情况:\n";
    
    foreach ($tenant1Roles as $role) {
        echo "  角色 {$role['name']} (ID:{$role['id']}) 的权限:\n";
        
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM system_role_menu rm
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE rm.role_id = ? AND m.status = 1 AND m.deleted_at IS NULL
        ");
        $stmt->execute([$role['id']]);
        $permissionCount = $stmt->fetch()['count'];
        
        echo "    权限数量: {$permissionCount} 个\n";
        
        if ($permissionCount > 0) {
            // 显示前10个权限示例
            $stmt = $pdo->prepare("
                SELECT m.name, m.title
                FROM system_role_menu rm
                LEFT JOIN system_menu m ON rm.menu_id = m.id
                WHERE rm.role_id = ? AND m.status = 1 AND m.deleted_at IS NULL
                ORDER BY m.name
                LIMIT 10
            ");
            $stmt->execute([$role['id']]);
            $samplePermissions = $stmt->fetchAll();
            
            echo "    权限示例:\n";
            foreach ($samplePermissions as $perm) {
                echo "      - {$perm['name']} ({$perm['title']})\n";
            }
            
            if ($permissionCount > 10) {
                echo "      ... 还有 " . ($permissionCount - 10) . " 个权限\n";
            }
        }
        echo "\n";
    }
    
    echo "5. 问题诊断:\n";
    
    $issues = [];
    
    // 检查是否有用户没有角色
    foreach ($tenant1Users as $user) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM system_admin_role ar
            LEFT JOIN system_role r ON ar.role_id = r.id
            WHERE ar.admin_id = ? AND r.deleted_at IS NULL
        ");
        $stmt->execute([$user['id']]);
        $roleCount = $stmt->fetch()['count'];
        
        if ($roleCount == 0) {
            $issues[] = "用户 {$user['username']} (ID:{$user['id']}) 未分配角色";
        }
    }
    
    // 检查是否有角色没有权限
    foreach ($tenant1Roles as $role) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM system_role_menu rm
            LEFT JOIN system_menu m ON rm.menu_id = m.id
            WHERE rm.role_id = ? AND m.status = 1 AND m.deleted_at IS NULL
        ");
        $stmt->execute([$role['id']]);
        $permissionCount = $stmt->fetch()['count'];
        
        if ($permissionCount == 0) {
            $issues[] = "角色 {$role['name']} (ID:{$role['id']}) 未分配权限";
        }
    }
    
    if (empty($issues)) {
        echo "  ✅ 未发现明显问题\n";
    } else {
        echo "  发现的问题:\n";
        foreach ($issues as $issue) {
            echo "    ❌ {$issue}\n";
        }
    }
    
    echo "\n6. 建议的修复方案:\n";
    echo "  1. 为所有用户分配适当的角色\n";
    echo "  2. 为所有角色分配完整的权限\n";
    echo "  3. 特别关注tenant_admin用户的超级管理员权限\n";
    echo "  4. 验证权限分配后的功能访问\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

echo "\n=== 分析完成 ===\n";

<?php
/**
 * 修正权限映射中的问题
 */

echo "=== 修正权限映射 ===\n\n";

// 读取生成的映射数据
$mappingData = json_decode(file_get_contents('permission_mapping.json'), true);

echo "1. 检查生成的权限映射:\n";
echo "  总权限数: {$mappingData['total_permissions']}\n";
echo "  需要更新: {$mappingData['updates']} 个\n";
echo "  需要新增: {$mappingData['inserts']} 个\n";
echo "  需要删除: {$mappingData['deletes']} 个\n\n";

echo "2. 修正问题权限:\n";

$fixedMappings = [];
$fixedSqls = [];

foreach ($mappingData['mappings'] as $mapping) {
    $oldName = $mapping['old_name'];
    $newName = $mapping['new_name'];
    $id = $mapping['id'];
    
    // 修正明显的问题
    $correctedName = correctPermissionName($oldName, $newName);
    
    if ($correctedName !== $newName) {
        echo "  修正: {$newName} -> {$correctedName}\n";
        $mapping['new_name'] = $correctedName;
        $mapping['corrected'] = true;
    }
    
    $fixedMappings[] = $mapping;
    $fixedSqls[] = "UPDATE system_menu SET name = '{$mapping['new_name']}' WHERE id = {$id}; -- {$oldName} -> {$mapping['new_name']}";
}

echo "\n3. 修正缺失权限:\n";

$fixedInserts = [];
foreach ($mappingData['missing'] as $missing) {
    $name = $missing['name'];
    $title = $missing['title'];
    
    // 修正权限名称
    $correctedName = correctMissingPermissionName($name);
    $correctedTitle = correctPermissionTitle($title);
    
    if ($correctedName !== $name) {
        echo "  修正: {$name} -> {$correctedName}\n";
    }
    
    $missing['name'] = $correctedName;
    $missing['title'] = $correctedTitle;
    $fixedInserts[] = $missing;
    
    $fixedSqls[] = "INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('{$correctedName}', '{$correctedTitle}', 1, 1, 100, NOW(), NOW());";
}

echo "\n4. 生成修正后的SQL脚本:\n";

$sqlScript = "-- 权限命名规范化SQL脚本（修正版）\n";
$sqlScript .= "-- 生成时间: " . date('Y-m-d H:i:s') . "\n";
$sqlScript .= "-- 总更新数: " . count($fixedMappings) . " 个\n";
$sqlScript .= "-- 总新增数: " . count($fixedInserts) . " 个\n";
$sqlScript .= "-- 总删除数: " . count($mappingData['redundant']) . " 个\n\n";

$sqlScript .= "-- 开始事务\n";
$sqlScript .= "START TRANSACTION;\n\n";

$sqlScript .= "-- 1. 更新现有权限名称\n";
foreach ($fixedMappings as $mapping) {
    $sqlScript .= "UPDATE system_menu SET name = '{$mapping['new_name']}' WHERE id = {$mapping['id']}; -- {$mapping['old_name']} -> {$mapping['new_name']}\n";
}

$sqlScript .= "\n-- 2. 添加缺失权限\n";
foreach ($fixedInserts as $insert) {
    $sqlScript .= "INSERT INTO system_menu (name, title, type, status, sort, created_at, updated_at) VALUES ('{$insert['name']}', '{$insert['title']}', 1, 1, 100, NOW(), NOW());\n";
}

$sqlScript .= "\n-- 3. 删除冗余权限\n";
foreach ($mappingData['redundant'] as $redundant) {
    $sqlScript .= "DELETE FROM system_menu WHERE id = {$redundant['id']}; -- 删除冗余权限: {$redundant['name']}\n";
}

$sqlScript .= "\n-- 提交事务\n";
$sqlScript .= "COMMIT;\n\n";
$sqlScript .= "-- 如有问题，可以回滚：ROLLBACK;\n";

file_put_contents('permission_normalization_fixed.sql', $sqlScript);
echo "  ✅ 修正后的SQL脚本已生成: permission_normalization_fixed.sql\n";

// 保存修正后的映射数据
$fixedMappingData = [
    'generated_at' => date('Y-m-d H:i:s'),
    'total_permissions' => $mappingData['total_permissions'],
    'updates' => count($fixedMappings),
    'inserts' => count($fixedInserts),
    'deletes' => count($mappingData['redundant']),
    'mappings' => $fixedMappings,
    'missing' => $fixedInserts,
    'redundant' => $mappingData['redundant']
];

file_put_contents('permission_mapping_fixed.json', json_encode($fixedMappingData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "  ✅ 修正后的权限映射已生成: permission_mapping_fixed.json\n";

echo "\n5. 主要修正内容:\n";
echo "  - 修正了重复的模块前缀问题\n";
echo "  - 修正了不合理的权限名称\n";
echo "  - 优化了权限标题\n";
echo "  - 确保权限名称与控制器文件对应\n";

/**
 * 修正权限名称
 */
function correctPermissionName($oldName, $newName) {
    // 修正重复前缀问题
    $corrections = [
        'project:project_project:' => 'project:project:',
        'system:system_system:' => 'system:system:',
        'office:office_office:' => 'office:office:',
        'notice:notice_notice:' => 'notice:notice:',
        'crm:crm_work_report/index:index' => 'crm:crm_work_report:index',
        'system:system_article:index' => 'system:article:index', // article权限保持简单
        'system:system_log:index' => 'system:log:index', // log权限保持简单
    ];
    
    foreach ($corrections as $pattern => $replacement) {
        if (strpos($newName, $pattern) !== false) {
            return str_replace($pattern, $replacement, $newName);
        }
    }
    
    // 特殊处理一些权限
    if ($oldName === 'article') {
        return 'system:article:index';
    }
    
    if ($oldName === 'log') {
        return 'system:log:index';
    }
    
    if ($oldName === 'message') {
        return 'notice:message:index';
    }
    
    if ($oldName === 'notice') {
        return 'notice:notice:index';
    }
    
    if ($oldName === 'office') {
        return 'office:office:index';
    }
    
    return $newName;
}

/**
 * 修正缺失权限名称
 */
function correctMissingPermissionName($name) {
    // 移除重复的模块前缀
    $corrections = [
        'crm:crm_business:index' => 'crm:crm_business:index', // 保持不变，这是正确的
        'project:project_project:index' => 'project:project:index',
        'system:system_' => 'system:',
    ];
    
    foreach ($corrections as $pattern => $replacement) {
        if (strpos($name, $pattern) !== false) {
            return str_replace($pattern, $replacement, $name);
        }
    }
    
    return $name;
}

/**
 * 修正权限标题
 */
function correctPermissionTitle($title) {
    // 移除重复的前缀
    $title = str_replace(['CRMCRM', 'CRM CRM'], 'CRM', $title);
    $title = str_replace(['ProjectProject', 'Project Project'], 'Project', $title);
    $title = str_replace(['SystemSystem', 'System System'], 'System', $title);
    
    // 修正一些标题
    $corrections = [
        'CRM商机管理' => '商机管理',
        'CRM客户管理' => '客户管理',
        'CRM合同管理' => '合同管理',
        'CRM产品管理' => '产品管理',
        'ProjectTaskRecord管理' => '项目任务记录管理',
        'CRMFollowRecord管理' => '跟进记录管理',
        'CRMLeadPool管理' => '线索池管理',
        'CRMSeaRule管理' => '公海规则管理',
        'CRM统计管理' => '统计分析',
    ];
    
    return $corrections[$title] ?? $title;
}

echo "\n=== 权限映射修正完成 ===\n";

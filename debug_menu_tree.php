<?php
/**
 * 调试MenuService的buildMenuTree方法
 * 分析为什么传入的菜单数量和输出不匹配
 */

// 模拟获取管理员215的菜单数据
function getMenuData() {
    // 连接数据库
    $pdo = new PDO('mysql:host=localhost;dbname=base_admin', 'root', '123456');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 获取管理员215的所有菜单权限
    $sql = "
        SELECT DISTINCT
            m.id,
            m.parent_id,
            m.title,
            m.name,
            m.path,
            m.component,
            m.type,
            m.icon,
            m.sort,
            m.external,
            m.keep_alive,
            m.visible,
            m.hide_tab,
            m.status
        FROM system_admin_role ar
        JOIN system_role_menu rm ON ar.role_id = rm.role_id
        JOIN system_menu m ON rm.menu_id = m.id
        WHERE ar.admin_id = 215 
          AND m.status = 1 
          AND m.deleted_at IS NULL
        ORDER BY m.sort DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// 模拟buildMenuTreeOptimized方法的逻辑
function debugBuildMenuTree($menuList) {
    echo "=== 开始调试buildMenuTree ===\n";
    echo "传入菜单总数: " . count($menuList) . "\n";
    
    // 分析菜单类型分布
    $typeCount = [];
    foreach ($menuList as $menu) {
        $type = $menu['type'];
        $typeCount[$type] = ($typeCount[$type] ?? 0) + 1;
    }
    
    echo "\n菜单类型分布:\n";
    foreach ($typeCount as $type => $count) {
        $typeName = $type == 0 ? '目录' : ($type == 1 ? '菜单' : ($type == 2 ? '按钮' : '其他'));
        echo "- 类型 {$type} ({$typeName}): {$count} 个\n";
    }
    
    // 按类型分组，模拟buildMenuTreeOptimized的逻辑
    $menus = [];       // 菜单项 (type != 2)
    $permissions = []; // 权限项 (type == 2)
    
    foreach ($menuList as $item) {
        if ($item['type'] == 2) {
            $permissions[$item['parent_id']][] = [
                'id' => $item['id'],
                'title' => $item['title'],
                'auth_mark' => $item['name'],
                'name' => $item['name'],
            ];
        } else {
            $menus[] = $item;
        }
    }
    
    echo "\n分组后统计:\n";
    echo "- 菜单项数量: " . count($menus) . "\n";
    echo "- 权限项分组数量: " . count($permissions) . "\n";
    
    // 分析顶级菜单
    $topMenus = array_filter($menus, function($menu) {
        return $menu['parent_id'] == 0;
    });
    
    echo "\n顶级菜单分析:\n";
    echo "- 顶级菜单数量: " . count($topMenus) . "\n";
    
    foreach ($topMenus as $menu) {
        echo "- ID: {$menu['id']}, 标题: {$menu['title']}, 类型: {$menu['type']}, 可见: {$menu['visible']}\n";
    }
    
    // 模拟递归构建树形结构
    $tree = buildTreeRecursive($menus, $permissions, 0);
    
    echo "\n最终输出:\n";
    echo "- 输出菜单数量: " . count($tree) . "\n";
    
    foreach ($tree as $menu) {
        echo "- 菜单: ID={$menu['id']}, 标题={$menu['title']}, 子菜单数=" . count($menu['children'] ?? []) . "\n";
        if (!empty($menu['children'])) {
            foreach ($menu['children'] as $child) {
                echo "  - 子菜单: ID={$child['id']}, 标题={$child['title']}\n";
            }
        }
    }
    
    return $tree;
}

// 模拟递归构建树形结构的方法
function buildTreeRecursive($menus, $permissions, $parentId) {
    $tree = [];
    
    foreach ($menus as $item) {
        if ($item['parent_id'] == $parentId) {
            // 递归获取子菜单
            $children = buildTreeRecursive($menus, $permissions, $item['id']);
            if (!empty($children)) {
                $item['children'] = $children;
            }
            
            // 获取权限列表
            $authList = $permissions[$item['id']] ?? [];
            
            $meta = [
                'title' => $item['title'],
                'icon' => $item['icon'],
                'keepAlive' => $item['keep_alive'],
                'isHide' => $item['visible'] == 0,
                'isIframe' => $item['external'] == 1,
                'isHideTab' => $item['hide_tab'] == 1,
                'authList' => $authList,
            ];
            
            $tree[] = [
                'id' => $item['id'],
                'title' => $item['title'],
                'name' => $item['name'],
                'path' => $item['path'],
                'component' => $item['component'],
                'meta' => $meta,
                'visible' => $item['visible'],
                'children' => $item['children'] ?? [],
            ];
        }
    }
    
    return $tree;
}

// 运行调试
try {
    echo "开始获取菜单数据...\n";
    $menuList = getMenuData();
    
    if (empty($menuList)) {
        echo "未获取到菜单数据！\n";
        exit;
    }
    
    $result = debugBuildMenuTree($menuList);
    
    echo "\n=== 调试完成 ===\n";
    echo "详细结果:\n";
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo "调试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
}

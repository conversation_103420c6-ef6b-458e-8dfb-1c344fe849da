<?php
declare(strict_types=1);

namespace app\crm\controller\traits;

use app\common\exception\BusinessException;
use app\crm\model\CrmCustomer;
use app\crm\model\CrmFollowRecord;
use think\facade\Db;
use think\response\Json;

/**
 * 客户跟进记录操作Trait
 */
trait CustomerFollowTrait
{
	/**
	 * 新增跟进
	 */
	public function addFollow(): Json
	{
		$data = $this->request->post();
		// todo 参数校验
		$customerId = $data['customer_id'] ?? 0;
		
		if (!$customerId) {
			return $this->error('客户ID不能为空');
		}
		Db::startTrans();
		try {
			
			// todo 获取客户后续可封装！
			$customerInfo = (new CrmCustomer())->findOrEmpty($customerId);
			
			if ($customerInfo->isEmpty()) {
				return $this->error('客户不存在');
			}
			
			$res = (new CrmFollowRecord())->saveByCreate($data);
			
			if (!$res) {
				throw new BusinessException('记录保存失败');
			}
			
			$res = $customerInfo->saveByUpdate([
				'last_followed_at' => $data['follow_date'],
				'next_followed_at' => $data['next_date']
			]);
			
			if (!$res) {
				throw new BusinessException('客户更新失败');
			}
			
			Db::commit();
			
			return $this->success('跟进记录添加成功');
			
		}
		catch (\Exception $e) {
			Db::rollback();
			return $this->error('跟进记录添加失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 编辑跟进
	 */
	public function editFollow(): Json
	{
		$data     = $this->request->post();
		$followId = $data['id'] ?? 0;
		
		if (!$followId) {
			return $this->error('跟进记录ID不能为空');
		}
		
		try {
			$follow = CrmFollowRecord::find($followId);
			if (!$follow) {
				return $this->error('跟进记录不存在');
			}
			
			// 检查是否为记录创建人
			if ($follow['creator_id'] != get_user_id() && !is_super_admin() && !is_tenant_super_admin()) {
				return $this->error('只能编辑自己创建的跟进记录');
			}
			
			// 更新数据
			unset($data['id']);
			$data['updated_at'] = date('Y-m-d H:i:s');
			
			$follow->save($data);
			
			return $this->success('跟进记录更新成功', $follow);
			
		}
		catch (\Exception $e) {
			return $this->error('跟进记录更新失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 删除跟进
	 */
	public function deleteFollow(): Json
	{
		$data     = $this->request->post();
		$followId = $data['id'] ?? 0;
		
		if (!$followId) {
			return $this->error('跟进记录ID不能为空');
		}
		
		// 验证跟进记录访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateFollowAccess((int)$followId, (int)get_user_id())) {
		// return $this->error('无权限操作此跟进记录');
		//	}
		
		try {
			$follow = CrmFollowRecord::find($followId);
			if (!$follow) {
				return $this->error('跟进记录不存在');
			}
			
			// 检查是否为记录创建人
			if ($follow['creator_id'] != get_user_id() && !is_super_admin() && !is_tenant_super_admin()) {
				return $this->error('只能删除自己创建的跟进记录');
			}
			
			// 软删除
			$follow->delete();
			
			return $this->success('跟进记录删除成功');
			
		}
		catch (\Exception $e) {
			return $this->error('跟进记录删除失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 跟进详情
	 */
	public function followDetail(): Json
	{
		$followId = $this->request->param('id', 0);
		
		if (!$followId) {
			return $this->error('跟进记录ID不能为空');
		}
		
		// 验证跟进记录访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateFollowAccess((int)$followId, (int)get_user_id())) {
		//		return $this->error('无权限访问此跟进记录');
		//	}
		
		try {
			$follow = CrmFollowRecord::with([
				'customer',
				'creator'
			])
			                         ->find($followId);
			
			if (!$follow) {
				return $this->error('跟进记录不存在');
			}
			
			return $this->success('获取成功', $follow);
			
		}
		catch (\Exception $e) {
			return $this->error('获取跟进详情失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 跟进记录列表
	 */
	public function followList(): Json
	{
		$customerId = $this->request->param('customer_id', 0);
		$page       = $this->request->param('page', 1, 'int');
		$limit      = $this->request->param('limit', 15, 'int');
		
		if (!$customerId) {
			return $this->error('客户ID不能为空');
		}
		
		// 验证客户访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateCustomerAccess((int)$customerId, (int)get_user_id())) {
		//		return $this->error('无权限访问此客户');
		//	}
		
		try {
			$query = CrmFollowRecord::with(['creator'])
			                        ->where('related_type', 'customer')
			                        ->where('related_id', $customerId)
			                        ->order('follow_date desc');
			
			$total = $query->count();
			$list  = $query->page($page, $limit)
			               ->select();
			
			return $this->success('获取成功', [
				'list'  => $list,
				'total' => $total,
				'page'  => $page,
				'limit' => $limit
			]);
			
		}
		
		catch (\Exception $e) {
			return $this->error('获取跟进记录列表失败：' . $e->getMessage());
		}
	}
}

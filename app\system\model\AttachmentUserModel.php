<?php
declare(strict_types=1);

namespace app\system\model;

use app\common\core\base\BaseModel;
use app\common\utils\BitFormat;
use think\model\relation\BelongsTo;

/**
 * 用户文件关联模型
 * 管理用户文件权限和显示信息
 */
class AttachmentUserModel extends BaseModel
{
	
	// 设置表名
	protected $name = 'system_attachment_user';
	
	// 设置字段信息
	protected $schema = [
		'id'            => 'int',
		'attachment_id' => 'int',
		'user_id'       => 'int',
		'tenant_id'     => 'int',
		'cate_id'       => 'int',
		'display_name'  => 'string',
		'original_name' => 'string',
		'upload_ip'     => 'string',
		'upload_source' => 'string',
		'creator_id'    => 'int',
		'created_at'    => 'datetime',
		'updated_at'    => 'datetime',
		'deleted_at'    => 'datetime',
	];
	
	// 字段类型转换
	protected $type = [
		'id'            => 'integer',
		'attachment_id' => 'integer',
		'user_id'       => 'integer',
		'tenant_id'     => 'integer',
		'cate_id'       => 'integer',
		'creator_id'    => 'integer',
		'created_at'    => 'datetime',
		'updated_at'    => 'datetime',
		'deleted_at'    => 'datetime',
	];
	
	/**
	 * 关联物理文件
	 */
	public function attachment(): BelongsTo
	{
		return $this->belongsTo(AttachmentModel::class, 'attachment_id', 'id');
	}
	
	/**
	 * 关联用户
	 */
	public function user(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'user_id', 'id');
	}
	
	/**
	 * 关联创建者
	 */
	public function creator(): BelongsTo
	{
		return $this->belongsTo(AdminModel::class, 'creator_id', 'id');
	}
	
	/**
	 * 获取用户文件列表（使用子查询优化，保持租户隔离）
	 * 用于媒体选择器等用户权限场景
	 */
	public static function getUserFiles(array $params = []): array
	{
		$userId = request()->adminId;

		// 第一步：构建基础查询（自动应用租户隔离和软删除）
		$query = self::where('user_id', $userId);

		// 分类筛选
		if (isset($params['cate_id']) && $params['cate_id'] >= 0) {
			$query->where('cate_id', $params['cate_id']);
		}

		// 文件名搜索
		if (!empty($params['name']) || !empty($params['keyword'])) {
			$keyword = $params['name'] ?? $params['keyword'];
			$query->where('display_name', 'like', '%' . $keyword . '%');
		}

		// 存储类型筛选（使用子查询，自动处理软删除）
		if (!empty($params['storage'])) {
			$attachmentModel = new AttachmentModel();
			$validIds = $attachmentModel->where('storage', $params['storage'])->column('id');
			if (!empty($validIds)) {
				$query->whereIn('attachment_id', $validIds);
			} else {
				$query->where('attachment_id', 0); // 没有匹配的文件
			}
		}

		// 文件扩展名筛选（使用子查询，自动处理软删除）
		if (!empty($params['extension'])) {
			$attachmentModel = new AttachmentModel();
			$validIds = $attachmentModel->where('extension', $params['extension'])->column('id');
			if (!empty($validIds)) {
				$query->whereIn('attachment_id', $validIds);
			} else {
				$query->where('attachment_id', 0); // 没有匹配的文件
			}
		}

		// 媒体类型筛选（使用子查询，自动处理软删除）
		if (!empty($params['media_type'])) {
			$attachmentModel = new AttachmentModel();

			switch ($params['media_type']) {
				case 'image':
					$attachmentModel->where('mime_type', 'like', 'image/%');
					break;
				case 'video':
					$attachmentModel->where('mime_type', 'like', 'video/%');
					break;
				case 'audio':
					$attachmentModel->where('mime_type', 'like', 'audio/%');
					break;
				case 'file':
					$attachmentModel->where('mime_type', 'not like', 'image/%')
					               ->where('mime_type', 'not like', 'video/%')
					               ->where('mime_type', 'not like', 'audio/%');
					break;
			}

			$validIds = $attachmentModel->column('id');
			if (!empty($validIds)) {
				$query->whereIn('attachment_id', $validIds);
			} else {
				$query->where('attachment_id', 0); // 没有匹配的文件
			}
		}

		// 排序
		$query->order('created_at', 'desc');

		// 分页
		$page  = $params['page'] ?? 1;
		$limit = $params['limit'] ?? 10;

		$result = $query->paginate([
			'page'      => $page,
			'list_rows' => $limit
		]);

		// 第二步：批量获取attachment信息（使用模型，自动处理软删除）
		$data = $result->toArray();
		if (!empty($data['data'])) {
			$attachmentIds = array_column($data['data'], 'attachment_id');
			$attachments = AttachmentModel::whereIn('id', $attachmentIds)
			                              ->column('*', 'id'); // 自动应用软删除

			// 合并数据
			$data['data'] = array_map(function($item) use ($attachments) {
				$attachment = $attachments[$item['attachment_id']] ?? [];
				return [
					'id' => $item['id'],
					'attachment_id' => $item['attachment_id'],
					'cate_id' => $item['cate_id'],
					'display_name' => $item['display_name'],
					'original_name' => $item['original_name'],
					'upload_source' => $item['upload_source'],
					'upload_time' => $item['created_at'],
					'created_at' => $item['created_at'],
					// 物理文件信息
					'name' => $attachment['name'] ?? '',
					'path' => $attachment['path'] ?? '',
					'url' => getImgUrl($attachment['path'] ?? ''),
					'size' => $attachment['size'] ?? 0,
					'extension' => $attachment['extension'] ?? '',
					'mime_type' => $attachment['mime_type'] ?? '',
					'storage' => $attachment['storage'] ?? '',
					'ref_count' => $attachment['ref_count'] ?? 0,
				];
			}, $data['data']);
		}

		return $data;
	}
	
	/**
	 * 检查用户是否有文件权限
	 */
	public static function checkUserFileAccess(int $fileId, int $userId): bool
	{
		$result = self::where('id', $fileId)
		              ->where('user_id', $userId)
		              ->find();
		
		return !is_null($result);
	}
	
	/**
	 * 创建用户文件关联
	 */
	public static function createUserFileAssociation(array $data): self
	{
		$model = new self();
		
		$res = $model->saveByCreate([
			'attachment_id' => $data['attachment_id'],
			'user_id'       => $data['user_id'],
			'cate_id'       => $data['cate_id'] ?? 0,
			'display_name'  => $data['display_name'],
			'original_name' => $data['original_name'],
			'upload_ip'     => $data['upload_ip'] ?? request()->ip(),
			'upload_source' => $data['upload_source'] ?? 'web',
		]);
		
		return $model;
	}
	
	/**
	 * 获取用户文件统计（使用分步骤查询优化，保持租户隔离）
	 */
	public static function getUserFileStats(): array
	{
		$userId = request()->adminId;

		// 第一步：获取用户文件ID列表（自动应用租户隔离和软删除）
		$userFileIds = self::where('user_id', $userId)->column('attachment_id');

		if (empty($userFileIds)) {
			return [
				'total_files' => 0,
				'total_size' => 0,
				'total_size_formatted' => '0B',
				'by_category' => [],
				'by_extension' => [],
			];
		}

		// 第二步：统计物理文件信息（使用模型，自动处理软删除）
		$attachmentStats = AttachmentModel::whereIn('id', $userFileIds)
		                                  ->field([
		                                  	'COUNT(*) as total_files',
		                                  	'COALESCE(SUM(size), 0) as total_size'
		                                  ])
		                                  ->find();

		$totalFiles = (int)($attachmentStats['total_files'] ?? 0);
		$totalSize = (int)($attachmentStats['total_size'] ?? 0);

		// 第三步：按分类统计（自动应用租户隔离和软删除）
		$categoryStats = self::where('user_id', $userId)
		                     ->field([
		                     	'cate_id',
		                     	'COUNT(*) as file_count'
		                     ])
		                     ->group('cate_id')
		                     ->order('file_count', 'desc')
		                     ->select()
		                     ->toArray();

		// 为每个分类计算大小
		foreach ($categoryStats as &$stat) {
			$categoryFileIds = self::where('user_id', $userId)
			                       ->where('cate_id', $stat['cate_id'])
			                       ->column('attachment_id');

			if (!empty($categoryFileIds)) {
				$stat['total_size'] = AttachmentModel::whereIn('id', $categoryFileIds)
				                                     ->sum('size');
			} else {
				$stat['total_size'] = 0;
			}

			// 格式化数据类型
			$stat['cate_id'] = (int)$stat['cate_id'];
			$stat['file_count'] = (int)$stat['file_count'];
			$stat['total_size'] = (int)$stat['total_size'];
		}

		// 第四步：按扩展名统计（使用模型，自动处理软删除）
		$extensionStats = AttachmentModel::whereIn('id', $userFileIds)
		                                 ->field([
		                                 	'COALESCE(extension, "unknown") as extension',
		                                 	'COUNT(*) as file_count',
		                                 	'COALESCE(SUM(size), 0) as total_size'
		                                 ])
		                                 ->group('extension')
		                                 ->order('file_count', 'desc')
		                                 ->select()
		                                 ->toArray();

		// 格式化扩展名统计数据
		$extensionStats = array_map(function($item) {
			return [
				'extension' => $item['extension'] ?? 'unknown',
				'file_count' => (int)$item['file_count'],
				'total_size' => (int)$item['total_size']
			];
		}, $extensionStats);

		return [
			'total_files'          => $totalFiles,
			'total_size'           => $totalSize,
			'total_size_formatted' => BitFormat::formatSizeAuto($totalSize),
			'by_category'          => $categoryStats,
			'by_extension'         => $extensionStats,
		];
	}

	/**
	 * 获取所有文件列表（管理员专用，使用子查询优化，保持租户隔离）
	 * 用于管理页面显示所有用户的文件
	 */
	public static function getAllFiles(array $params = []): array
	{
		// 第一步：构建基础查询（自动应用租户隔离和软删除）
		$query = self::query();

		// 分类筛选
		if (isset($params['cate_id']) && $params['cate_id'] >= 0) {
			$query->where('cate_id', $params['cate_id']);
		}

		// 未分类筛选
		if (!empty($params['is_uncategorized'])) {
			$query->where('cate_id', 0);
		}

		// 文件名搜索
		if (!empty($params['name']) || !empty($params['keyword'])) {
			$keyword = $params['name'] ?? $params['keyword'];
			$query->where('display_name', 'like', '%' . $keyword . '%');
		}

		// 存储类型筛选（使用子查询，自动处理软删除）
		if (!empty($params['storage'])) {
			$attachmentModel = new AttachmentModel();
			$validIds = $attachmentModel->where('storage', $params['storage'])->column('id');
			if (!empty($validIds)) {
				$query->whereIn('attachment_id', $validIds);
			} else {
				$query->where('attachment_id', 0); // 没有匹配的文件
			}
		}

		// 文件扩展名筛选（使用子查询，自动处理软删除）
		if (!empty($params['extension'])) {
			$attachmentModel = new AttachmentModel();
			$validIds = $attachmentModel->where('extension', $params['extension'])->column('id');
			if (!empty($validIds)) {
				$query->whereIn('attachment_id', $validIds);
			} else {
				$query->where('attachment_id', 0); // 没有匹配的文件
			}
		}

		// 媒体类型筛选（使用子查询，自动处理软删除）
		if (!empty($params['media_type'])) {
			$attachmentModel = new AttachmentModel();

			switch ($params['media_type']) {
				case 'image':
					$attachmentModel->where('mime_type', 'like', 'image/%');
					break;
				case 'video':
					$attachmentModel->where('mime_type', 'like', 'video/%');
					break;
				case 'audio':
					$attachmentModel->where('mime_type', 'like', 'audio/%');
					break;
				case 'file':
					$attachmentModel->where('mime_type', 'not like', 'image/%')
					               ->where('mime_type', 'not like', 'video/%')
					               ->where('mime_type', 'not like', 'audio/%');
					break;
			}

			$validIds = $attachmentModel->column('id');
			if (!empty($validIds)) {
				$query->whereIn('attachment_id', $validIds);
			} else {
				$query->where('attachment_id', 0); // 没有匹配的文件
			}
		}

		// 排序
		$query->order('created_at', 'desc');

		// 分页
		$page  = $params['page'] ?? 1;
		$limit = $params['limit'] ?? 10;

		$result = $query->paginate([
			'page'      => $page,
			'list_rows' => $limit
		]);

		// 第二步：批量获取相关信息
		$data = $result->toArray();
		if (!empty($data['data'])) {
			// 获取attachment信息
			$attachmentIds = array_column($data['data'], 'attachment_id');
			$attachments = AttachmentModel::whereIn('id', $attachmentIds)
			                              ->column('*', 'id'); // 自动应用软删除

			// 获取用户信息
			$userIds = array_unique(array_merge(
				array_column($data['data'], 'user_id'),
				array_column($data['data'], 'creator_id')
			));
			$userIds = array_filter($userIds); // 移除空值

			$users = [];
			if (!empty($userIds)) {
				$users = AdminModel::whereIn('id', $userIds)
				                   ->column('real_name,username', 'id');
			}

			// 合并数据
			$data['data'] = array_map(function($item) use ($attachments, $users) {
				$attachment = $attachments[$item['attachment_id']] ?? [];
				$user = $users[$item['user_id']] ?? [];
				$creator = $users[$item['creator_id']] ?? [];

				return [
					'id' => $item['id'],
					'attachment_id' => $item['attachment_id'],
					'cate_id' => $item['cate_id'],
					'display_name' => $item['display_name'],
					'original_name' => $item['original_name'],
					'upload_source' => $item['upload_source'],
					'upload_ip' => $item['upload_ip'] ?? '',
					'upload_time' => $item['created_at'],
					// 物理文件信息
					'name' => $attachment['name'] ?? '',
					'path' => $attachment['path'] ?? '',
					'url' => getImgUrl($attachment['path'] ?? ''),
					'size' => $attachment['size'] ?? 0,
					'extension' => $attachment['extension'] ?? '',
					'mime_type' => $attachment['mime_type'] ?? '',
					'storage' => $attachment['storage'] ?? '',
					'ref_count' => $attachment['ref_count'] ?? 0,
					// 用户信息
					'user_id' => $item['user_id'],
					'user_name' => $user['real_name'] ?? $user['username'] ?? '',
					'creator_id' => $item['creator_id'],
					'creator_name' => $creator['real_name'] ?? $creator['username'] ?? '',
				];
			}, $data['data']);
		}

		return $data;
	}

	/**
	 * 获取所有文件统计（管理员专用，使用分步骤查询优化，保持租户隔离）
	 */
	public static function getAllFileStats(): array
	{
		// 第一步：获取所有文件ID列表（自动应用租户隔离和软删除）
		$allFileIds = self::column('attachment_id');

		if (empty($allFileIds)) {
			return [
				'total_files' => 0,
				'total_size' => 0,
				'total_size_formatted' => '0B',
				'by_category' => [],
				'by_extension' => [],
				'by_storage' => [],
			];
		}

		// 第二步：统计物理文件信息（使用模型，自动处理软删除）
		$attachmentStats = AttachmentModel::whereIn('id', $allFileIds)
		                                  ->field([
		                                  	'COUNT(*) as total_files',
		                                  	'COALESCE(SUM(size), 0) as total_size'
		                                  ])
		                                  ->find();

		$totalFiles = (int)($attachmentStats['total_files'] ?? 0);
		$totalSize = (int)($attachmentStats['total_size'] ?? 0);

		// 第三步：按分类统计（自动应用租户隔离和软删除）
		$categoryStats = self::field([
		                     	'cate_id',
		                     	'COUNT(*) as file_count'
		                     ])
		                     ->group('cate_id')
		                     ->order('file_count', 'desc')
		                     ->select()
		                     ->toArray();

		// 为每个分类计算大小
		foreach ($categoryStats as &$stat) {
			$categoryFileIds = self::where('cate_id', $stat['cate_id'])
			                       ->column('attachment_id');

			if (!empty($categoryFileIds)) {
				$stat['total_size'] = AttachmentModel::whereIn('id', $categoryFileIds)
				                                     ->sum('size');
			} else {
				$stat['total_size'] = 0;
			}

			// 格式化数据类型
			$stat['cate_id'] = (int)$stat['cate_id'];
			$stat['file_count'] = (int)$stat['file_count'];
			$stat['total_size'] = (int)$stat['total_size'];
		}

		// 第四步：按扩展名统计（使用模型，自动处理软删除）
		$extensionStats = AttachmentModel::whereIn('id', $allFileIds)
		                                 ->field([
		                                 	'COALESCE(extension, "unknown") as extension',
		                                 	'COUNT(*) as file_count',
		                                 	'COALESCE(SUM(size), 0) as total_size'
		                                 ])
		                                 ->group('extension')
		                                 ->order('file_count', 'desc')
		                                 ->select()
		                                 ->toArray();

		// 第五步：按存储类型统计（使用模型，自动处理软删除）
		$storageStats = AttachmentModel::whereIn('id', $allFileIds)
		                               ->field([
		                               	'COALESCE(storage, "unknown") as storage',
		                               	'COUNT(*) as file_count',
		                               	'COALESCE(SUM(size), 0) as total_size'
		                               ])
		                               ->group('storage')
		                               ->order('file_count', 'desc')
		                               ->select()
		                               ->toArray();

		// 格式化扩展名统计数据
		$extensionStats = array_map(function($item) {
			return [
				'extension' => $item['extension'] ?? 'unknown',
				'file_count' => (int)$item['file_count'],
				'total_size' => (int)$item['total_size']
			];
		}, $extensionStats);

		// 格式化存储类型统计数据
		$storageStats = array_map(function($item) {
			return [
				'storage' => $item['storage'] ?? 'unknown',
				'file_count' => (int)$item['file_count'],
				'total_size' => (int)$item['total_size']
			];
		}, $storageStats);

		return [
			'total_files'          => $totalFiles,
			'total_size'           => $totalSize,
			'total_size_formatted' => BitFormat::formatSizeAuto($totalSize),
			'by_category'          => $categoryStats,
			'by_extension'         => $extensionStats,
			'by_storage'           => $storageStats,
		];
	}
	
	/**
	 * 批量删除用户文件
	 */
	public static function batchDeleteUserFiles(array $fileIds, int $userId): array
	{
		$successCount = 0;
		$failedCount  = 0;
		$failedIds    = [];
		$model        = new self();
		foreach ($fileIds as $fileId) {
			try {
				$userFile = $model->where('id', $fileId)
				                  ->where('user_id', $userId)
				                  ->find();
				
				if ($userFile) {
					$userFile->delete();
					$successCount++;
				}
				else {
					$failedCount++;
					$failedIds[] = $fileId;
				}
			}
			catch (\Exception $e) {
				$failedCount++;
				$failedIds[] = $fileId;
			}
		}
		
		return [
			'success_count' => $successCount,
			'failed_count'  => $failedCount,
			'failed_ids'    => $failedIds
		];
	}
}

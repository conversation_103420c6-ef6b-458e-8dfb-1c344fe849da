<?php
/**
 * 精确的控制器命名分析
 * 只分析app模块中controller目录内的控制器文件
 */

echo "=== 精确控制器命名分析 ===\n\n";

// 扫描app目录下的模块
$appDir = 'app';
$modules = [];

if (is_dir($appDir)) {
    $items = scandir($appDir);
    foreach ($items as $item) {
        if ($item == '.' || $item == '..') continue;
        
        $modulePath = $appDir . DIRECTORY_SEPARATOR . $item;
        if (is_dir($modulePath)) {
            // 检查是否有controller目录
            $controllerPath = $modulePath . DIRECTORY_SEPARATOR . 'controller';
            if (is_dir($controllerPath)) {
                $modules[$item] = $controllerPath;
            }
        }
    }
}

echo "1. 发现的模块及其controller目录:\n";
foreach ($modules as $module => $path) {
    echo "  - {$module}: {$path}\n";
}
echo "\n";

// 分析每个模块的控制器
function analyzeControllers($dir, $prefix = '') {
    $controllers = [];
    
    if (!is_dir($dir)) {
        return $controllers;
    }
    
    $items = scandir($dir);
    foreach ($items as $item) {
        if ($item == '.' || $item == '..') continue;
        
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        $relativePath = $prefix . $item;
        
        if (is_dir($path)) {
            // 递归扫描子目录
            $subControllers = analyzeControllers($path, $relativePath . '/');
            $controllers = array_merge($controllers, $subControllers);
        } elseif (is_file($path) && pathinfo($item, PATHINFO_EXTENSION) == 'php') {
            // 分析PHP文件
            $content = file_get_contents($path);
            if (preg_match('/class\s+(\w+)/', $content, $matches)) {
                $className = $matches[1];
                $controllers[] = [
                    'file' => $relativePath,
                    'path' => $path,
                    'class' => $className,
                    'hasController' => strpos($className, 'Controller') !== false,
                    'subdir' => $prefix ? rtrim($prefix, '/') : ''
                ];
            }
        }
    }
    
    return $controllers;
}

$allControllers = [];
$moduleStats = [];

echo "2. 各模块控制器分析:\n";
foreach ($modules as $module => $controllerPath) {
    $controllers = analyzeControllers($controllerPath);
    $allControllers[$module] = $controllers;
    
    $withController = array_filter($controllers, function($c) { return $c['hasController']; });
    $withoutController = array_filter($controllers, function($c) { return !$c['hasController']; });
    
    $moduleStats[$module] = [
        'total' => count($controllers),
        'with' => count($withController),
        'without' => count($withoutController)
    ];
    
    echo "  {$module}模块:\n";
    echo "    总计: " . count($controllers) . " 个控制器\n";
    echo "    带Controller后缀: " . count($withController) . " 个\n";
    echo "    不带Controller后缀: " . count($withoutController) . " 个\n";
    
    if (count($withoutController) > 0) {
        echo "    规范率: " . round(count($withController) / count($controllers) * 100, 1) . "%\n";
    } else {
        echo "    规范率: 100% ✅\n";
    }
    echo "\n";
}

echo "3. 需要重命名的控制器文件详细列表:\n";
$totalNeedRename = 0;

foreach ($allControllers as $module => $controllers) {
    $withoutController = array_filter($controllers, function($c) { return !$c['hasController']; });
    
    if (count($withoutController) > 0) {
        echo "  📁 {$module}模块 (" . count($withoutController) . "个需要重命名):\n";
        
        foreach ($withoutController as $controller) {
            $newClassName = $controller['class'] . 'Controller';
            $newFileName = str_replace('.php', 'Controller.php', basename($controller['file']));
            $subdir = $controller['subdir'] ? $controller['subdir'] . '/' : '';
            
            echo "    - {$subdir}{$controller['file']}\n";
            echo "      类名: {$controller['class']} → {$newClassName}\n";
            echo "      文件: " . basename($controller['file']) . " → {$newFileName}\n";
            echo "      完整路径: {$controller['path']}\n";
            echo "\n";
            
            $totalNeedRename++;
        }
    }
}

echo "4. 统计汇总:\n";
$totalControllers = 0;
$totalWithController = 0;
$totalWithoutController = 0;

foreach ($moduleStats as $module => $stats) {
    $totalControllers += $stats['total'];
    $totalWithController += $stats['with'];
    $totalWithoutController += $stats['without'];
}

echo "  总控制器数量: {$totalControllers} 个\n";
echo "  已规范(带Controller后缀): {$totalWithController} 个 (" . round($totalWithController / $totalControllers * 100, 1) . "%)\n";
echo "  需要重命名: {$totalWithoutController} 个 (" . round($totalWithoutController / $totalControllers * 100, 1) . "%)\n\n";

echo "5. 模块规范化程度排名:\n";
uasort($moduleStats, function($a, $b) {
    $rateA = $a['total'] > 0 ? $a['with'] / $a['total'] : 0;
    $rateB = $b['total'] > 0 ? $b['with'] / $b['total'] : 0;
    return $rateB <=> $rateA;
});

foreach ($moduleStats as $module => $stats) {
    $rate = $stats['total'] > 0 ? round($stats['with'] / $stats['total'] * 100, 1) : 0;
    $status = $rate == 100 ? '✅' : ($rate >= 50 ? '🟡' : '🔴');
    echo "  {$status} {$module}: {$rate}% ({$stats['with']}/{$stats['total']})\n";
}

echo "\n6. 工作量评估:\n";
echo "  需要重命名的文件: {$totalWithoutController} 个\n";
echo "  预估工作时间: " . ceil($totalWithoutController / 10) . " 小时 (按每小时处理10个文件计算)\n";
echo "  风险等级: 🟡 中等 (需要同步更新路由引用)\n\n";

echo "=== 分析完成 ===\n";

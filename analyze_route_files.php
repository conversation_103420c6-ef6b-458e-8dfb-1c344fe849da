<?php
/**
 * 分析需要修改的路由文件
 * 基于需要重命名的控制器，找出对应的路由文件和具体的修改内容
 */

echo "=== 路由文件修改分析 ===\n\n";

// 需要重命名的控制器列表
$controllersToRename = [
    // hr模块
    'app\\hr\\controller\\HrMonthlyStats' => 'app\\hr\\controller\\HrMonthlyStatsController',

    // system模块基础控制器
    'app\\system\\controller\\Attachment' => 'app\\system\\controller\\AttachmentController',
    'app\\system\\controller\\AttachmentCat' => 'app\\system\\controller\\AttachmentCatController',
    'app\\system\\controller\\Auth' => 'app\\system\\controller\\AuthController',
    'app\\system\\controller\\Config' => 'app\\system\\controller\\ConfigController',
    'app\\system\\controller\\Upload' => 'app\\system\\controller\\UploadController',

    // system模块日志控制器
    'app\\system\\controller\\log\\Login' => 'app\\system\\controller\\log\\LoginController',
    'app\\system\\controller\\log\\Operation' => 'app\\system\\controller\\log\\OperationController',

    // system模块权限控制器
    'app\\system\\controller\\permission\\Admin' => 'app\\system\\controller\\permission\\AdminController',
    'app\\system\\controller\\permission\\Department' => 'app\\system\\controller\\permission\\DepartmentController',
    'app\\system\\controller\\permission\\Menu' => 'app\\system\\controller\\permission\\MenuController',
    'app\\system\\controller\\permission\\Post' => 'app\\system\\controller\\permission\\PostController',
    'app\\system\\controller\\permission\\Role' => 'app\\system\\controller\\permission\\RoleController',

    // system模块租户控制器
    'app\\system\\controller\\tenant\\TenantConfig' => 'app\\system\\controller\\tenant\\TenantConfigController',
];

// 简化的控制器映射（用于匹配不同的引用格式）
$simpleControllerMap = [
    // hr模块
    'HrMonthlyStats' => 'HrMonthlyStatsController',

    // system模块
    'Attachment' => 'AttachmentController',
    'AttachmentCat' => 'AttachmentCatController',
    'Auth' => 'AuthController',
    'Config' => 'ConfigController',
    'Upload' => 'UploadController',
    'Login' => 'LoginController',
    'Operation' => 'OperationController',
    'Admin' => 'AdminController',
    'Department' => 'DepartmentController',
    'Menu' => 'MenuController',
    'Post' => 'PostController',
    'Role' => 'RoleController',
    'TenantConfig' => 'TenantConfigController',
];

// 扫描route目录
$routeDir = 'route';
$routeFiles = [];

if (is_dir($routeDir)) {
    $items = scandir($routeDir);
    foreach ($items as $item) {
        if ($item == '.' || $item == '..' || pathinfo($item, PATHINFO_EXTENSION) != 'php') {
            continue;
        }
        
        $routeFiles[] = $routeDir . DIRECTORY_SEPARATOR . $item;
    }
}

echo "1. 发现的路由文件数量: " . count($routeFiles) . " 个\n\n";

// 分析每个路由文件
$affectedRoutes = [];

foreach ($routeFiles as $routeFile) {
    $content = file_get_contents($routeFile);
    $fileName = basename($routeFile);
    $needsUpdate = false;
    $updates = [];
    
    // 检查是否包含需要修改的控制器引用
    foreach ($controllersToRename as $oldController => $newController) {
        // 查找各种可能的引用格式
        $patterns = [
            // 直接引用格式: 'app\system\controller\Auth@method'
            "/['\"]" . preg_quote($oldController, '/') . "@(\w+)['\"]/",
            // 带反斜杠的格式
            "/['\"]" . preg_quote(str_replace('\\', '\\\\', $oldController), '/') . "@(\w+)['\"]/",
        ];

        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
                $needsUpdate = true;
                foreach ($matches[0] as $index => $match) {
                    $method = $matches[1][$index][0];
                    $oldRef = trim($match[0], '"\'');
                    $newRef = str_replace($oldController, $newController, $oldRef);

                    $updates[] = [
                        'line_content' => trim($match[0]),
                        'old_ref' => $oldRef,
                        'new_ref' => $newRef,
                        'method' => $method,
                        'position' => $match[1]
                    ];
                }
            }
        }
    }

    // 检查简化格式的引用（如：$nameSpace . '\permission\Admin@method'）
    foreach ($simpleControllerMap as $oldClass => $newClass) {
        // 查找 $nameSpace . '\path\ClassName@method' 格式
        $patterns = [
            "/(\\\$nameSpace\s*\.\s*['\"][^'\"]*\\\\{$oldClass})(@\w+)['\"]/",
            "/(['\"][^'\"]*\\\\{$oldClass})(@\w+)['\"]/",
        ];

        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
                $needsUpdate = true;
                foreach ($matches[0] as $index => $match) {
                    $pathPart = $matches[1][$index][0];
                    $methodPart = $matches[2][$index][0];

                    $oldRef = $pathPart . $methodPart;
                    $newRef = str_replace($oldClass, $newClass, $oldRef);

                    $updates[] = [
                        'line_content' => trim($match[0]),
                        'old_ref' => $oldRef,
                        'new_ref' => $newRef,
                        'method' => substr($methodPart, 1), // 去掉@符号
                        'position' => $match[1]
                    ];
                }
            }
        }
    }
    
    if ($needsUpdate) {
        $affectedRoutes[$fileName] = [
            'file_path' => $routeFile,
            'updates' => $updates
        ];
    }
}

echo "2. 需要修改的路由文件分析:\n";
if (empty($affectedRoutes)) {
    echo "  ✅ 没有发现需要修改的路由文件\n\n";
} else {
    echo "  需要修改的路由文件数量: " . count($affectedRoutes) . " 个\n\n";
    
    foreach ($affectedRoutes as $fileName => $routeInfo) {
        echo "  📄 {$fileName}:\n";
        echo "    文件路径: {$routeInfo['file_path']}\n";
        echo "    需要修改的引用数量: " . count($routeInfo['updates']) . " 个\n";
        
        foreach ($routeInfo['updates'] as $index => $update) {
            echo "    \n";
            echo "    修改 " . ($index + 1) . ":\n";
            echo "      旧引用: {$update['old_ref']}\n";
            echo "      新引用: {$update['new_ref']}\n";
            echo "      方法: {$update['method']}\n";
        }
        echo "\n";
    }
}

// 详细分析每个路由文件的具体内容
echo "3. 路由文件详细修改内容:\n";
foreach ($affectedRoutes as $fileName => $routeInfo) {
    echo "  📄 {$fileName} 详细修改:\n";
    
    $content = file_get_contents($routeInfo['file_path']);
    $lines = explode("\n", $content);
    
    foreach ($routeInfo['updates'] as $update) {
        // 找到包含该引用的行
        foreach ($lines as $lineNum => $line) {
            if (strpos($line, $update['old_ref']) !== false) {
                $newLine = str_replace($update['old_ref'], $update['new_ref'], $line);
                echo "    第" . ($lineNum + 1) . "行:\n";
                echo "      修改前: " . trim($line) . "\n";
                echo "      修改后: " . trim($newLine) . "\n";
                echo "\n";
                break;
            }
        }
    }
}

// 生成修改脚本预览
echo "4. 批量修改脚本预览:\n";
if (!empty($affectedRoutes)) {
    echo "  以下是可以执行的批量修改命令:\n\n";
    
    foreach ($affectedRoutes as $fileName => $routeInfo) {
        echo "  # 修改 {$fileName}\n";
        foreach ($routeInfo['updates'] as $update) {
            $oldRef = addslashes($update['old_ref']);
            $newRef = addslashes($update['new_ref']);
            echo "  sed -i 's/{$oldRef}/{$newRef}/g' {$routeInfo['file_path']}\n";
        }
        echo "\n";
    }
} else {
    echo "  ✅ 无需执行批量修改\n";
}

echo "5. 修改工作量统计:\n";
$totalUpdates = 0;
foreach ($affectedRoutes as $routeInfo) {
    $totalUpdates += count($routeInfo['updates']);
}

echo "  需要修改的路由文件: " . count($affectedRoutes) . " 个\n";
echo "  需要修改的引用总数: {$totalUpdates} 个\n";
echo "  预估修改时间: " . ceil(count($affectedRoutes) * 5) . " 分钟\n\n";

echo "6. 修改建议:\n";
echo "  1. 先备份route目录\n";
echo "  2. 使用批量替换工具进行修改\n";
echo "  3. 修改完成后检查语法\n";
echo "  4. 测试路由是否正常工作\n\n";

echo "=== 分析完成 ===\n";

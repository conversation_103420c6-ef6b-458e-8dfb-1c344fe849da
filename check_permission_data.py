#!/usr/bin/env python3
"""
检查权限测试数据是否与新的权限标识匹配
"""
import os
import mysql.connector

# 设置环境变量
os.environ['MYSQL_HOST'] = '*************'
os.environ['MYSQL_PORT'] = '3306'
os.environ['MYSQL_USER'] = 'www_bs_com'
os.environ['MYSQL_PASSWORD'] = 'PdadjMXmNy8Pn9tj'
os.environ['MYSQL_DATABASE'] = 'www_bs_com'

def get_db_connection():
    return mysql.connector.connect(
        host=os.environ['MYSQL_HOST'],
        port=int(os.environ['MYSQL_PORT']),
        user=os.environ['MYSQL_USER'],
        password=os.environ['MYSQL_PASSWORD'],
        database=os.environ['MYSQL_DATABASE']
    )

def parse_permission_info(rule_name):
    """
    解析权限信息（使用v2.0优化后的逻辑）
    """
    # 分割类路径和方法名
    class_path, method = rule_name.split('@')
    
    # 分割类路径为命名空间部分
    parts = class_path.split('\\')
    
    # 提取模块名（app\模块\controller\...）
    module = parts[1].lower()  # 第二个部分是模块名
    
    # 提取控制器名（最后一个命名空间部分）
    controller_class = parts[-1]
    
    # 去掉Controller后缀，转换为小写
    controller = controller_class.replace('Controller', '').lower()
    
    # 处理子目录情况（如：permission/admin, log/login）
    if len(parts) > 4:
        # 有子目录，格式：app\system\controller\permission\AdminController
        sub_path = parts[3].lower()  # 子目录名
        controller = f"{sub_path}:{controller}"
    
    return f"{module}:{controller}:{method}"

def main():
    print("=== 检查权限测试数据 ===\n")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 1. 检查数据库中的权限标识
    print("1. 检查数据库中的权限标识:")
    cursor.execute("""
        SELECT id, parent_id, title, name, type 
        FROM system_menu 
        WHERE (name LIKE 'crm:%' OR name LIKE 'system:%' OR name LIKE 'project:%' OR name LIKE 'daily:%') 
        AND status = 1 AND deleted_at IS NULL
        ORDER BY name 
        LIMIT 20
    """)
    
    db_permissions = cursor.fetchall()
    print(f"  数据库中相关权限数量: {len(db_permissions)}")
    for perm in db_permissions[:10]:  # 显示前10个
        print(f"    {perm[3]} ({perm[2]})")
    if len(db_permissions) > 10:
        print(f"    ... 还有 {len(db_permissions) - 10} 个权限")
    
    # 2. 测试新的权限解析逻辑
    print("\n2. 测试新的权限解析逻辑:")
    test_routes = [
        'app\\crm\\controller\\CrmCustomerMyController@index',
        'app\\crm\\controller\\CrmLeadController@add',
        'app\\system\\controller\\AuthController@login',
        'app\\system\\controller\\permission\\AdminController@index',
        'app\\system\\controller\\log\\LoginController@index',
        'app\\project\\controller\\ProjectController@index',
        'app\\hr\\controller\\HrMonthlyStatsController@getEmployeeStats',
    ]
    
    parsed_permissions = []
    for route in test_routes:
        parsed = parse_permission_info(route)
        parsed_permissions.append(parsed)
        print(f"    {route}")
        print(f"    → {parsed}")
        
        # 检查是否在数据库中存在
        cursor.execute("SELECT COUNT(*) FROM system_menu WHERE name = %s AND status = 1 AND deleted_at IS NULL", (parsed,))
        exists = cursor.fetchone()[0] > 0
        status = "✅ 存在" if exists else "❌ 不存在"
        print(f"    {status}\n")
    
    # 3. 检查测试用户的权限分配
    print("3. 检查测试用户的权限分配:")
    test_users = [202, 203, 204]  # 销售经理、组长、员工
    
    for user_id in test_users:
        cursor.execute("""
            SELECT a.username, a.real_name, r.name as role_name 
            FROM system_admin a 
            LEFT JOIN system_admin_role ar ON a.id = ar.admin_id 
            LEFT JOIN system_role r ON ar.role_id = r.id 
            WHERE a.id = %s AND a.tenant_id = 1
        """, (user_id,))
        
        user = cursor.fetchone()
        if user:
            print(f"  用户: {user[1]} ({user[0]}) - {user[2]}")
            
            # 查询用户权限
            cursor.execute("""
                SELECT m.name 
                FROM system_admin_role ar
                LEFT JOIN system_role_menu rm ON ar.role_id = rm.role_id
                LEFT JOIN system_menu m ON rm.menu_id = m.id
                WHERE ar.admin_id = %s AND m.status = 1 AND m.deleted_at IS NULL
                ORDER BY m.name
            """, (user_id,))
            
            permissions = [row[0] for row in cursor.fetchall()]
            print(f"    权限数量: {len(permissions)} 个")
            
            # 检查是否有我们解析的权限
            matched_permissions = [p for p in parsed_permissions if p in permissions]
            print(f"    匹配的测试权限: {len(matched_permissions)} 个")
            for perm in matched_permissions:
                print(f"      ✅ {perm}")
            
            # 显示不匹配的权限
            unmatched_permissions = [p for p in parsed_permissions if p not in permissions]
            if unmatched_permissions:
                print(f"    不匹配的权限: {len(unmatched_permissions)} 个")
                for perm in unmatched_permissions:
                    print(f"      ❌ {perm}")
        else:
            print(f"  ❌ 用户ID {user_id} 不存在")
        print()
    
    # 4. 检查业务测试数据
    print("4. 检查业务测试数据:")
    
    # 检查客户数据
    cursor.execute("""
        SELECT COUNT(*) FROM crm_customer 
        WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209)
    """)
    customer_count = cursor.fetchone()[0]
    print(f"  CRM客户测试数据: {customer_count} 条")
    
    # 检查线索数据
    cursor.execute("""
        SELECT COUNT(*) FROM crm_lead 
        WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209)
    """)
    lead_count = cursor.fetchone()[0]
    print(f"  CRM线索测试数据: {lead_count} 条")
    
    # 检查项目数据
    cursor.execute("""
        SELECT COUNT(*) FROM project_project 
        WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209)
    """)
    project_count = cursor.fetchone()[0]
    print(f"  项目测试数据: {project_count} 条")
    
    # 检查报价数据
    cursor.execute("""
        SELECT COUNT(*) FROM daily_price_order 
        WHERE tenant_id = 1 AND creator_id IN (201,202,203,204,205,206,207,208,209)
    """)
    price_count = cursor.fetchone()[0]
    print(f"  每日报价测试数据: {price_count} 条")
    
    cursor.close()
    conn.close()
    
    print("\n=== 检查完成 ===")

if __name__ == "__main__":
    main()

<?php

namespace app\system\controller\permission;

use app\common\core\base\BaseAdminController;
use app\system\service\RoleService;
use think\facade\Cache;
use think\response\Json;

/**
 * 角色权限控制器
 */
class RoleController extends BaseAdminController
{
	
	/**
	 * @var RoleService
	 */
	private RoleService $service;
	
	public function initialize(): void
	{
		parent::initialize();
		
		$this->service = RoleService::getInstance();
	}
	
	/**
	 * 获取角色列表
	 */
	public function index(): Json
	{
		return $this->success('获取成功', $this->service->getList(input()));
	}
	
	/**
	 * 获取角色详情
	 */
	public function detail($id): Json
	{
		return $this->success('获取成功', $this->service->getDetail((int)$id));
	}
	
	/**
	 * 创建角色
	 */
	public function add(): Json
	{
		
		$result = $this->service->create($this->request->post());
		
		if (!$result) {
			return $this->error('创建失败');
		}
		
		// 清除角色缓存
		$this->clearRoleCache();
		
		return $this->success('创建成功');
	}
	
	/**
	 * 更新角色
	 */
	public function edit($id): Json
	{
		$result = $this->service->update((int)$id, $this->tenantId, input());
		
		if (!$result) {
			return $this->error('更新失败');
		}
		
		// 清除角色和菜单缓存
		$this->clearRoleCache();
		
		return $this->success('更新成功');
	}
	
	/**
	 * 删除角色
	 */
	public function delete(): Json
	{
		$result = $this->service->delete(input('id/d'));
		
		if (!$result) {
			return $this->error('删除失败');
		}
		// todo 对应用户的菜单缓存是否需更新？全局缓存统一处理，使用封装的缓存处理！
		// 清除角色和菜单缓存
		$this->clearRoleCache();
		
		return $this->success('删除成功');
	}
	
	/**
	 * 获取下拉
	 */
	public function options(): Json
	{
		return $this->success('获取成功', $this->service->getOptions([
			[
				'tenant_id',
				'=',
				$this->tenantId,
			],
			[
				'status',
				'=',
				1
			]
		]));
	}
	
	/**
	 * 清除角色和相关缓存
	 */
	private function clearRoleCache(): void
	{
		// 清除角色缓存
		Cache::tag('role')
		     ->clear();
		// 清除菜单缓存
		Cache::tag('menu')
		     ->clear();
	}
}

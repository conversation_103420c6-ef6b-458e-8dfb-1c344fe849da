# 按钮权限与数据权限测试计划

## 📋 项目概述

本测试计划为租户ID=1制定了完整的按钮权限和数据权限测试方案，涵盖system_menu、system_admin、system_dept、system_role四个核心表的权限测试。

## 🎯 测试目标

1. **按钮权限验证**：确保不同角色用户只能看到被授权的功能按钮
2. **数据权限验证**：验证5种数据权限范围的正确性（全部、本部门、本部门及以下、仅本人、自定义）
3. **租户隔离验证**：确保租户间的权限完全隔离
4. **权限联动验证**：验证按钮权限与数据权限的正确联动

## 📁 文档结构

```
docs/test_role/
├── README.md                           # 本文档
├── 01-测试数据准备方案.md               # 测试环境和数据准备
├── 02-按钮权限测试计划.md               # 按钮权限测试详细计划
├── 03-数据权限测试计划.md               # 数据权限测试详细计划
├── 04-权限测试执行方案.md               # 具体执行步骤和验证方法
└── sql/                                # SQL脚本目录
    ├── 01-create-test-data.sql         # 创建测试数据脚本
    ├── 02-role-menu-permissions.sql    # 角色菜单权限配置脚本
    └── 03-business-test-data.sql       # 业务测试数据脚本
```

## 🏢 测试环境设计

### 组织架构
```
总公司 (tenant_id=1)
├── 销售部 (dept_id=102)
│   ├── 销售一组 (dept_id=103)
│   └── 销售二组 (dept_id=104)
├── 技术部 (dept_id=105)
│   ├── 前端组 (dept_id=106)
│   └── 后端组 (dept_id=107)
└── 财务部 (dept_id=108)
```

### 测试角色
| 角色ID | 角色名称 | 数据权限 | 测试重点 |
|--------|----------|----------|----------|
| 101 | 租户超级管理员 | 全部数据(1) | 验证所有权限 |
| 102 | 部门经理 | 本部门及以下(3) | 验证层级权限 |
| 103 | 组长 | 本部门(2) | 验证部门权限 |
| 104 | 普通员工 | 仅本人(4) | 验证个人权限 |
| 105 | 自定义权限 | 自定义(5) | 验证自定义权限 |

### 测试账号
| 账号 | 密码 | 角色 | 部门 | 用途 |
|------|------|------|------|------|
| tenant_admin | password | 租户超级管理员 | 销售部 | 全权限测试 |
| sales_manager | password | 部门经理 | 销售部 | 部门管理权限测试 |
| sales_leader1 | password | 组长 | 销售一组 | 组长权限测试 |
| sales_staff1 | password | 普通员工 | 销售一组 | 员工权限测试 |
| tech_manager | password | 部门经理 | 技术部 | 跨部门权限测试 |
| custom_user | password | 自定义权限 | 技术部 | 自定义权限测试 |

## 🚀 快速开始

### 1. 环境准备
```bash
# 1. 备份现有数据（可选）
mysqldump -u用户名 -p 数据库名 system_admin system_role system_dept > backup.sql

# 2. 执行测试数据创建
mysql -u用户名 -p数据库名 < docs/test_role/sql/01-create-test-data.sql

# 3. 配置角色菜单权限
mysql -u用户名 -p数据库名 < docs/test_role/sql/02-role-menu-permissions.sql

# 4. 创建业务测试数据（可选）
mysql -u用户名 -p数据库名 < docs/test_role/sql/03-business-test-data.sql
```

### 2. 验证环境
```sql
-- 检查测试数据是否创建成功
SELECT '部门数据' as type, COUNT(*) as count FROM system_dept WHERE tenant_id = 1
UNION ALL
SELECT '角色数据', COUNT(*) FROM system_role WHERE tenant_id = 1
UNION ALL
SELECT '用户数据', COUNT(*) FROM system_admin WHERE tenant_id = 1
UNION ALL
SELECT '用户角色关联', COUNT(*) FROM system_admin_role WHERE tenant_id = 1
UNION ALL
SELECT '角色菜单权限', COUNT(*) FROM system_role_menu WHERE tenant_id = 1;
```

### 3. 开始测试
1. 使用不同测试账号登录系统
2. 按照测试计划逐一验证按钮权限
3. 验证数据权限范围的正确性
4. 记录测试结果和发现的问题

## 📊 功能模块权限分类

### 🔴 需要数据权限的模块
- **CRM客户管理**：我的客户、我的线索、联系人管理、合同管理、回款管理
- **项目管理**：项目列表、任务管理
- **每日报价**：报价单管理
- **工作流**：我的申请、我的审批

### 🟡 部分需要数据权限的模块
- **权限管理**：管理员管理（需要部门权限）、部门管理（需要层级权限）

### 🟢 不需要数据权限的模块
- **系统配置**：菜单管理、角色管理、岗位管理、租户管理
- **基础数据**：产品管理、产品分类、标签管理
- **日志模块**：登录日志、操作日志

## ✅ 测试检查清单

### 按钮权限测试
- [ ] 租户超级管理员：能看到所有配置的按钮
- [ ] 部门经理：能看到管理类按钮，无高危操作
- [ ] 组长：能看到基础操作按钮
- [ ] 普通员工：只能看到基础查看和操作按钮
- [ ] 自定义权限：只能看到特定配置的按钮组合

### 数据权限测试
- [ ] 全部数据权限：能看到租户内所有数据
- [ ] 本部门及以下权限：能看到本部门及下级部门数据
- [ ] 本部门权限：只能看到本部门数据
- [ ] 仅本人权限：只能看到自己创建的数据
- [ ] 自定义权限：只能看到指定部门数据

### 系统安全测试
- [ ] 跨租户隔离：不能看到其他租户数据
- [ ] 权限联动：按钮权限与数据权限正确联动
- [ ] 越权访问：无法通过直接URL访问无权限功能

## 🔍 常见问题排查

### 按钮权限问题
1. 检查角色菜单权限配置
2. 检查前端权限指令实现
3. 检查权限中间件配置

### 数据权限问题
1. 检查用户角色配置
2. 检查角色数据权限设置
3. 检查数据权限实现逻辑

### 租户隔离问题
1. 检查租户ID设置
2. 检查数据查询条件
3. 检查权限验证逻辑

## 📞 技术支持

如果在测试过程中遇到问题，请：

1. 查看详细的测试文档
2. 检查SQL脚本执行结果
3. 验证数据库数据是否正确
4. 查看系统日志和错误信息

## 📝 测试报告

测试完成后，请按照以下格式提交测试报告：

1. **测试概要**：测试时间、人员、环境、范围
2. **测试结果统计**：通过率、发现问题数量
3. **问题详情**：问题描述、严重程度、解决方案
4. **测试结论**：是否可以上线
5. **改进建议**：优化建议和改进方向

---

**注意事项**：
- 所有测试账号密码均为：`password`
- 测试数据都设置了 `tenant_id = 1`
- 请在测试环境中执行，避免影响生产数据
- 测试完成后可选择性清理测试数据

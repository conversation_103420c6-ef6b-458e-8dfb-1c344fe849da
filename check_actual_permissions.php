<?php
/**
 * 检查数据库中实际的权限标识格式
 */

require_once 'vendor/autoload.php';

echo "=== 检查数据库中实际的权限标识格式 ===\n\n";

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "1. 检查CRM相关权限标识:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'crm:%' AND status = 1 AND deleted_at IS NULL
        ORDER BY name
        LIMIT 20
    ");
    $stmt->execute();
    $crmPermissions = $stmt->fetchAll();
    
    foreach ($crmPermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n2. 检查System相关权限标识:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'system:%' AND status = 1 AND deleted_at IS NULL
        ORDER BY name
        LIMIT 20
    ");
    $stmt->execute();
    $systemPermissions = $stmt->fetchAll();
    
    foreach ($systemPermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n3. 检查Project相关权限标识:\n";
    $stmt = $pdo->prepare("
        SELECT name, title, type 
        FROM system_menu 
        WHERE name LIKE 'project:%' AND status = 1 AND deleted_at IS NULL
        ORDER BY name
        LIMIT 10
    ");
    $stmt->execute();
    $projectPermissions = $stmt->fetchAll();
    
    foreach ($projectPermissions as $perm) {
        $type = $perm['type'] == 1 ? '菜单' : '按钮';
        echo "  {$perm['name']} ({$perm['title']}) - {$type}\n";
    }
    
    echo "\n4. 分析权限标识格式:\n";
    
    // 分析CRM权限格式
    $crmCustomerPermissions = array_filter($crmPermissions, function($perm) {
        return strpos($perm['name'], 'crm_customer') !== false;
    });
    
    if ($crmCustomerPermissions) {
        echo "  CRM客户权限格式示例:\n";
        foreach (array_slice($crmCustomerPermissions, 0, 5) as $perm) {
            echo "    {$perm['name']}\n";
        }
    }
    
    // 分析System权限格式
    $systemAdminPermissions = array_filter($systemPermissions, function($perm) {
        return strpos($perm['name'], 'permission:admin') !== false;
    });
    
    if ($systemAdminPermissions) {
        echo "  System管理权限格式示例:\n";
        foreach (array_slice($systemAdminPermissions, 0, 5) as $perm) {
            echo "    {$perm['name']}\n";
        }
    }
    
    echo "\n5. 权限标识格式总结:\n";
    echo "  数据库中的权限标识格式似乎是: 模块:子模块_功能:操作\n";
    echo "  例如: crm:crm_customer_my:index\n";
    echo "  而不是: crm:crmcustomermy:index\n";
    
    echo "\n6. 建议的修复方案:\n";
    echo "  需要修改PermissionService.php中的parsePermissionInfo方法\n";
    echo "  使其生成正确的权限标识格式\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}

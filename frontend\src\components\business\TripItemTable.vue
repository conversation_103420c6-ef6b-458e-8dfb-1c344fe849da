<!--出差行程明细表格组件-->
<template>
  <div class="trip-item-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <ElButton type="primary" :disabled="readonly" @click="addItem" size="large">
          <ElIcon>
            <Plus />
          </ElIcon>
          添加行程
        </ElButton>
        <ElButton
          v-if="items.length > 0"
          type="danger"
          :disabled="readonly"
          @click="clearAll"
          size="large"
        >
          <ElIcon>
            <Delete />
          </ElIcon>
          清空
        </ElButton>
      </div>
      <div class="toolbar-right">
        <span class="item-count">共 {{ items.length }} 项</span>
      </div>
    </div>

    <!-- 移动端卡片列表 -->
    <div v-if="isMobile" class="mobile-card-list">
      <div
        v-for="(item, index) in items"
        :key="index"
        class="mobile-card"
      >
        <div class="card-header">
          <span class="card-index">{{ index + 1 }}</span>
          <ElButton v-if="!readonly" type="danger" size="small" text @click="removeItem(index)">
            <ElIcon>
              <Delete />
            </ElIcon>
          </ElButton>
        </div>

        <div class="card-content">
          <!-- 交通工具 -->
          <div class="form-row">
            <label class="form-label">交通工具</label>
            <ElSelect
              v-if="!readonly"
              v-model="item.transport_type"
              placeholder="请选择"
              size="large"
              style="width: 100%"
            >
              <ElOption label="飞机" :value="1" />
              <ElOption label="高铁" :value="2" />
              <ElOption label="火车" :value="3" />
              <ElOption label="汽车" :value="4" />
              <ElOption label="其他" :value="5" />
            </ElSelect>
            <span v-else class="readonly-value">{{ getTransportTypeText(item.transport_type) || '-' }}</span>
          </div>

          <!-- 单程往返 -->
          <div class="form-row">
            <label class="form-label">单程往返</label>
            <ElSelect
              v-if="!readonly"
              v-model="item.trip_mode"
              placeholder="请选择"
              size="large"
              style="width: 100%"
            >
              <ElOption label="往返" :value="1" />
              <ElOption label="单程" :value="2" />
            </ElSelect>
            <span v-else class="readonly-value">{{ getTripModeText(item.trip_mode) || '-' }}</span>
          </div>

          <!-- 出发地 -->
          <div class="form-row">
            <label class="form-label">出发地</label>
            <RegionSelector
              v-if="!readonly"
              v-model="item.departure_city_code"
              placeholder="请选择出发地"
              size="large"
              style="width: 100%"
              :level="2"
              return-type="array"
              @change="(value, selectedData) => handleRegionChange(item, 'departure', value, selectedData)"
            />
            <span v-else class="readonly-value">{{ item.departure_city || '-' }}</span>
          </div>

          <!-- 目的地 -->
          <div class="form-row">
            <label class="form-label">目的地</label>
            <RegionSelector
              v-if="!readonly"
              v-model="item.destination_city_code"
              placeholder="请选择目的地"
              size="large"
              style="width: 100%"
              :level="2"
              return-type="array"
              @change="(value, selectedData) => handleRegionChange(item, 'destination', value, selectedData)"
            />
            <span v-else class="readonly-value">{{ item.destination_city || '-' }}</span>
          </div>

          <!-- 开始时间 -->
          <div class="form-row">
            <label class="form-label">开始时间</label>
            <ElDatePicker
              v-if="!readonly"
              v-model="item.start_time"
              type="datetime"
              placeholder="请选择开始时间"
              size="large"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="calculateItemDuration(item)"
            />
            <span v-else class="readonly-value">{{ item.start_time || '-' }}</span>
          </div>

          <!-- 结束时间 -->
          <div class="form-row">
            <label class="form-label">结束时间</label>
            <ElDatePicker
              v-if="!readonly"
              v-model="item.end_time"
              type="datetime"
              placeholder="请选择结束时间"
              size="large"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="calculateItemDuration(item)"
            />
            <span v-else class="readonly-value">{{ item.end_time || '-' }}</span>
          </div>

          <!-- 时长 -->
          <div class="form-row">
            <label class="form-label">时长</label>
            <span class="duration-value">{{ item.duration || 0 }}小时</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 桌面端表格 -->
    <div v-else class="desktop-table">
      <ElTable :data="items" border size="default" style="width: 100%">
        <ElTableColumn label="交通工具" width="120">
          <template #default="{ row }">
            <ElSelect
              v-if="!readonly"
              v-model="row.transport_type"
              placeholder="请选择"
              size="default"
              style="width: 100%"
            >
              <ElOption label="飞机" :value="1" />
              <ElOption label="高铁" :value="2" />
              <ElOption label="火车" :value="3" />
              <ElOption label="汽车" :value="4" />
              <ElOption label="其他" :value="5" />
            </ElSelect>
            <span v-else>{{ getTransportTypeText(row.transport_type) || '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="单程往返" width="120">
          <template #default="{ row }">
            <ElSelect
              v-if="!readonly"
              v-model="row.trip_mode"
              placeholder="请选择"
              size="default"
              style="width: 100%"
            >
              <ElOption label="往返" :value="1" />
              <ElOption label="单程" :value="2" />
            </ElSelect>
            <span v-else>{{ getTripModeText(row.trip_mode) || '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="出发地" min-width="140">
          <template #default="{ row }">
            <RegionSelector
              v-if="!readonly"
              v-model="row.departure_city_code"
              placeholder="请选择出发地"
              size="default"
              style="width: 100%"
              :level="2"
              return-type="array"
              @change="(value, selectedData) => handleRegionChange(row, 'departure', value, selectedData)"
            />
            <span v-else>{{ row.departure_city || '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="目的地" min-width="140">
          <template #default="{ row }">
            <RegionSelector
              v-if="!readonly"
              v-model="row.destination_city_code"
              placeholder="请选择目的地"
              size="default"
              style="width: 100%"
              :level="2"
              return-type="array"
              @change="(value, selectedData) => handleRegionChange(row, 'destination', value, selectedData)"
            />
            <span v-else>{{ row.destination_city || '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="开始时间" min-width="180">
          <template #default="{ row }">
            <ElDatePicker
              v-if="!readonly"
              v-model="row.start_time"
              type="datetime"
              placeholder="请选择开始时间"
              size="default"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="calculateItemDuration(row)"
            />
            <span v-else>{{ row.start_time || '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="结束时间" min-width="180">
          <template #default="{ row }">
            <ElDatePicker
              v-if="!readonly"
              v-model="row.end_time"
              type="datetime"
              placeholder="请选择结束时间"
              size="default"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="calculateItemDuration(row)"
            />
            <span v-else>{{ row.end_time || '-' }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="时长" width="100">
          <template #default="{ row }">
            <span style="font-size: 14px; font-weight: 500">{{ row.duration || 0 }}小时</span>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="100" v-if="!readonly" fixed="right">
          <template #default="{ $index }">
            <ElButton type="danger" size="default" @click="removeItem($index)">删除</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>

    <!-- 总计信息 -->
    <div v-if="items.length > 0" class="summary-section">
      <div class="summary-item">
        <span class="summary-label">总小时数:</span>
        <span class="summary-value">{{ totalDuration }}小时</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ElButton,
  ElIcon,
  ElTable,
  ElTableColumn,
  ElSelect,
  ElOption,
  ElDatePicker
} from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import RegionSelector from '@/components/custom/RegionSelector/index.vue'
import { calculateWorkingHoursWithHalfHourRule } from '@/utils/date'

// 组件属性
interface Props {
  modelValue: any[]
  readonly?: boolean
  itemTemplate?: () => any
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  itemTemplate: () => ({
    transport_type: null,
    trip_mode: null,
    departure_city: '',
    destination_city: '',
    departure_city_code: [],
    destination_city_code: [],
    start_time: '',
    end_time: '',
    duration: 0
  })
})

// 事件定义
interface Emits {
  'update:modelValue': [value: any[]]
  change: [value: any[]]
}

const emit = defineEmits<Emits>()

// 响应式数据
const items = computed({
  get: () => props.modelValue || [],
  set: (value) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

// 检测移动端
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 计算总小时数
const totalDuration = computed(() => {
  return items.value.reduce((sum, item) => sum + (item.duration || 0), 0)
})

// 监听items变化，触发change事件
watch(items, (newItems) => {
  emit('change', newItems)
}, { deep: true })

// 方法
const addItem = () => {
  const newItem = props.itemTemplate()
  items.value = [...items.value, newItem]
}

const removeItem = (index: number) => {
  const newItems = [...items.value]
  newItems.splice(index, 1)
  items.value = newItems
}

const clearAll = () => {
  items.value = []
}

// 获取交通工具文本
const getTransportTypeText = (value: number) => {
  const options: Record<number, string> = {
    1: '飞机',
    2: '高铁',
    3: '火车',
    4: '汽车',
    5: '其他'
  }
  return options[value] || ''
}

// 获取单程往返文本
const getTripModeText = (value: number) => {
  const options: Record<number, string> = {
    1: '往返',
    2: '单程'
  }
  return options[value] || ''
}

// 处理地区选择变化
const handleRegionChange = (row: any, type: 'departure' | 'destination', value: any, selectedData: any) => {
  if (value && Array.isArray(value) && value.length >= 2 && selectedData) {
    if (type === 'departure') {
      row.departure_city = selectedData.city.name
    } else {
      row.destination_city = selectedData.city.name
    }
  }
}

// 计算行程时长
const calculateItemDuration = (item: any) => {
  if (item.start_time && item.end_time) {
    // 使用新的工作时间计算方法，与出差表单保持一致
    // 默认工作时间配置：08:00-12:00,14:00-18:00
    item.duration = calculateWorkingHoursWithHalfHourRule(
      item.start_time,
      item.end_time,
      '08:00-12:00,14:00-18:00'
    )
  } else {
    item.duration = 0
  }
  // 触发change事件，通知父组件重新计算总小时数
  emit('change', items.value)
}
</script>

<style lang="scss" scoped>
.trip-item-table {
  width: 100%;

  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .toolbar-left {
      display: flex;
      gap: 12px;
    }

    .toolbar-right {
      .item-count {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .mobile-card-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .mobile-card {
      border: 1px solid #e4e7ed;
      border-radius: 12px;
      padding: 16px;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;

        .card-index {
          font-size: 16px;
          font-weight: 600;
          color: #409eff;
          background: #ecf5ff;
          padding: 4px 12px;
          border-radius: 16px;
        }
      }

      .card-content {
        .form-row {
          display: flex;
          flex-direction: column;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .form-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
            font-weight: 500;
          }

          .readonly-value {
            font-size: 14px;
            color: #303133;
            padding: 8px 0;
          }

          .duration-value {
            font-size: 16px;
            font-weight: 600;
            color: #409eff;
            padding: 8px 0;
          }
        }
      }
    }
  }

  .desktop-table {
    :deep(.el-table) {
      width: 100%;
    }

    :deep(.el-table__body-wrapper) {
      overflow-x: auto;
    }
  }

  .summary-section {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    justify-content: flex-end;
    gap: 24px;

    .summary-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .summary-label {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }

      .summary-value {
        font-size: 16px;
        font-weight: 600;
        color: #409eff;
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .table-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .toolbar-left {
        justify-content: center;
      }

      .toolbar-right {
        text-align: center;
      }
    }
  }
}
</style>

# 系统部署指南

**版本**: v2.0  
**更新日期**: 2025-01-31  
**适用系统**: CRM管理系统

---

## 🎯 部署概述

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+, CentOS 8+)
- **Web服务器**: Nginx 1.18+
- **PHP版本**: PHP 8.1+
- **数据库**: MySQL 8.0+ / MariaDB 10.5+
- **缓存**: Redis 6.0+
- **内存**: 最低 4GB，推荐 8GB+
- **磁盘**: 最低 50GB，推荐 100GB+

### 部署架构
```
用户 → Nginx → PHP-FPM → ThinkPHP → MySQL/Redis
```

---

## 🛠️ 环境准备

### 1. 系统更新
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 2. 安装基础软件
```bash
# Ubuntu/Debian
sudo apt install -y curl wget git unzip software-properties-common

# CentOS/RHEL
sudo yum install -y curl wget git unzip epel-release
```

### 3. 安装PHP 8.1
```bash
# Ubuntu/Debian
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install -y php8.1 php8.1-fpm php8.1-mysql php8.1-redis \
    php8.1-curl php8.1-json php8.1-mbstring php8.1-xml php8.1-zip \
    php8.1-gd php8.1-intl php8.1-bcmath

# CentOS/RHEL
sudo yum install -y php81 php81-php-fpm php81-php-mysql php81-php-redis \
    php81-php-curl php81-php-json php81-php-mbstring php81-php-xml \
    php81-php-zip php81-php-gd php81-php-intl php81-php-bcmath
```

### 4. 安装MySQL 8.0
```bash
# Ubuntu/Debian
sudo apt install -y mysql-server-8.0

# CentOS/RHEL
sudo yum install -y mysql-server
sudo systemctl enable mysqld
sudo systemctl start mysqld
```

### 5. 安装Redis
```bash
# Ubuntu/Debian
sudo apt install -y redis-server

# CentOS/RHEL
sudo yum install -y redis
sudo systemctl enable redis
sudo systemctl start redis
```

### 6. 安装Nginx
```bash
# Ubuntu/Debian
sudo apt install -y nginx

# CentOS/RHEL
sudo yum install -y nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

---

## 📁 代码部署

### 1. 创建部署目录
```bash
sudo mkdir -p /var/www/crm_system
sudo chown -R www-data:www-data /var/www/crm_system
```

### 2. 下载代码
```bash
cd /var/www/crm_system
git clone https://github.com/your-repo/crm-system.git .
```

### 3. 安装Composer依赖
```bash
# 安装Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# 安装项目依赖
cd /var/www/crm_system
composer install --no-dev --optimize-autoloader
```

### 4. 设置目录权限
```bash
sudo chown -R www-data:www-data /var/www/crm_system
sudo chmod -R 755 /var/www/crm_system
sudo chmod -R 777 /var/www/crm_system/runtime
sudo chmod -R 777 /var/www/crm_system/public/uploads
```

---

## ⚙️ 配置文件

### 1. 数据库配置
```bash
# 复制配置文件
cp config/database.example.php config/database.php

# 编辑数据库配置
vim config/database.php
```

```php
<?php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'type' => 'mysql',
            'hostname' => '127.0.0.1',
            'database' => 'crm_system',
            'username' => 'crm_user',
            'password' => 'your_password',
            'hostport' => '3306',
            'charset' => 'utf8mb4',
            'prefix' => '',
        ],
    ],
];
```

### 2. Redis配置
```bash
# 编辑Redis配置
vim config/cache.php
```

```php
<?php
return [
    'default' => 'redis',
    'stores' => [
        'redis' => [
            'type' => 'redis',
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'select' => 0,
            'timeout' => 0,
            'expire' => 0,
            'persistent' => false,
            'prefix' => 'crm:',
        ],
    ],
];
```

### 3. 应用配置
```bash
# 编辑应用配置
vim config/app.php
```

```php
<?php
return [
    'app_debug' => false,
    'app_trace' => false,
    'default_timezone' => 'Asia/Shanghai',
    'app_key' => 'your_app_key_here',
    'jwt_secret' => 'your_jwt_secret_here',
];
```

---

## 🗄️ 数据库初始化

### 1. 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE crm_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'crm_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON crm_system.* TO 'crm_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 导入数据库结构
```bash
# 导入数据库结构
mysql -u crm_user -p crm_system < database/structure.sql

# 导入初始数据
mysql -u crm_user -p crm_system < database/data.sql
```

### 3. 运行数据库迁移
```bash
cd /var/www/crm_system
php think migrate:run
```

---

## 🌐 Nginx配置

### 1. 创建站点配置
```bash
sudo vim /etc/nginx/sites-available/crm_system
```

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/crm_system/public;
    index index.php index.html;

    # 日志配置
    access_log /var/log/nginx/crm_access.log;
    error_log /var/log/nginx/crm_error.log;

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # PHP处理
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 安全配置
        fastcgi_hide_header X-Powered-By;
        fastcgi_read_timeout 300;
    }

    # 安全配置
    location ~ /\. {
        deny all;
    }

    location ~ /(config|runtime|vendor)/ {
        deny all;
    }
}
```

### 2. 启用站点
```bash
sudo ln -s /etc/nginx/sites-available/crm_system /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🔒 SSL证书配置

### 1. 安装Certbot
```bash
# Ubuntu/Debian
sudo apt install -y certbot python3-certbot-nginx

# CentOS/RHEL
sudo yum install -y certbot python3-certbot-nginx
```

### 2. 获取SSL证书
```bash
sudo certbot --nginx -d your-domain.com
```

### 3. 自动续期
```bash
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 🚀 服务启动

### 1. 启动所有服务
```bash
sudo systemctl enable nginx php8.1-fpm mysql redis
sudo systemctl start nginx php8.1-fpm mysql redis
```

### 2. 检查服务状态
```bash
sudo systemctl status nginx
sudo systemctl status php8.1-fpm
sudo systemctl status mysql
sudo systemctl status redis
```

### 3. 验证部署
```bash
# 检查网站访问
curl -I http://your-domain.com

# 检查PHP
php -v

# 检查数据库连接
mysql -u crm_user -p -e "SELECT 1"

# 检查Redis连接
redis-cli ping
```

---

## 📊 性能优化

### 1. PHP-FPM优化
```bash
sudo vim /etc/php/8.1/fpm/pool.d/www.conf
```

```ini
[www]
user = www-data
group = www-data
listen = /var/run/php/php8.1-fpm.sock
listen.owner = www-data
listen.group = www-data

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

request_terminate_timeout = 300
```

### 2. MySQL优化
```bash
sudo vim /etc/mysql/mysql.conf.d/mysqld.cnf
```

```ini
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_type = 1
query_cache_size = 256M
max_connections = 200
```

### 3. Redis优化
```bash
sudo vim /etc/redis/redis.conf
```

```ini
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

---

## 🔧 维护脚本

### 1. 备份脚本
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/crm_system"
DB_NAME="crm_system"
DB_USER="crm_user"
DB_PASS="your_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# 备份代码
tar -czf $BACKUP_DIR/code_$DATE.tar.gz /var/www/crm_system

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 2. 监控脚本
```bash
#!/bin/bash
# monitor.sh

# 检查服务状态
services=("nginx" "php8.1-fpm" "mysql" "redis")

for service in "${services[@]}"; do
    if ! systemctl is-active --quiet $service; then
        echo "Service $service is down!"
        systemctl restart $service
    fi
done

# 检查磁盘空间
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is high: $DISK_USAGE%"
fi
```

---

## 📋 部署检查清单

### 部署前检查
- [ ] 服务器资源充足
- [ ] 域名DNS解析正确
- [ ] 防火墙端口开放
- [ ] SSL证书准备就绪

### 部署过程检查
- [ ] 代码下载完整
- [ ] 依赖安装成功
- [ ] 配置文件正确
- [ ] 数据库初始化完成
- [ ] 权限设置正确

### 部署后检查
- [ ] 网站正常访问
- [ ] 登录功能正常
- [ ] 数据库连接正常
- [ ] 缓存功能正常
- [ ] 日志记录正常

---

**部署完成后，请进行全面的功能测试，确保系统正常运行。**
